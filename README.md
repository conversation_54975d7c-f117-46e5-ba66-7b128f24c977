# Trello Clone - Frontend

A modern React application built with Vite and Material-UI, designed as a foundation for building a Trello-like project management tool.

## 🚀 Features

- **React 19** - Latest React with modern features
- **Vite** - Lightning fast build tool and development server
- **Material-UI (MUI)** - Complete React component library
- **Emotion** - CSS-in-JS styling solution
- **Roboto Font** - Google's signature font family for optimal readability
- **Dark/Light Mode** - Automatic system preference detection with manual toggle
- **Custom Theme** - Trello-inspired color palette and design
- **Responsive Design** - Mobile-first approach with MUI Grid system
- **Component Architecture** - Well-organized, reusable components

## 🛠️ Tech Stack

- **Frontend Framework**: React 19
- **Build Tool**: Vite 6.3.5
- **UI Library**: Material-UI 7.1.0
- **Styling**: Emotion (CSS-in-JS)
- **Typography**: Roboto Font Family
- **Icons**: Material-UI Icons
- **Development**: ESLint for code quality

## 📦 Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd trello-fe
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Start development server**

   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173`

## 🏗️ Project Structure

```text
src/
├── components/
│   ├── Navigation.jsx         # App navigation bar
│   ├── TrelloBoard.jsx        # Kanban board component
│   ├── TypographyShowcase.jsx # Roboto font demonstration
│   ├── ThemeDemo.jsx          # Enhanced dark/light mode demonstration
│   └── ThemeToggle.jsx        # Theme toggle button component
├── hooks/
│   └── useThemeMode.js        # Enhanced theme utilities hook
├── theme.js                   # MUI theme with colorSchemes and CSS variables
├── App.jsx                    # Main application component
└── main.jsx                  # Application entry point
```

## 🎨 Theme Customization

The application includes a custom MUI theme with Trello-inspired colors:

- **Primary**: #0079bf (Trello Blue)
- **Secondary**: #026aa7
- **Background**: #f4f5f7
- **Success**: #61bd4f
- **Warning**: #f2d600
- **Error**: #eb5a46

## 🔤 Typography & Fonts

The application uses **Roboto** as the primary font family with a dual loading strategy:

### Font Loading Strategy

1. **Google Fonts CDN**: Fast loading via CDN with `font-display: swap`
2. **Self-hosted fonts**: `@fontsource/roboto` package for offline support

### Font Weights Available

- **Light (300)**: For subtle text elements
- **Regular (400)**: Default body text
- **Medium (500)**: Emphasis and headings
- **Bold (700)**: Strong emphasis

### Typography Variants

All MUI typography variants are optimized for Roboto with proper font weights and line heights:

- Headings (h1-h6) use Medium weight (500)
- Body text uses Regular weight (400)
- Buttons and emphasis use Medium weight (500)

Visit the **Typography** tab in the application to see all variants in action.

## 🌙 Dark & Light Mode

The application features a comprehensive dark/light mode system using MUI's built-in `useColorScheme` hook with enhanced utilities:

### Core Features

- **MUI Integration**: Uses MUI's native `useColorScheme` hook for optimal performance
- **CSS Variables**: Automatic CSS custom properties for theme values
- **System Detection**: Respects system preference (`prefers-color-scheme`)
- **Manual Controls**: Multiple ways to switch themes
- **Persistent Storage**: Theme preference automatically saved by MUI
- **Smooth Transitions**: Animated theme switching with visual feedback
- **Complete Coverage**: All components adapt to the selected theme

### Enhanced Features

- **System Mode**: Automatic system preference following with `setSystemMode()`
- **Direct Setters**: `setLight()`, `setDark()`, `setSystemMode()` for precise control
- **Theme Utilities**: Pre-computed color values and CSS variables access
- **Performance Optimized**: CSS variables for faster theme switching
- **Advanced Demo**: Interactive theme playground with all controls

### Theme Toggle

- **Light Mode Icon**: Sun icon (☀️) when in light mode
- **Dark Mode Icon**: Moon icon (🌙) when in dark mode
- **Hover Animation**: 180° rotation on hover for visual feedback
- **Tooltip**: Shows current mode and switch action

### Color Adaptations

- **Backgrounds**: Light (#f4f5f7) ↔ Dark (#121212)
- **Surfaces**: White (#ffffff) ↔ Dark Gray (#1e1e1e)
- **Text**: Dark (#172b4d) ↔ Light (#ffffff)
- **Shadows**: Adjusted opacity for better contrast in each mode
- **Cards**: Enhanced shadows and backgrounds for dark mode

### Usage

**Basic useColorScheme:**

```javascript
import { useColorScheme } from '@mui/material'

const MyComponent = () => {
  const { mode, setMode } = useColorScheme()

  const toggleTheme = () => {
    setMode(mode === 'light' ? 'dark' : 'light')
  }

  return <Button onClick={toggleTheme}>Current mode: {mode}</Button>
}
```

**Enhanced useThemeMode Hook:**

```javascript
import { useThemeMode } from './hooks/useThemeMode'

const MyComponent = () => {
  const {
    mode,
    toggleMode,
    setLight,
    setDark,
    setSystemMode,
    colors,
    cssVars,
  } = useThemeMode()

  return (
    <Box>
      <Button onClick={toggleMode}>Toggle: {mode}</Button>
      <Button onClick={setLight}>Light</Button>
      <Button onClick={setDark}>Dark</Button>
      <Button onClick={setSystemMode}>System</Button>
    </Box>
  )
}
```

Visit the **Theme** tab in the application to see the dark/light mode demo.

## 🧩 Available Components

### Navigation

- Responsive app bar with navigation
- User profile menu
- Notifications dropdown
- Settings access

### Trello Board

- Kanban-style board layout
- Drag-and-drop ready structure
- Card management
- List organization
- Interactive dialogs

### Home View

- Welcome dashboard
- Feature showcase
- Interactive demo components
- Technology stack display

### Theme Demo (Enhanced)

- **Multiple Control Methods**: Toggle, direct setters, system mode
- **Color Palette Showcase**: Dynamic color display with hex values
- **Component Examples**: All MUI components in both themes
- **Background Demonstrations**: Surface and text color examples
- **CSS Variables Display**: Advanced developer information
- **System Mode Support**: Automatic system preference following
- **Real-time Updates**: Live theme switching with immediate feedback

### Theme Toggle Component

- **Smart Toggle**: Cycles between light/dark modes
- **Animated Icons**: Smooth rotation and color transitions
- **Enhanced Hook**: Uses custom `useThemeMode` for additional utilities
- **Performance Optimized**: Leverages CSS variables for fast switching

### useThemeMode Hook

- **Extended API**: Beyond basic `useColorScheme` functionality
- **Convenience Methods**: `toggleMode()`, `setLight()`, `setDark()`, `setSystemMode()`
- **Theme Utilities**: Pre-computed colors and CSS variables access
- **Type Safety**: Full TypeScript support (when using TypeScript)
- **Performance**: Optimized for frequent theme changes

## 📱 Responsive Design

The application is fully responsive with breakpoints:

- **xs**: 0px and up
- **sm**: 600px and up
- **md**: 900px and up
- **lg**: 1200px and up
- **xl**: 1536px and up

## 🚀 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Run ESLint and fix issues automatically
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check code formatting

## 🔧 Development

### Code Style

This project follows strict code style guidelines:

**ESLint Configuration:**

- **Single quotes** for strings (`'hello'` instead of `"hello"`)
- **No semicolons** at the end of statements
- **Single quotes for JSX** attributes (`<div className='example'>`)
- Consistent spacing and formatting

**Prettier Configuration:**

- 2-space indentation
- Single quotes everywhere
- No trailing semicolons
- 80-character line width
- Trailing commas in ES5-compatible locations

**Automatic Formatting:**

```bash
# Fix linting issues automatically
npm run lint:fix

# Format all files with Prettier
npm run format

# Check if files are properly formatted
npm run format:check
```

### Adding New Components

1. Create component in `src/components/`
2. Import and use in `App.jsx` or other components
3. Follow MUI theming conventions

### Customizing Theme

Edit `src/theme.js` to modify:

- Color palette
- Typography
- Component overrides
- Spacing and breakpoints

### State Management

Currently using React's built-in state management. For larger applications, consider:

- Redux Toolkit
- Zustand
- React Query for server state

## 🌟 Next Steps

This foundation is ready for building a full Trello clone. Consider adding:

1. **Backend Integration**

   - API client setup
   - Authentication
   - Data persistence

2. **Advanced Features**

   - Drag and drop functionality
   - Real-time updates
   - User management
   - Board sharing

3. **Performance Optimization**

   - Code splitting
   - Lazy loading
   - Memoization

4. **Testing**
   - Unit tests with Jest
   - Component tests with React Testing Library
   - E2E tests with Playwright

## 📄 License

This project is open source and available under the [MIT License](LICENSE).
