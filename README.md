# Trello Clone - Frontend

A modern React application built with Vite and Material-UI, designed as a foundation for building a Trello-like project management tool.

## 🚀 Features

- **React 19** - Latest React with modern features
- **Vite** - Lightning fast build tool and development server
- **Material-UI (MUI)** - Complete React component library
- **Emotion** - CSS-in-JS styling solution
- **Custom Theme** - Trello-inspired color palette and design
- **Responsive Design** - Mobile-first approach with MUI Grid system
- **Component Architecture** - Well-organized, reusable components

## 🛠️ Tech Stack

- **Frontend Framework**: React 19
- **Build Tool**: Vite 6.3.5
- **UI Library**: Material-UI 7.1.0
- **Styling**: Emotion (CSS-in-JS)
- **Icons**: Material-UI Icons
- **Development**: ESLint for code quality

## 📦 Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd trello-fe
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Start development server**

   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173`

## 🏗️ Project Structure

```text
src/
├── components/
│   ├── Navigation.jsx      # App navigation bar
│   └── TrelloBoard.jsx     # Kanban board component
├── theme.js                # MUI custom theme configuration
├── App.jsx                 # Main application component
└── main.jsx               # Application entry point
```

## 🎨 Theme Customization

The application includes a custom MUI theme with Trello-inspired colors:

- **Primary**: #0079bf (Trello Blue)
- **Secondary**: #026aa7
- **Background**: #f4f5f7
- **Success**: #61bd4f
- **Warning**: #f2d600
- **Error**: #eb5a46

## 🧩 Available Components

### Navigation

- Responsive app bar with navigation
- User profile menu
- Notifications dropdown
- Settings access

### Trello Board

- Kanban-style board layout
- Drag-and-drop ready structure
- Card management
- List organization
- Interactive dialogs

### Home View

- Welcome dashboard
- Feature showcase
- Interactive demo components
- Technology stack display

## 📱 Responsive Design

The application is fully responsive with breakpoints:

- **xs**: 0px and up
- **sm**: 600px and up
- **md**: 900px and up
- **lg**: 1200px and up
- **xl**: 1536px and up

## 🚀 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 🔧 Development

### Adding New Components

1. Create component in `src/components/`
2. Import and use in `App.jsx` or other components
3. Follow MUI theming conventions

### Customizing Theme

Edit `src/theme.js` to modify:

- Color palette
- Typography
- Component overrides
- Spacing and breakpoints

### State Management

Currently using React's built-in state management. For larger applications, consider:

- Redux Toolkit
- Zustand
- React Query for server state

## 🌟 Next Steps

This foundation is ready for building a full Trello clone. Consider adding:

1. **Backend Integration**

   - API client setup
   - Authentication
   - Data persistence

2. **Advanced Features**

   - Drag and drop functionality
   - Real-time updates
   - User management
   - Board sharing

3. **Performance Optimization**

   - Code splitting
   - Lazy loading
   - Memoization

4. **Testing**
   - Unit tests with Jest
   - Component tests with React Testing Library
   - E2E tests with Playwright

## 📄 License

This project is open source and available under the [MIT License](LICENSE).
