import { useState } from "react";
import {
  Container,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  <PERSON>ton,
  Box,
  Chip,
  Fab,
  Typography,
} from "@mui/material";
import { Add as AddIcon } from "@mui/icons-material";
import Navigation from "./components/Navigation";
import TrelloBoard from "./components/TrelloBoard";

function App() {
  const [count, setCount] = useState(0);
  const [currentView, setCurrentView] = useState("home");

  const handleViewChange = (view) => {
    setCurrentView(view);
  };

  const renderContent = () => {
    switch (currentView) {
      case "board":
        return <TrelloBoard />;
      case "home":
      default:
        return <HomeView count={count} setCount={setCount} />;
    }
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Navigation currentView={currentView} onViewChange={handleViewChange} />

      {renderContent()}

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{
          position: "fixed",
          bottom: 16,
          right: 16,
        }}
      >
        <AddIcon />
      </Fab>
    </Box>
  );
}

// Home View Component
const HomeView = ({ count, setCount }) => {
  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Grid container spacing={3}>
        {/* Welcome Card */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h4" component="h1" gutterBottom>
                Welcome to Your Trello Clone!
              </Typography>
              <Typography variant="body1" paragraph>
                This is a modern React application built with Vite and
                Material-UI. The setup includes a custom theme with
                Trello-inspired colors and styling.
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Chip label="React 19" color="primary" sx={{ mr: 1 }} />
                <Chip label="Vite" color="secondary" sx={{ mr: 1 }} />
                <Chip label="Material-UI" color="success" sx={{ mr: 1 }} />
                <Chip label="Emotion" color="info" />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Counter Demo Card */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h5" component="h2" gutterBottom>
                Interactive Demo
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Test the reactivity with this counter:
              </Typography>
              <Box sx={{ textAlign: "center", mt: 2 }}>
                <Typography variant="h3" color="primary" gutterBottom>
                  {count}
                </Typography>
                <Button
                  variant="contained"
                  onClick={() => setCount((count) => count + 1)}
                  sx={{ mr: 1 }}
                >
                  Increment
                </Button>
                <Button variant="outlined" onClick={() => setCount(0)}>
                  Reset
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Features Grid */}
        <Grid item xs={12}>
          <Typography variant="h5" component="h2" gutterBottom sx={{ mt: 2 }}>
            Features Included
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ height: "100%" }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    🎨 Custom Theme
                  </Typography>
                  <Typography variant="body2">
                    Trello-inspired color palette and typography
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ height: "100%" }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    ⚡ Vite Build Tool
                  </Typography>
                  <Typography variant="body2">
                    Lightning fast development and build process
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ height: "100%" }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    🧩 MUI Components
                  </Typography>
                  <Typography variant="body2">
                    Complete Material-UI component library
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ height: "100%" }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    🔧 Ready to Build
                  </Typography>
                  <Typography variant="body2">
                    Perfect foundation for your Trello clone
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Container>
  );
};

export default App;
