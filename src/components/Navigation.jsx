import { useState } from 'react'
import {
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  Box,
  Button,
  Tooltip,
} from '@mui/material'
import {
  Dashboard as DashboardIcon,
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  AccountCircle as AccountCircleIcon,
  Home as HomeIcon,
  ViewKanban as ViewKanbanIcon,
  TextFields as TextFieldsIcon,
} from '@mui/icons-material'

const Navigation = ({ currentView, onViewChange }) => {
  const [anchorEl, setAnchorEl] = useState(null)
  const [notificationAnchor, setNotificationAnchor] = useState(null)

  const handleProfileMenuOpen = event => {
    setAnchorEl(event.currentTarget)
  }

  const handleProfileMenuClose = () => {
    setAnchorEl(null)
  }

  const handleNotificationMenuOpen = event => {
    setNotificationAnchor(event.currentTarget)
  }

  const handleNotificationMenuClose = () => {
    setNotificationAnchor(null)
  }

  const isMenuOpen = Boolean(anchorEl)
  const isNotificationOpen = Boolean(notificationAnchor)

  return (
    <AppBar position='static' elevation={1}>
      <Toolbar>
        <DashboardIcon sx={{ mr: 2 }} />
        <Typography variant='h6' component='div' sx={{ flexGrow: 1 }}>
          Trello Clone
        </Typography>

        {/* Navigation Buttons */}
        <Box sx={{ display: 'flex', gap: 1, mr: 2 }}>
          <Button
            color='inherit'
            startIcon={<HomeIcon />}
            onClick={() => onViewChange('home')}
            variant={currentView === 'home' ? 'outlined' : 'text'}
            sx={{
              borderColor:
                currentView === 'home'
                  ? 'rgba(255,255,255,0.5)'
                  : 'transparent',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.1)',
              },
            }}
          >
            Home
          </Button>
          <Button
            color='inherit'
            startIcon={<ViewKanbanIcon />}
            onClick={() => onViewChange('board')}
            variant={currentView === 'board' ? 'outlined' : 'text'}
            sx={{
              borderColor:
                currentView === 'board'
                  ? 'rgba(255,255,255,0.5)'
                  : 'transparent',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.1)',
              },
            }}
          >
            Board
          </Button>
          <Button
            color='inherit'
            startIcon={<TextFieldsIcon />}
            onClick={() => onViewChange('typography')}
            variant={currentView === 'typography' ? 'outlined' : 'text'}
            sx={{
              borderColor:
                currentView === 'typography'
                  ? 'rgba(255,255,255,0.5)'
                  : 'transparent',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.1)',
              },
            }}
          >
            Typography
          </Button>
        </Box>

        {/* Right side icons */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Tooltip title='Notifications'>
            <IconButton
              color='inherit'
              onClick={handleNotificationMenuOpen}
              sx={{ position: 'relative' }}
            >
              <NotificationsIcon />
              {/* Notification badge */}
              <Box
                sx={{
                  position: 'absolute',
                  top: 8,
                  right: 8,
                  width: 8,
                  height: 8,
                  backgroundColor: 'error.main',
                  borderRadius: '50%',
                }}
              />
            </IconButton>
          </Tooltip>

          <Tooltip title='Settings'>
            <IconButton color='inherit'>
              <SettingsIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title='Profile'>
            <IconButton onClick={handleProfileMenuOpen} color='inherit'>
              <Avatar sx={{ width: 32, height: 32, bgcolor: 'secondary.main' }}>
                JD
              </Avatar>
            </IconButton>
          </Tooltip>
        </Box>

        {/* Profile Menu */}
        <Menu
          anchorEl={anchorEl}
          open={isMenuOpen}
          onClose={handleProfileMenuClose}
          onClick={handleProfileMenuClose}
          slotProps={{
            paper: {
              elevation: 3,
              sx: {
                mt: 1.5,
                minWidth: 200,
              },
            },
          }}
        >
          <MenuItem>
            <AccountCircleIcon sx={{ mr: 2 }} />
            Profile
          </MenuItem>
          <MenuItem>
            <SettingsIcon sx={{ mr: 2 }} />
            Settings
          </MenuItem>
          <MenuItem>Logout</MenuItem>
        </Menu>

        {/* Notification Menu */}
        <Menu
          anchorEl={notificationAnchor}
          open={isNotificationOpen}
          onClose={handleNotificationMenuClose}
          onClick={handleNotificationMenuClose}
          slotProps={{
            paper: {
              elevation: 3,
              sx: {
                mt: 1.5,
                minWidth: 300,
              },
            },
          }}
        >
          <MenuItem>
            <Box>
              <Typography variant='subtitle2'>New card added</Typography>
              <Typography variant='body2' color='text.secondary'>
                "Implement authentication" was added to In Progress
              </Typography>
            </Box>
          </MenuItem>
          <MenuItem>
            <Box>
              <Typography variant='subtitle2'>Card moved</Typography>
              <Typography variant='body2' color='text.secondary'>
                "Project setup" was moved to Done
              </Typography>
            </Box>
          </MenuItem>
          <MenuItem>
            <Box>
              <Typography variant='subtitle2'>Due date reminder</Typography>
              <Typography variant='body2' color='text.secondary'>
                "Design homepage" is due tomorrow
              </Typography>
            </Box>
          </MenuItem>
        </Menu>
      </Toolbar>
    </AppBar>
  )
}

export default Navigation
