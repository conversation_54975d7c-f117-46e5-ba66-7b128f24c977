import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  Paper,
  Switch,
  FormControlLabel,
  Alert,
  useTheme as useMuiTheme,
} from '@mui/material'
import {
  LightMode as LightModeIcon,
  DarkMode as DarkModeIcon,
  Palette as PaletteIcon,
} from '@mui/icons-material'
import { useTheme } from '../contexts/ThemeContext'

const ThemeDemo = () => {
  const { mode, toggleTheme } = useTheme()
  const muiTheme = useMuiTheme()

  const colorPalette = [
    { name: 'Primary', color: muiTheme.palette.primary.main },
    { name: 'Secondary', color: muiTheme.palette.secondary.main },
    { name: 'Success', color: muiTheme.palette.success.main },
    { name: 'Warning', color: muiTheme.palette.warning.main },
    { name: 'Error', color: muiTheme.palette.error.main },
    { name: 'Info', color: muiTheme.palette.info.main },
  ]

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <PaletteIcon sx={{ mr: 2, fontSize: 32 }} />
        <Typography variant='h3' gutterBottom>
          Dark & Light Mode Demo
        </Typography>
      </Box>

      <Typography variant='body1' paragraph>
        This application supports both light and dark themes with automatic
        system preference detection and manual toggle functionality.
      </Typography>

      {/* Theme Toggle Section */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant='h5' gutterBottom>
            Theme Controls
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={mode === 'dark'}
                  onChange={toggleTheme}
                  icon={<LightModeIcon fontSize='90' />}
                  checkedIcon={<DarkModeIcon />}
                />
              }
              label={`Current mode: ${mode}`}
            />
            <Button
              variant='outlined'
              onClick={toggleTheme}
              startIcon={
                mode === 'light' ? <DarkModeIcon /> : <LightModeIcon />
              }
            >
              Switch to {mode === 'light' ? 'Dark' : 'Light'} Mode
            </Button>
          </Box>
          <Alert severity='info'>
            Theme preference is automatically saved to localStorage and will
            persist across browser sessions.
          </Alert>
        </CardContent>
      </Card>

      {/* Color Palette */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant='h5' gutterBottom>
            Color Palette
          </Typography>
          <Grid container spacing={2}>
            {colorPalette.map(item => (
              <Grid item xs={6} sm={4} md={2} key={item.name}>
                <Paper
                  sx={{
                    p: 2,
                    textAlign: 'center',
                    backgroundColor: item.color,
                    color: muiTheme.palette.getContrastText(item.color),
                  }}
                >
                  <Typography variant='subtitle2'>{item.name}</Typography>
                  <Typography
                    variant='caption'
                    sx={{ fontFamily: 'monospace' }}
                  >
                    {item.color}
                  </Typography>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Component Examples */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant='h5' gutterBottom>
            Component Examples
          </Typography>
          <Grid container spacing={3}>
            {/* Buttons */}
            <Grid item xs={12} md={6}>
              <Typography variant='h6' gutterBottom>
                Buttons
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                <Button variant='contained' color='primary'>
                  Primary
                </Button>
                <Button variant='contained' color='secondary'>
                  Secondary
                </Button>
                <Button variant='outlined' color='primary'>
                  Outlined
                </Button>
                <Button variant='text' color='primary'>
                  Text
                </Button>
              </Box>
            </Grid>

            {/* Chips */}
            <Grid item xs={12} md={6}>
              <Typography variant='h6' gutterBottom>
                Chips
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                <Chip label='Default' />
                <Chip label='Primary' color='primary' />
                <Chip label='Secondary' color='secondary' />
                <Chip label='Success' color='success' />
                <Chip label='Warning' color='warning' />
                <Chip label='Error' color='error' />
              </Box>
            </Grid>

            {/* Alerts */}
            <Grid item xs={12}>
              <Typography variant='h6' gutterBottom>
                Alerts
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Alert severity='success'>
                    This is a success alert in {mode} mode!
                  </Alert>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Alert severity='warning'>
                    This is a warning alert in {mode} mode!
                  </Alert>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Alert severity='error'>
                    This is an error alert in {mode} mode!
                  </Alert>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Alert severity='info'>
                    This is an info alert in {mode} mode!
                  </Alert>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Background Examples */}
      <Card>
        <CardContent>
          <Typography variant='h5' gutterBottom>
            Background & Surface Examples
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Paper
                sx={{
                  p: 2,
                  backgroundColor: 'background.default',
                  border: 1,
                  borderColor: 'divider',
                }}
              >
                <Typography variant='subtitle2'>Default Background</Typography>
                <Typography variant='body2' color='text.secondary'>
                  {muiTheme.palette.background.default}
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Paper sx={{ p: 2 }}>
                <Typography variant='subtitle2'>Paper Surface</Typography>
                <Typography variant='body2' color='text.secondary'>
                  {muiTheme.palette.background.paper}
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Paper
                sx={{
                  p: 2,
                  backgroundColor: 'primary.main',
                  color: 'primary.contrastText',
                }}
              >
                <Typography variant='subtitle2'>Primary Surface</Typography>
                <Typography variant='body2' sx={{ opacity: 0.8 }}>
                  {muiTheme.palette.primary.main}
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Paper
                sx={{
                  p: 2,
                  backgroundColor: 'secondary.main',
                  color: 'secondary.contrastText',
                }}
              >
                <Typography variant='subtitle2'>Secondary Surface</Typography>
                <Typography variant='body2' sx={{ opacity: 0.8 }}>
                  {muiTheme.palette.secondary.main}
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  )
}

export default ThemeDemo
