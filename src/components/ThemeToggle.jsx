import { Icon<PERSON>utton, Tooltip, useTheme as useMuiTheme } from '@mui/material'
import {
  LightMode as LightModeIcon,
  DarkMode as DarkModeIcon,
} from '@mui/icons-material'
import { useTheme } from '../contexts/ThemeContext'

const ThemeToggle = () => {
  const { mode, toggleTheme } = useTheme()
  const muiTheme = useMuiTheme()

  return (
    <Tooltip title={`Switch to ${mode === 'light' ? 'dark' : 'light'} mode`}>
      <IconButton
        onClick={toggleTheme}
        color='inherit'
        sx={{
          transition: 'transform 0.3s ease-in-out',
          '&:hover': {
            transform: 'rotate(180deg)',
          },
        }}
      >
        {mode === 'light' ? (
          <DarkModeIcon />
        ) : (
          <LightModeIcon sx={{ color: muiTheme.palette.warning.main }} />
        )}
      </IconButton>
    </Tooltip>
  )
}

export default ThemeToggle
