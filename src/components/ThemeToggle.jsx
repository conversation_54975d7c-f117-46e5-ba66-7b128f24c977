import { Icon<PERSON>utton, Tooltip, useColorScheme, useTheme } from '@mui/material'
import {
  LightMode as LightModeIcon,
  DarkMode as DarkModeIcon,
} from '@mui/icons-material'

const ThemeToggle = () => {
  const { mode, setMode } = useColorScheme()
  const theme = useTheme()

  const handleToggle = () => {
    setMode(mode === 'light' ? 'dark' : 'light')
  }

  return (
    <Tooltip title={`Switch to ${mode === 'light' ? 'dark' : 'light'} mode`}>
      <IconButton
        onClick={handleToggle}
        color='inherit'
        sx={{
          transition: 'transform 0.3s ease-in-out',
          '&:hover': {
            transform: 'rotate(180deg)',
          },
        }}
      >
        {mode === 'light' ? (
          <DarkModeIcon />
        ) : (
          <LightModeIcon sx={{ color: theme.palette.warning.main }} />
        )}
      </IconButton>
    </Tooltip>
  )
}

export default ThemeToggle
