import { createTheme } from '@mui/material/styles';

// Create a custom theme for the Trello-like application
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#0079bf', // Trello blue
      light: '#4da6d9',
      dark: '#005582',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#026aa7',
      light: '#4da6d9',
      dark: '#004a7c',
      contrastText: '#ffffff',
    },
    background: {
      default: '#f4f5f7',
      paper: '#ffffff',
    },
    text: {
      primary: '#172b4d',
      secondary: '#5e6c84',
    },
    success: {
      main: '#61bd4f',
    },
    warning: {
      main: '#f2d600',
    },
    error: {
      main: '#eb5a46',
    },
    info: {
      main: '#00c2e0',
    },
  },
  typography: {
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      'Segoe UI',
      'Roboto',
      'Noto Sans',
      'Ubuntu',
      'Droid Sans',
      'Helvetica Neue',
      'sans-serif',
    ].join(','),
    h1: {
      fontSize: '2rem',
      fontWeight: 600,
      color: '#172b4d',
    },
    h2: {
      fontSize: '1.5rem',
      fontWeight: 600,
      color: '#172b4d',
    },
    h3: {
      fontSize: '1.25rem',
      fontWeight: 600,
      color: '#172b4d',
    },
    h4: {
      fontSize: '1.125rem',
      fontWeight: 600,
      color: '#172b4d',
    },
    h5: {
      fontSize: '1rem',
      fontWeight: 600,
      color: '#172b4d',
    },
    h6: {
      fontSize: '0.875rem',
      fontWeight: 600,
      color: '#172b4d',
    },
    body1: {
      fontSize: '0.875rem',
      color: '#172b4d',
    },
    body2: {
      fontSize: '0.75rem',
      color: '#5e6c84',
    },
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
          fontWeight: 500,
        },
        contained: {
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
          '&:hover': {
            boxShadow: '0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)',
          },
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          boxShadow: '0 1px 3px rgba(0,0,0,0.12)',
        },
      },
    },
  },
});

export default theme;
