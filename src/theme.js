import { createTheme } from '@mui/material/styles'

// Create a theme with color scheme support
const theme = createTheme({
  cssVariables: true,
  colorSchemes: {
    light: {
      palette: {
        primary: {
          main: '#0079bf', // Trello blue
          light: '#4da6d9',
          dark: '#005582',
          contrastText: '#ffffff',
        },
        secondary: {
          main: '#026aa7',
          light: '#4da6d9',
          dark: '#004a7c',
          contrastText: '#ffffff',
        },
        background: {
          default: '#f4f5f7',
          paper: '#ffffff',
        },
        text: {
          primary: '#172b4d',
          secondary: '#5e6c84',
        },
        success: {
          main: '#61bd4f',
        },
        warning: {
          main: '#f2d600',
        },
        error: {
          main: '#eb5a46',
        },
        info: {
          main: '#00c2e0',
        },
      },
    },
    dark: {
      palette: {
        primary: {
          main: '#0079bf', // Trello blue
          light: '#4da6d9',
          dark: '#005582',
          contrastText: '#ffffff',
        },
        secondary: {
          main: '#026aa7',
          light: '#4da6d9',
          dark: '#004a7c',
          contrastText: '#ffffff',
        },
        background: {
          default: '#121212',
          paper: '#1e1e1e',
        },
        text: {
          primary: '#ffffff',
          secondary: '#b3b3b3',
        },
        success: {
          main: '#61bd4f',
        },
        warning: {
          main: '#f2d600',
        },
        error: {
          main: '#eb5a46',
        },
        info: {
          main: '#00c2e0',
        },
      },
    },
  },
  typography: {
    fontFamily: [
      'Roboto',
      '-apple-system',
      'BlinkMacSystemFont',
      'Segoe UI',
      'Helvetica Neue',
      'Arial',
      'sans-serif',
    ].join(','),
    // Define font weights for Roboto
    fontWeightLight: 300,
    fontWeightRegular: 400,
    fontWeightMedium: 500,
    fontWeightBold: 700,

    h1: {
      fontSize: '2rem',
      fontWeight: 500, // Medium weight for better readability
      lineHeight: 1.2,
    },
    h2: {
      fontSize: '1.5rem',
      fontWeight: 500,
      lineHeight: 1.3,
    },
    h3: {
      fontSize: '1.25rem',
      fontWeight: 500,
      lineHeight: 1.4,
    },
    h4: {
      fontSize: '1.125rem',
      fontWeight: 500,
      lineHeight: 1.4,
    },
    h5: {
      fontSize: '1rem',
      fontWeight: 500,
      lineHeight: 1.5,
    },
    h6: {
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: 1.6,
    },
    body1: {
      fontSize: '1rem',
      fontWeight: 400,
      lineHeight: 1.5,
    },
    body2: {
      fontSize: '0.875rem',
      fontWeight: 400,
      lineHeight: 1.43,
    },
    subtitle1: {
      fontSize: '1rem',
      fontWeight: 500,
      lineHeight: 1.75,
    },
    subtitle2: {
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: 1.57,
    },
    caption: {
      fontSize: '0.75rem',
      fontWeight: 400,
      lineHeight: 1.66,
    },
    overline: {
      fontSize: '0.75rem',
      fontWeight: 500,
      lineHeight: 2.66,
      textTransform: 'uppercase',
      letterSpacing: '0.08333em',
    },
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
          fontWeight: 500, // Use Roboto medium weight
          fontFamily:
            'Roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif',
        },
        contained: {
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
          '&:hover': {
            boxShadow: '0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)',
          },
          // Dark mode shadows will be handled by CSS variables
          '[data-mui-color-scheme="dark"] &': {
            boxShadow: '0 1px 3px rgba(0,0,0,0.3), 0 1px 2px rgba(0,0,0,0.4)',
            '&:hover': {
              boxShadow: '0 3px 6px rgba(0,0,0,0.4), 0 3px 6px rgba(0,0,0,0.5)',
            },
          },
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          boxShadow: '0 1px 3px rgba(0,0,0,0.12)',
          '[data-mui-color-scheme="dark"] &': {
            boxShadow: '0 1px 3px rgba(0,0,0,0.3)',
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          '[data-mui-color-scheme="dark"] &': {
            backgroundImage: 'linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))',
          },
        },
      },
    },
  },
})

export default theme
