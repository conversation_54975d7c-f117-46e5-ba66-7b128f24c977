/* Global styles for MUI with useColorScheme */
:root {
  /* Let MUI handle color scheme detection */
  color-scheme: light dark;

  /* Optimize font rendering */
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Reset body to work with MUI */
body {
  margin: 0;
  min-height: 100vh;
  /* Let MUI CssBaseline handle the rest */
}

/* All other styles are handled by MUI components and theme */
