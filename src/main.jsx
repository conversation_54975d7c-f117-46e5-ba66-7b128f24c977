/* eslint-disable react-refresh/only-export-components */
import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { ThemeProvider } from '@mui/material/styles'
import CssBaseline from '@mui/material/CssBaseline'

// Import Roboto font
import '@fontsource/roboto/300.css'
import '@fontsource/roboto/400.css'
import '@fontsource/roboto/500.css'
import '@fontsource/roboto/700.css'

import createCustomTheme from './theme'
import { ThemeContextProvider, useTheme } from './contexts/ThemeContext'
import App from './App.jsx'

const AppTheme = () => {
  const { mode } = useTheme()
  const theme = createCustomTheme(mode)

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <App />
    </ThemeProvider>
  )
}

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <ThemeContextProvider>
      <AppTheme />
    </ThemeContextProvider>
  </StrictMode>
)
