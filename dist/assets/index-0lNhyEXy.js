var vS=Object.defineProperty;var bS=(n,r,o)=>r in n?vS(n,r,{enumerable:!0,configurable:!0,writable:!0,value:o}):n[r]=o;var Vi=(n,r,o)=>bS(n,typeof r!="symbol"?r+"":r,o);function SS(n,r){for(var o=0;o<r.length;o++){const l=r[o];if(typeof l!="string"&&!Array.isArray(l)){for(const u in l)if(u!=="default"&&!(u in n)){const c=Object.getOwnPropertyDescriptor(l,u);c&&Object.defineProperty(n,u,c.get?c:{enumerable:!0,get:()=>l[u]})}}}return Object.freeze(Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}))}(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const u of document.querySelectorAll('link[rel="modulepreload"]'))l(u);new MutationObserver(u=>{for(const c of u)if(c.type==="childList")for(const d of c.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&l(d)}).observe(document,{childList:!0,subtree:!0});function o(u){const c={};return u.integrity&&(c.integrity=u.integrity),u.referrerPolicy&&(c.referrerPolicy=u.referrerPolicy),u.crossOrigin==="use-credentials"?c.credentials="include":u.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function l(u){if(u.ep)return;u.ep=!0;const c=o(u);fetch(u.href,c)}})();function Uv(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var If={exports:{}},Yi={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ly;function xS(){if(ly)return Yi;ly=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function o(l,u,c){var d=null;if(c!==void 0&&(d=""+c),u.key!==void 0&&(d=""+u.key),"key"in u){c={};for(var p in u)p!=="key"&&(c[p]=u[p])}else c=u;return u=c.ref,{$$typeof:n,type:l,key:d,ref:u!==void 0?u:null,props:c}}return Yi.Fragment=r,Yi.jsx=o,Yi.jsxs=o,Yi}var sy;function CS(){return sy||(sy=1,If.exports=xS()),If.exports}var T=CS(),Wf={exports:{}},zt={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var uy;function TS(){if(uy)return zt;uy=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),c=Symbol.for("react.consumer"),d=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),h=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),S=Symbol.iterator;function E(z){return z===null||typeof z!="object"?null:(z=S&&z[S]||z["@@iterator"],typeof z=="function"?z:null)}var R={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},x=Object.assign,C={};function D(z,X,et){this.props=z,this.context=X,this.refs=C,this.updater=et||R}D.prototype.isReactComponent={},D.prototype.setState=function(z,X){if(typeof z!="object"&&typeof z!="function"&&z!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,z,X,"setState")},D.prototype.forceUpdate=function(z){this.updater.enqueueForceUpdate(this,z,"forceUpdate")};function j(){}j.prototype=D.prototype;function U(z,X,et){this.props=z,this.context=X,this.refs=C,this.updater=et||R}var M=U.prototype=new j;M.constructor=U,x(M,D.prototype),M.isPureReactComponent=!0;var O=Array.isArray,A={H:null,A:null,T:null,S:null,V:null},$=Object.prototype.hasOwnProperty;function H(z,X,et,nt,lt,ct){return et=ct.ref,{$$typeof:n,type:z,key:X,ref:et!==void 0?et:null,props:ct}}function P(z,X){return H(z.type,X,void 0,void 0,void 0,z.props)}function K(z){return typeof z=="object"&&z!==null&&z.$$typeof===n}function v(z){var X={"=":"=0",":":"=2"};return"$"+z.replace(/[=:]/g,function(et){return X[et]})}var _=/\/+/g;function W(z,X){return typeof z=="object"&&z!==null&&z.key!=null?v(""+z.key):X.toString(36)}function Z(){}function it(z){switch(z.status){case"fulfilled":return z.value;case"rejected":throw z.reason;default:switch(typeof z.status=="string"?z.then(Z,Z):(z.status="pending",z.then(function(X){z.status==="pending"&&(z.status="fulfilled",z.value=X)},function(X){z.status==="pending"&&(z.status="rejected",z.reason=X)})),z.status){case"fulfilled":return z.value;case"rejected":throw z.reason}}throw z}function F(z,X,et,nt,lt){var ct=typeof z;(ct==="undefined"||ct==="boolean")&&(z=null);var ft=!1;if(z===null)ft=!0;else switch(ct){case"bigint":case"string":case"number":ft=!0;break;case"object":switch(z.$$typeof){case n:case r:ft=!0;break;case y:return ft=z._init,F(ft(z._payload),X,et,nt,lt)}}if(ft)return lt=lt(z),ft=nt===""?"."+W(z,0):nt,O(lt)?(et="",ft!=null&&(et=ft.replace(_,"$&/")+"/"),F(lt,X,et,"",function(At){return At})):lt!=null&&(K(lt)&&(lt=P(lt,et+(lt.key==null||z&&z.key===lt.key?"":(""+lt.key).replace(_,"$&/")+"/")+ft)),X.push(lt)),1;ft=0;var Tt=nt===""?".":nt+":";if(O(z))for(var bt=0;bt<z.length;bt++)nt=z[bt],ct=Tt+W(nt,bt),ft+=F(nt,X,et,ct,lt);else if(bt=E(z),typeof bt=="function")for(z=bt.call(z),bt=0;!(nt=z.next()).done;)nt=nt.value,ct=Tt+W(nt,bt++),ft+=F(nt,X,et,ct,lt);else if(ct==="object"){if(typeof z.then=="function")return F(it(z),X,et,nt,lt);throw X=String(z),Error("Objects are not valid as a React child (found: "+(X==="[object Object]"?"object with keys {"+Object.keys(z).join(", ")+"}":X)+"). If you meant to render a collection of children, use an array instead.")}return ft}function B(z,X,et){if(z==null)return z;var nt=[],lt=0;return F(z,nt,"","",function(ct){return X.call(et,ct,lt++)}),nt}function V(z){if(z._status===-1){var X=z._result;X=X(),X.then(function(et){(z._status===0||z._status===-1)&&(z._status=1,z._result=et)},function(et){(z._status===0||z._status===-1)&&(z._status=2,z._result=et)}),z._status===-1&&(z._status=0,z._result=X)}if(z._status===1)return z._result.default;throw z._result}var ot=typeof reportError=="function"?reportError:function(z){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var X=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof z=="object"&&z!==null&&typeof z.message=="string"?String(z.message):String(z),error:z});if(!window.dispatchEvent(X))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",z);return}console.error(z)};function Q(){}return zt.Children={map:B,forEach:function(z,X,et){B(z,function(){X.apply(this,arguments)},et)},count:function(z){var X=0;return B(z,function(){X++}),X},toArray:function(z){return B(z,function(X){return X})||[]},only:function(z){if(!K(z))throw Error("React.Children.only expected to receive a single React element child.");return z}},zt.Component=D,zt.Fragment=o,zt.Profiler=u,zt.PureComponent=U,zt.StrictMode=l,zt.Suspense=h,zt.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=A,zt.__COMPILER_RUNTIME={__proto__:null,c:function(z){return A.H.useMemoCache(z)}},zt.cache=function(z){return function(){return z.apply(null,arguments)}},zt.cloneElement=function(z,X,et){if(z==null)throw Error("The argument must be a React element, but you passed "+z+".");var nt=x({},z.props),lt=z.key,ct=void 0;if(X!=null)for(ft in X.ref!==void 0&&(ct=void 0),X.key!==void 0&&(lt=""+X.key),X)!$.call(X,ft)||ft==="key"||ft==="__self"||ft==="__source"||ft==="ref"&&X.ref===void 0||(nt[ft]=X[ft]);var ft=arguments.length-2;if(ft===1)nt.children=et;else if(1<ft){for(var Tt=Array(ft),bt=0;bt<ft;bt++)Tt[bt]=arguments[bt+2];nt.children=Tt}return H(z.type,lt,void 0,void 0,ct,nt)},zt.createContext=function(z){return z={$$typeof:d,_currentValue:z,_currentValue2:z,_threadCount:0,Provider:null,Consumer:null},z.Provider=z,z.Consumer={$$typeof:c,_context:z},z},zt.createElement=function(z,X,et){var nt,lt={},ct=null;if(X!=null)for(nt in X.key!==void 0&&(ct=""+X.key),X)$.call(X,nt)&&nt!=="key"&&nt!=="__self"&&nt!=="__source"&&(lt[nt]=X[nt]);var ft=arguments.length-2;if(ft===1)lt.children=et;else if(1<ft){for(var Tt=Array(ft),bt=0;bt<ft;bt++)Tt[bt]=arguments[bt+2];lt.children=Tt}if(z&&z.defaultProps)for(nt in ft=z.defaultProps,ft)lt[nt]===void 0&&(lt[nt]=ft[nt]);return H(z,ct,void 0,void 0,null,lt)},zt.createRef=function(){return{current:null}},zt.forwardRef=function(z){return{$$typeof:p,render:z}},zt.isValidElement=K,zt.lazy=function(z){return{$$typeof:y,_payload:{_status:-1,_result:z},_init:V}},zt.memo=function(z,X){return{$$typeof:m,type:z,compare:X===void 0?null:X}},zt.startTransition=function(z){var X=A.T,et={};A.T=et;try{var nt=z(),lt=A.S;lt!==null&&lt(et,nt),typeof nt=="object"&&nt!==null&&typeof nt.then=="function"&&nt.then(Q,ot)}catch(ct){ot(ct)}finally{A.T=X}},zt.unstable_useCacheRefresh=function(){return A.H.useCacheRefresh()},zt.use=function(z){return A.H.use(z)},zt.useActionState=function(z,X,et){return A.H.useActionState(z,X,et)},zt.useCallback=function(z,X){return A.H.useCallback(z,X)},zt.useContext=function(z){return A.H.useContext(z)},zt.useDebugValue=function(){},zt.useDeferredValue=function(z,X){return A.H.useDeferredValue(z,X)},zt.useEffect=function(z,X,et){var nt=A.H;if(typeof et=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return nt.useEffect(z,X)},zt.useId=function(){return A.H.useId()},zt.useImperativeHandle=function(z,X,et){return A.H.useImperativeHandle(z,X,et)},zt.useInsertionEffect=function(z,X){return A.H.useInsertionEffect(z,X)},zt.useLayoutEffect=function(z,X){return A.H.useLayoutEffect(z,X)},zt.useMemo=function(z,X){return A.H.useMemo(z,X)},zt.useOptimistic=function(z,X){return A.H.useOptimistic(z,X)},zt.useReducer=function(z,X,et){return A.H.useReducer(z,X,et)},zt.useRef=function(z){return A.H.useRef(z)},zt.useState=function(z){return A.H.useState(z)},zt.useSyncExternalStore=function(z,X,et){return A.H.useSyncExternalStore(z,X,et)},zt.useTransition=function(){return A.H.useTransition()},zt.version="19.1.0",zt}var cy;function _d(){return cy||(cy=1,Wf.exports=TS()),Wf.exports}var w=_d();const oa=Uv(w),dd=SS({__proto__:null,default:oa},[w]);var Kf={exports:{}},Xi={},Qf={exports:{}},Zf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fy;function ES(){return fy||(fy=1,function(n){function r(B,V){var ot=B.length;B.push(V);t:for(;0<ot;){var Q=ot-1>>>1,z=B[Q];if(0<u(z,V))B[Q]=V,B[ot]=z,ot=Q;else break t}}function o(B){return B.length===0?null:B[0]}function l(B){if(B.length===0)return null;var V=B[0],ot=B.pop();if(ot!==V){B[0]=ot;t:for(var Q=0,z=B.length,X=z>>>1;Q<X;){var et=2*(Q+1)-1,nt=B[et],lt=et+1,ct=B[lt];if(0>u(nt,ot))lt<z&&0>u(ct,nt)?(B[Q]=ct,B[lt]=ot,Q=lt):(B[Q]=nt,B[et]=ot,Q=et);else if(lt<z&&0>u(ct,ot))B[Q]=ct,B[lt]=ot,Q=lt;else break t}}return V}function u(B,V){var ot=B.sortIndex-V.sortIndex;return ot!==0?ot:B.id-V.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var c=performance;n.unstable_now=function(){return c.now()}}else{var d=Date,p=d.now();n.unstable_now=function(){return d.now()-p}}var h=[],m=[],y=1,S=null,E=3,R=!1,x=!1,C=!1,D=!1,j=typeof setTimeout=="function"?setTimeout:null,U=typeof clearTimeout=="function"?clearTimeout:null,M=typeof setImmediate<"u"?setImmediate:null;function O(B){for(var V=o(m);V!==null;){if(V.callback===null)l(m);else if(V.startTime<=B)l(m),V.sortIndex=V.expirationTime,r(h,V);else break;V=o(m)}}function A(B){if(C=!1,O(B),!x)if(o(h)!==null)x=!0,$||($=!0,W());else{var V=o(m);V!==null&&F(A,V.startTime-B)}}var $=!1,H=-1,P=5,K=-1;function v(){return D?!0:!(n.unstable_now()-K<P)}function _(){if(D=!1,$){var B=n.unstable_now();K=B;var V=!0;try{t:{x=!1,C&&(C=!1,U(H),H=-1),R=!0;var ot=E;try{e:{for(O(B),S=o(h);S!==null&&!(S.expirationTime>B&&v());){var Q=S.callback;if(typeof Q=="function"){S.callback=null,E=S.priorityLevel;var z=Q(S.expirationTime<=B);if(B=n.unstable_now(),typeof z=="function"){S.callback=z,O(B),V=!0;break e}S===o(h)&&l(h),O(B)}else l(h);S=o(h)}if(S!==null)V=!0;else{var X=o(m);X!==null&&F(A,X.startTime-B),V=!1}}break t}finally{S=null,E=ot,R=!1}V=void 0}}finally{V?W():$=!1}}}var W;if(typeof M=="function")W=function(){M(_)};else if(typeof MessageChannel<"u"){var Z=new MessageChannel,it=Z.port2;Z.port1.onmessage=_,W=function(){it.postMessage(null)}}else W=function(){j(_,0)};function F(B,V){H=j(function(){B(n.unstable_now())},V)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(B){B.callback=null},n.unstable_forceFrameRate=function(B){0>B||125<B?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):P=0<B?Math.floor(1e3/B):5},n.unstable_getCurrentPriorityLevel=function(){return E},n.unstable_next=function(B){switch(E){case 1:case 2:case 3:var V=3;break;default:V=E}var ot=E;E=V;try{return B()}finally{E=ot}},n.unstable_requestPaint=function(){D=!0},n.unstable_runWithPriority=function(B,V){switch(B){case 1:case 2:case 3:case 4:case 5:break;default:B=3}var ot=E;E=B;try{return V()}finally{E=ot}},n.unstable_scheduleCallback=function(B,V,ot){var Q=n.unstable_now();switch(typeof ot=="object"&&ot!==null?(ot=ot.delay,ot=typeof ot=="number"&&0<ot?Q+ot:Q):ot=Q,B){case 1:var z=-1;break;case 2:z=250;break;case 5:z=1073741823;break;case 4:z=1e4;break;default:z=5e3}return z=ot+z,B={id:y++,callback:V,priorityLevel:B,startTime:ot,expirationTime:z,sortIndex:-1},ot>Q?(B.sortIndex=ot,r(m,B),o(h)===null&&B===o(m)&&(C?(U(H),H=-1):C=!0,F(A,ot-Q))):(B.sortIndex=z,r(h,B),x||R||(x=!0,$||($=!0,W()))),B},n.unstable_shouldYield=v,n.unstable_wrapCallback=function(B){var V=E;return function(){var ot=E;E=V;try{return B.apply(this,arguments)}finally{E=ot}}}}(Zf)),Zf}var dy;function RS(){return dy||(dy=1,Qf.exports=ES()),Qf.exports}var Ff={exports:{}},tn={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var py;function wS(){if(py)return tn;py=1;var n=_d();function r(h){var m="https://react.dev/errors/"+h;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var y=2;y<arguments.length;y++)m+="&args[]="+encodeURIComponent(arguments[y])}return"Minified React error #"+h+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(){}var l={d:{f:o,r:function(){throw Error(r(522))},D:o,C:o,L:o,m:o,X:o,S:o,M:o},p:0,findDOMNode:null},u=Symbol.for("react.portal");function c(h,m,y){var S=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:u,key:S==null?null:""+S,children:h,containerInfo:m,implementation:y}}var d=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function p(h,m){if(h==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return tn.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=l,tn.createPortal=function(h,m){var y=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(r(299));return c(h,m,null,y)},tn.flushSync=function(h){var m=d.T,y=l.p;try{if(d.T=null,l.p=2,h)return h()}finally{d.T=m,l.p=y,l.d.f()}},tn.preconnect=function(h,m){typeof h=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,l.d.C(h,m))},tn.prefetchDNS=function(h){typeof h=="string"&&l.d.D(h)},tn.preinit=function(h,m){if(typeof h=="string"&&m&&typeof m.as=="string"){var y=m.as,S=p(y,m.crossOrigin),E=typeof m.integrity=="string"?m.integrity:void 0,R=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;y==="style"?l.d.S(h,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:S,integrity:E,fetchPriority:R}):y==="script"&&l.d.X(h,{crossOrigin:S,integrity:E,fetchPriority:R,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},tn.preinitModule=function(h,m){if(typeof h=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var y=p(m.as,m.crossOrigin);l.d.M(h,{crossOrigin:y,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&l.d.M(h)},tn.preload=function(h,m){if(typeof h=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var y=m.as,S=p(y,m.crossOrigin);l.d.L(h,y,{crossOrigin:S,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},tn.preloadModule=function(h,m){if(typeof h=="string")if(m){var y=p(m.as,m.crossOrigin);l.d.m(h,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:y,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else l.d.m(h)},tn.requestFormReset=function(h){l.d.r(h)},tn.unstable_batchedUpdates=function(h,m){return h(m)},tn.useFormState=function(h,m,y){return d.H.useFormState(h,m,y)},tn.useFormStatus=function(){return d.H.useHostTransitionStatus()},tn.version="19.1.0",tn}var hy;function Lv(){if(hy)return Ff.exports;hy=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),Ff.exports=wS(),Ff.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var my;function AS(){if(my)return Xi;my=1;var n=RS(),r=_d(),o=Lv();function l(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)e+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function c(t){var e=t,a=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(a=e.return),t=e.return;while(t)}return e.tag===3?a:null}function d(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function p(t){if(c(t)!==t)throw Error(l(188))}function h(t){var e=t.alternate;if(!e){if(e=c(t),e===null)throw Error(l(188));return e!==t?null:t}for(var a=t,i=e;;){var s=a.return;if(s===null)break;var f=s.alternate;if(f===null){if(i=s.return,i!==null){a=i;continue}break}if(s.child===f.child){for(f=s.child;f;){if(f===a)return p(s),t;if(f===i)return p(s),e;f=f.sibling}throw Error(l(188))}if(a.return!==i.return)a=s,i=f;else{for(var g=!1,b=s.child;b;){if(b===a){g=!0,a=s,i=f;break}if(b===i){g=!0,i=s,a=f;break}b=b.sibling}if(!g){for(b=f.child;b;){if(b===a){g=!0,a=f,i=s;break}if(b===i){g=!0,i=f,a=s;break}b=b.sibling}if(!g)throw Error(l(189))}}if(a.alternate!==i)throw Error(l(190))}if(a.tag!==3)throw Error(l(188));return a.stateNode.current===a?t:e}function m(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=m(t),e!==null)return e;t=t.sibling}return null}var y=Object.assign,S=Symbol.for("react.element"),E=Symbol.for("react.transitional.element"),R=Symbol.for("react.portal"),x=Symbol.for("react.fragment"),C=Symbol.for("react.strict_mode"),D=Symbol.for("react.profiler"),j=Symbol.for("react.provider"),U=Symbol.for("react.consumer"),M=Symbol.for("react.context"),O=Symbol.for("react.forward_ref"),A=Symbol.for("react.suspense"),$=Symbol.for("react.suspense_list"),H=Symbol.for("react.memo"),P=Symbol.for("react.lazy"),K=Symbol.for("react.activity"),v=Symbol.for("react.memo_cache_sentinel"),_=Symbol.iterator;function W(t){return t===null||typeof t!="object"?null:(t=_&&t[_]||t["@@iterator"],typeof t=="function"?t:null)}var Z=Symbol.for("react.client.reference");function it(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===Z?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case x:return"Fragment";case D:return"Profiler";case C:return"StrictMode";case A:return"Suspense";case $:return"SuspenseList";case K:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case R:return"Portal";case M:return(t.displayName||"Context")+".Provider";case U:return(t._context.displayName||"Context")+".Consumer";case O:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case H:return e=t.displayName||null,e!==null?e:it(t.type)||"Memo";case P:e=t._payload,t=t._init;try{return it(t(e))}catch{}}return null}var F=Array.isArray,B=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,V=o.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ot={pending:!1,data:null,method:null,action:null},Q=[],z=-1;function X(t){return{current:t}}function et(t){0>z||(t.current=Q[z],Q[z]=null,z--)}function nt(t,e){z++,Q[z]=t.current,t.current=e}var lt=X(null),ct=X(null),ft=X(null),Tt=X(null);function bt(t,e){switch(nt(ft,e),nt(ct,t),nt(lt,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?kg(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=kg(e),t=$g(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}et(lt),nt(lt,t)}function At(){et(lt),et(ct),et(ft)}function yt(t){t.memoizedState!==null&&nt(Tt,t);var e=lt.current,a=$g(e,t.type);e!==a&&(nt(ct,t),nt(lt,a))}function wt(t){ct.current===t&&(et(lt),et(ct)),Tt.current===t&&(et(Tt),Li._currentValue=ot)}var St=Object.prototype.hasOwnProperty,$t=n.unstable_scheduleCallback,Et=n.unstable_cancelCallback,Vt=n.unstable_shouldYield,Te=n.unstable_requestPaint,Lt=n.unstable_now,Kt=n.unstable_getCurrentPriorityLevel,Qt=n.unstable_ImmediatePriority,Yt=n.unstable_UserBlockingPriority,_t=n.unstable_NormalPriority,dt=n.unstable_LowPriority,Ue=n.unstable_IdlePriority,le=n.log,Ze=n.unstable_setDisableYieldValue,Le=null,ne=null;function Ee(t){if(typeof le=="function"&&Ze(t),ne&&typeof ne.setStrictMode=="function")try{ne.setStrictMode(Le,t)}catch{}}var ae=Math.clz32?Math.clz32:we,se=Math.log,mt=Math.LN2;function we(t){return t>>>=0,t===0?32:31-(se(t)/mt|0)|0}var re=256,Zt=4194304;function gt(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Xt(t,e,a){var i=t.pendingLanes;if(i===0)return 0;var s=0,f=t.suspendedLanes,g=t.pingedLanes;t=t.warmLanes;var b=i&134217727;return b!==0?(i=b&~f,i!==0?s=gt(i):(g&=b,g!==0?s=gt(g):a||(a=b&~t,a!==0&&(s=gt(a))))):(b=i&~f,b!==0?s=gt(b):g!==0?s=gt(g):a||(a=i&~t,a!==0&&(s=gt(a)))),s===0?0:e!==0&&e!==s&&(e&f)===0&&(f=s&-s,a=e&-e,f>=a||f===32&&(a&4194048)!==0)?e:s}function be(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function mn(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ur(){var t=re;return re<<=1,(re&4194048)===0&&(re=256),t}function Ml(){var t=Zt;return Zt<<=1,(Zt&62914560)===0&&(Zt=4194304),t}function Wo(t){for(var e=[],a=0;31>a;a++)e.push(t);return e}function cr(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function _u(t,e,a,i,s,f){var g=t.pendingLanes;t.pendingLanes=a,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=a,t.entangledLanes&=a,t.errorRecoveryDisabledLanes&=a,t.shellSuspendCounter=0;var b=t.entanglements,N=t.expirationTimes,G=t.hiddenUpdates;for(a=g&~a;0<a;){var tt=31-ae(a),rt=1<<tt;b[tt]=0,N[tt]=-1;var Y=G[tt];if(Y!==null)for(G[tt]=null,tt=0;tt<Y.length;tt++){var I=Y[tt];I!==null&&(I.lane&=-536870913)}a&=~rt}i!==0&&zl(t,i,0),f!==0&&s===0&&t.tag!==0&&(t.suspendedLanes|=f&~(g&~e))}function zl(t,e,a){t.pendingLanes|=e,t.suspendedLanes&=~e;var i=31-ae(e);t.entangledLanes|=e,t.entanglements[i]=t.entanglements[i]|1073741824|a&4194090}function Dl(t,e){var a=t.entangledLanes|=e;for(t=t.entanglements;a;){var i=31-ae(a),s=1<<i;s&e|t[i]&e&&(t[i]|=e),a&=~s}}function Ko(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function Ot(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function on(){var t=V.p;return t!==0?t:(t=window.event,t===void 0?32:ey(t.type))}function Bl(t,e){var a=V.p;try{return V.p=t,e()}finally{V.p=a}}var ja=Math.random().toString(36).slice(2),Fe="__reactFiber$"+ja,ln="__reactProps$"+ja,$r="__reactContainer$"+ja,Uu="__reactEvents$"+ja,sb="__reactListeners$"+ja,ub="__reactHandles$"+ja,Tp="__reactResources$"+ja,Qo="__reactMarker$"+ja;function Lu(t){delete t[Fe],delete t[ln],delete t[Uu],delete t[sb],delete t[ub]}function _r(t){var e=t[Fe];if(e)return e;for(var a=t.parentNode;a;){if(e=a[$r]||a[Fe]){if(a=e.alternate,e.child!==null||a!==null&&a.child!==null)for(t=Hg(t);t!==null;){if(a=t[Fe])return a;t=Hg(t)}return e}t=a,a=t.parentNode}return null}function Ur(t){if(t=t[Fe]||t[$r]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Zo(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(l(33))}function Lr(t){var e=t[Tp];return e||(e=t[Tp]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Ge(t){t[Qo]=!0}var Ep=new Set,Rp={};function fr(t,e){Hr(t,e),Hr(t+"Capture",e)}function Hr(t,e){for(Rp[t]=e,t=0;t<e.length;t++)Ep.add(e[t])}var cb=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),wp={},Ap={};function fb(t){return St.call(Ap,t)?!0:St.call(wp,t)?!1:cb.test(t)?Ap[t]=!0:(wp[t]=!0,!1)}function Nl(t,e,a){if(fb(e))if(a===null)t.removeAttribute(e);else{switch(typeof a){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var i=e.toLowerCase().slice(0,5);if(i!=="data-"&&i!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+a)}}function jl(t,e,a){if(a===null)t.removeAttribute(e);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+a)}}function fa(t,e,a,i){if(i===null)t.removeAttribute(a);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(a);return}t.setAttributeNS(e,a,""+i)}}var Hu,Op;function qr(t){if(Hu===void 0)try{throw Error()}catch(a){var e=a.stack.trim().match(/\n( *(at )?)/);Hu=e&&e[1]||"",Op=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Hu+t+Op}var qu=!1;function Pu(t,e){if(!t||qu)return"";qu=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var i={DetermineComponentFrameRoot:function(){try{if(e){var rt=function(){throw Error()};if(Object.defineProperty(rt.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(rt,[])}catch(I){var Y=I}Reflect.construct(t,[],rt)}else{try{rt.call()}catch(I){Y=I}t.call(rt.prototype)}}else{try{throw Error()}catch(I){Y=I}(rt=t())&&typeof rt.catch=="function"&&rt.catch(function(){})}}catch(I){if(I&&Y&&typeof I.stack=="string")return[I.stack,Y.stack]}return[null,null]}};i.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var s=Object.getOwnPropertyDescriptor(i.DetermineComponentFrameRoot,"name");s&&s.configurable&&Object.defineProperty(i.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var f=i.DetermineComponentFrameRoot(),g=f[0],b=f[1];if(g&&b){var N=g.split(`
`),G=b.split(`
`);for(s=i=0;i<N.length&&!N[i].includes("DetermineComponentFrameRoot");)i++;for(;s<G.length&&!G[s].includes("DetermineComponentFrameRoot");)s++;if(i===N.length||s===G.length)for(i=N.length-1,s=G.length-1;1<=i&&0<=s&&N[i]!==G[s];)s--;for(;1<=i&&0<=s;i--,s--)if(N[i]!==G[s]){if(i!==1||s!==1)do if(i--,s--,0>s||N[i]!==G[s]){var tt=`
`+N[i].replace(" at new "," at ");return t.displayName&&tt.includes("<anonymous>")&&(tt=tt.replace("<anonymous>",t.displayName)),tt}while(1<=i&&0<=s);break}}}finally{qu=!1,Error.prepareStackTrace=a}return(a=t?t.displayName||t.name:"")?qr(a):""}function db(t){switch(t.tag){case 26:case 27:case 5:return qr(t.type);case 16:return qr("Lazy");case 13:return qr("Suspense");case 19:return qr("SuspenseList");case 0:case 15:return Pu(t.type,!1);case 11:return Pu(t.type.render,!1);case 1:return Pu(t.type,!0);case 31:return qr("Activity");default:return""}}function Mp(t){try{var e="";do e+=db(t),t=t.return;while(t);return e}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function zn(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function zp(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function pb(t){var e=zp(t)?"checked":"value",a=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),i=""+t[e];if(!t.hasOwnProperty(e)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var s=a.get,f=a.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return s.call(this)},set:function(g){i=""+g,f.call(this,g)}}),Object.defineProperty(t,e,{enumerable:a.enumerable}),{getValue:function(){return i},setValue:function(g){i=""+g},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function kl(t){t._valueTracker||(t._valueTracker=pb(t))}function Dp(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var a=e.getValue(),i="";return t&&(i=zp(t)?t.checked?"true":"false":t.value),t=i,t!==a?(e.setValue(t),!0):!1}function $l(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var hb=/[\n"\\]/g;function Dn(t){return t.replace(hb,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Gu(t,e,a,i,s,f,g,b){t.name="",g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"?t.type=g:t.removeAttribute("type"),e!=null?g==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+zn(e)):t.value!==""+zn(e)&&(t.value=""+zn(e)):g!=="submit"&&g!=="reset"||t.removeAttribute("value"),e!=null?Vu(t,g,zn(e)):a!=null?Vu(t,g,zn(a)):i!=null&&t.removeAttribute("value"),s==null&&f!=null&&(t.defaultChecked=!!f),s!=null&&(t.checked=s&&typeof s!="function"&&typeof s!="symbol"),b!=null&&typeof b!="function"&&typeof b!="symbol"&&typeof b!="boolean"?t.name=""+zn(b):t.removeAttribute("name")}function Bp(t,e,a,i,s,f,g,b){if(f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(t.type=f),e!=null||a!=null){if(!(f!=="submit"&&f!=="reset"||e!=null))return;a=a!=null?""+zn(a):"",e=e!=null?""+zn(e):a,b||e===t.value||(t.value=e),t.defaultValue=e}i=i??s,i=typeof i!="function"&&typeof i!="symbol"&&!!i,t.checked=b?t.checked:!!i,t.defaultChecked=!!i,g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"&&(t.name=g)}function Vu(t,e,a){e==="number"&&$l(t.ownerDocument)===t||t.defaultValue===""+a||(t.defaultValue=""+a)}function Pr(t,e,a,i){if(t=t.options,e){e={};for(var s=0;s<a.length;s++)e["$"+a[s]]=!0;for(a=0;a<t.length;a++)s=e.hasOwnProperty("$"+t[a].value),t[a].selected!==s&&(t[a].selected=s),s&&i&&(t[a].defaultSelected=!0)}else{for(a=""+zn(a),e=null,s=0;s<t.length;s++){if(t[s].value===a){t[s].selected=!0,i&&(t[s].defaultSelected=!0);return}e!==null||t[s].disabled||(e=t[s])}e!==null&&(e.selected=!0)}}function Np(t,e,a){if(e!=null&&(e=""+zn(e),e!==t.value&&(t.value=e),a==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=a!=null?""+zn(a):""}function jp(t,e,a,i){if(e==null){if(i!=null){if(a!=null)throw Error(l(92));if(F(i)){if(1<i.length)throw Error(l(93));i=i[0]}a=i}a==null&&(a=""),e=a}a=zn(e),t.defaultValue=a,i=t.textContent,i===a&&i!==""&&i!==null&&(t.value=i)}function Gr(t,e){if(e){var a=t.firstChild;if(a&&a===t.lastChild&&a.nodeType===3){a.nodeValue=e;return}}t.textContent=e}var mb=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function kp(t,e,a){var i=e.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?i?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":i?t.setProperty(e,a):typeof a!="number"||a===0||mb.has(e)?e==="float"?t.cssFloat=a:t[e]=(""+a).trim():t[e]=a+"px"}function $p(t,e,a){if(e!=null&&typeof e!="object")throw Error(l(62));if(t=t.style,a!=null){for(var i in a)!a.hasOwnProperty(i)||e!=null&&e.hasOwnProperty(i)||(i.indexOf("--")===0?t.setProperty(i,""):i==="float"?t.cssFloat="":t[i]="");for(var s in e)i=e[s],e.hasOwnProperty(s)&&a[s]!==i&&kp(t,s,i)}else for(var f in e)e.hasOwnProperty(f)&&kp(t,f,e[f])}function Yu(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var gb=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),yb=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function _l(t){return yb.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Xu=null;function Iu(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Vr=null,Yr=null;function _p(t){var e=Ur(t);if(e&&(t=e.stateNode)){var a=t[ln]||null;t:switch(t=e.stateNode,e.type){case"input":if(Gu(t,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),e=a.name,a.type==="radio"&&e!=null){for(a=t;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+Dn(""+e)+'"][type="radio"]'),e=0;e<a.length;e++){var i=a[e];if(i!==t&&i.form===t.form){var s=i[ln]||null;if(!s)throw Error(l(90));Gu(i,s.value,s.defaultValue,s.defaultValue,s.checked,s.defaultChecked,s.type,s.name)}}for(e=0;e<a.length;e++)i=a[e],i.form===t.form&&Dp(i)}break t;case"textarea":Np(t,a.value,a.defaultValue);break t;case"select":e=a.value,e!=null&&Pr(t,!!a.multiple,e,!1)}}}var Wu=!1;function Up(t,e,a){if(Wu)return t(e,a);Wu=!0;try{var i=t(e);return i}finally{if(Wu=!1,(Vr!==null||Yr!==null)&&(Cs(),Vr&&(e=Vr,t=Yr,Yr=Vr=null,_p(e),t)))for(e=0;e<t.length;e++)_p(t[e])}}function Fo(t,e){var a=t.stateNode;if(a===null)return null;var i=a[ln]||null;if(i===null)return null;a=i[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(t=t.type,i=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!i;break t;default:t=!1}if(t)return null;if(a&&typeof a!="function")throw Error(l(231,e,typeof a));return a}var da=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ku=!1;if(da)try{var Jo={};Object.defineProperty(Jo,"passive",{get:function(){Ku=!0}}),window.addEventListener("test",Jo,Jo),window.removeEventListener("test",Jo,Jo)}catch{Ku=!1}var ka=null,Qu=null,Ul=null;function Lp(){if(Ul)return Ul;var t,e=Qu,a=e.length,i,s="value"in ka?ka.value:ka.textContent,f=s.length;for(t=0;t<a&&e[t]===s[t];t++);var g=a-t;for(i=1;i<=g&&e[a-i]===s[f-i];i++);return Ul=s.slice(t,1<i?1-i:void 0)}function Ll(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Hl(){return!0}function Hp(){return!1}function sn(t){function e(a,i,s,f,g){this._reactName=a,this._targetInst=s,this.type=i,this.nativeEvent=f,this.target=g,this.currentTarget=null;for(var b in t)t.hasOwnProperty(b)&&(a=t[b],this[b]=a?a(f):f[b]);return this.isDefaultPrevented=(f.defaultPrevented!=null?f.defaultPrevented:f.returnValue===!1)?Hl:Hp,this.isPropagationStopped=Hp,this}return y(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=Hl)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=Hl)},persist:function(){},isPersistent:Hl}),e}var dr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ql=sn(dr),ti=y({},dr,{view:0,detail:0}),vb=sn(ti),Zu,Fu,ei,Pl=y({},ti,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:tc,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==ei&&(ei&&t.type==="mousemove"?(Zu=t.screenX-ei.screenX,Fu=t.screenY-ei.screenY):Fu=Zu=0,ei=t),Zu)},movementY:function(t){return"movementY"in t?t.movementY:Fu}}),qp=sn(Pl),bb=y({},Pl,{dataTransfer:0}),Sb=sn(bb),xb=y({},ti,{relatedTarget:0}),Ju=sn(xb),Cb=y({},dr,{animationName:0,elapsedTime:0,pseudoElement:0}),Tb=sn(Cb),Eb=y({},dr,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Rb=sn(Eb),wb=y({},dr,{data:0}),Pp=sn(wb),Ab={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Ob={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Mb={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function zb(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Mb[t])?!!e[t]:!1}function tc(){return zb}var Db=y({},ti,{key:function(t){if(t.key){var e=Ab[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Ll(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Ob[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:tc,charCode:function(t){return t.type==="keypress"?Ll(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Ll(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Bb=sn(Db),Nb=y({},Pl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Gp=sn(Nb),jb=y({},ti,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:tc}),kb=sn(jb),$b=y({},dr,{propertyName:0,elapsedTime:0,pseudoElement:0}),_b=sn($b),Ub=y({},Pl,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Lb=sn(Ub),Hb=y({},dr,{newState:0,oldState:0}),qb=sn(Hb),Pb=[9,13,27,32],ec=da&&"CompositionEvent"in window,ni=null;da&&"documentMode"in document&&(ni=document.documentMode);var Gb=da&&"TextEvent"in window&&!ni,Vp=da&&(!ec||ni&&8<ni&&11>=ni),Yp=" ",Xp=!1;function Ip(t,e){switch(t){case"keyup":return Pb.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Wp(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Xr=!1;function Vb(t,e){switch(t){case"compositionend":return Wp(e);case"keypress":return e.which!==32?null:(Xp=!0,Yp);case"textInput":return t=e.data,t===Yp&&Xp?null:t;default:return null}}function Yb(t,e){if(Xr)return t==="compositionend"||!ec&&Ip(t,e)?(t=Lp(),Ul=Qu=ka=null,Xr=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Vp&&e.locale!=="ko"?null:e.data;default:return null}}var Xb={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Kp(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Xb[t.type]:e==="textarea"}function Qp(t,e,a,i){Vr?Yr?Yr.push(i):Yr=[i]:Vr=i,e=Os(e,"onChange"),0<e.length&&(a=new ql("onChange","change",null,a,i),t.push({event:a,listeners:e}))}var ai=null,ri=null;function Ib(t){zg(t,0)}function Gl(t){var e=Zo(t);if(Dp(e))return t}function Zp(t,e){if(t==="change")return e}var Fp=!1;if(da){var nc;if(da){var ac="oninput"in document;if(!ac){var Jp=document.createElement("div");Jp.setAttribute("oninput","return;"),ac=typeof Jp.oninput=="function"}nc=ac}else nc=!1;Fp=nc&&(!document.documentMode||9<document.documentMode)}function th(){ai&&(ai.detachEvent("onpropertychange",eh),ri=ai=null)}function eh(t){if(t.propertyName==="value"&&Gl(ri)){var e=[];Qp(e,ri,t,Iu(t)),Up(Ib,e)}}function Wb(t,e,a){t==="focusin"?(th(),ai=e,ri=a,ai.attachEvent("onpropertychange",eh)):t==="focusout"&&th()}function Kb(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Gl(ri)}function Qb(t,e){if(t==="click")return Gl(e)}function Zb(t,e){if(t==="input"||t==="change")return Gl(e)}function Fb(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var gn=typeof Object.is=="function"?Object.is:Fb;function oi(t,e){if(gn(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var a=Object.keys(t),i=Object.keys(e);if(a.length!==i.length)return!1;for(i=0;i<a.length;i++){var s=a[i];if(!St.call(e,s)||!gn(t[s],e[s]))return!1}return!0}function nh(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function ah(t,e){var a=nh(t);t=0;for(var i;a;){if(a.nodeType===3){if(i=t+a.textContent.length,t<=e&&i>=e)return{node:a,offset:e-t};t=i}t:{for(;a;){if(a.nextSibling){a=a.nextSibling;break t}a=a.parentNode}a=void 0}a=nh(a)}}function rh(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?rh(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function oh(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=$l(t.document);e instanceof t.HTMLIFrameElement;){try{var a=typeof e.contentWindow.location.href=="string"}catch{a=!1}if(a)t=e.contentWindow;else break;e=$l(t.document)}return e}function rc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Jb=da&&"documentMode"in document&&11>=document.documentMode,Ir=null,oc=null,ii=null,ic=!1;function ih(t,e,a){var i=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;ic||Ir==null||Ir!==$l(i)||(i=Ir,"selectionStart"in i&&rc(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),ii&&oi(ii,i)||(ii=i,i=Os(oc,"onSelect"),0<i.length&&(e=new ql("onSelect","select",null,e,a),t.push({event:e,listeners:i}),e.target=Ir)))}function pr(t,e){var a={};return a[t.toLowerCase()]=e.toLowerCase(),a["Webkit"+t]="webkit"+e,a["Moz"+t]="moz"+e,a}var Wr={animationend:pr("Animation","AnimationEnd"),animationiteration:pr("Animation","AnimationIteration"),animationstart:pr("Animation","AnimationStart"),transitionrun:pr("Transition","TransitionRun"),transitionstart:pr("Transition","TransitionStart"),transitioncancel:pr("Transition","TransitionCancel"),transitionend:pr("Transition","TransitionEnd")},lc={},lh={};da&&(lh=document.createElement("div").style,"AnimationEvent"in window||(delete Wr.animationend.animation,delete Wr.animationiteration.animation,delete Wr.animationstart.animation),"TransitionEvent"in window||delete Wr.transitionend.transition);function hr(t){if(lc[t])return lc[t];if(!Wr[t])return t;var e=Wr[t],a;for(a in e)if(e.hasOwnProperty(a)&&a in lh)return lc[t]=e[a];return t}var sh=hr("animationend"),uh=hr("animationiteration"),ch=hr("animationstart"),t1=hr("transitionrun"),e1=hr("transitionstart"),n1=hr("transitioncancel"),fh=hr("transitionend"),dh=new Map,sc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");sc.push("scrollEnd");function Yn(t,e){dh.set(t,e),fr(e,[t])}var ph=new WeakMap;function Bn(t,e){if(typeof t=="object"&&t!==null){var a=ph.get(t);return a!==void 0?a:(e={value:t,source:e,stack:Mp(e)},ph.set(t,e),e)}return{value:t,source:e,stack:Mp(e)}}var Nn=[],Kr=0,uc=0;function Vl(){for(var t=Kr,e=uc=Kr=0;e<t;){var a=Nn[e];Nn[e++]=null;var i=Nn[e];Nn[e++]=null;var s=Nn[e];Nn[e++]=null;var f=Nn[e];if(Nn[e++]=null,i!==null&&s!==null){var g=i.pending;g===null?s.next=s:(s.next=g.next,g.next=s),i.pending=s}f!==0&&hh(a,s,f)}}function Yl(t,e,a,i){Nn[Kr++]=t,Nn[Kr++]=e,Nn[Kr++]=a,Nn[Kr++]=i,uc|=i,t.lanes|=i,t=t.alternate,t!==null&&(t.lanes|=i)}function cc(t,e,a,i){return Yl(t,e,a,i),Xl(t)}function Qr(t,e){return Yl(t,null,null,e),Xl(t)}function hh(t,e,a){t.lanes|=a;var i=t.alternate;i!==null&&(i.lanes|=a);for(var s=!1,f=t.return;f!==null;)f.childLanes|=a,i=f.alternate,i!==null&&(i.childLanes|=a),f.tag===22&&(t=f.stateNode,t===null||t._visibility&1||(s=!0)),t=f,f=f.return;return t.tag===3?(f=t.stateNode,s&&e!==null&&(s=31-ae(a),t=f.hiddenUpdates,i=t[s],i===null?t[s]=[e]:i.push(e),e.lane=a|536870912),f):null}function Xl(t){if(50<Di)throw Di=0,yf=null,Error(l(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var Zr={};function a1(t,e,a,i){this.tag=t,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function yn(t,e,a,i){return new a1(t,e,a,i)}function fc(t){return t=t.prototype,!(!t||!t.isReactComponent)}function pa(t,e){var a=t.alternate;return a===null?(a=yn(t.tag,e,t.key,t.mode),a.elementType=t.elementType,a.type=t.type,a.stateNode=t.stateNode,a.alternate=t,t.alternate=a):(a.pendingProps=e,a.type=t.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=t.flags&65011712,a.childLanes=t.childLanes,a.lanes=t.lanes,a.child=t.child,a.memoizedProps=t.memoizedProps,a.memoizedState=t.memoizedState,a.updateQueue=t.updateQueue,e=t.dependencies,a.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},a.sibling=t.sibling,a.index=t.index,a.ref=t.ref,a.refCleanup=t.refCleanup,a}function mh(t,e){t.flags&=65011714;var a=t.alternate;return a===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=a.childLanes,t.lanes=a.lanes,t.child=a.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=a.memoizedProps,t.memoizedState=a.memoizedState,t.updateQueue=a.updateQueue,t.type=a.type,e=a.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Il(t,e,a,i,s,f){var g=0;if(i=t,typeof t=="function")fc(t)&&(g=1);else if(typeof t=="string")g=oS(t,a,lt.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case K:return t=yn(31,a,e,s),t.elementType=K,t.lanes=f,t;case x:return mr(a.children,s,f,e);case C:g=8,s|=24;break;case D:return t=yn(12,a,e,s|2),t.elementType=D,t.lanes=f,t;case A:return t=yn(13,a,e,s),t.elementType=A,t.lanes=f,t;case $:return t=yn(19,a,e,s),t.elementType=$,t.lanes=f,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case j:case M:g=10;break t;case U:g=9;break t;case O:g=11;break t;case H:g=14;break t;case P:g=16,i=null;break t}g=29,a=Error(l(130,t===null?"null":typeof t,"")),i=null}return e=yn(g,a,e,s),e.elementType=t,e.type=i,e.lanes=f,e}function mr(t,e,a,i){return t=yn(7,t,i,e),t.lanes=a,t}function dc(t,e,a){return t=yn(6,t,null,e),t.lanes=a,t}function pc(t,e,a){return e=yn(4,t.children!==null?t.children:[],t.key,e),e.lanes=a,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Fr=[],Jr=0,Wl=null,Kl=0,jn=[],kn=0,gr=null,ha=1,ma="";function yr(t,e){Fr[Jr++]=Kl,Fr[Jr++]=Wl,Wl=t,Kl=e}function gh(t,e,a){jn[kn++]=ha,jn[kn++]=ma,jn[kn++]=gr,gr=t;var i=ha;t=ma;var s=32-ae(i)-1;i&=~(1<<s),a+=1;var f=32-ae(e)+s;if(30<f){var g=s-s%5;f=(i&(1<<g)-1).toString(32),i>>=g,s-=g,ha=1<<32-ae(e)+s|a<<s|i,ma=f+t}else ha=1<<f|a<<s|i,ma=t}function hc(t){t.return!==null&&(yr(t,1),gh(t,1,0))}function mc(t){for(;t===Wl;)Wl=Fr[--Jr],Fr[Jr]=null,Kl=Fr[--Jr],Fr[Jr]=null;for(;t===gr;)gr=jn[--kn],jn[kn]=null,ma=jn[--kn],jn[kn]=null,ha=jn[--kn],jn[kn]=null}var an=null,Ae=null,Ft=!1,vr=null,Kn=!1,gc=Error(l(519));function br(t){var e=Error(l(418,""));throw ui(Bn(e,t)),gc}function yh(t){var e=t.stateNode,a=t.type,i=t.memoizedProps;switch(e[Fe]=t,e[ln]=i,a){case"dialog":qt("cancel",e),qt("close",e);break;case"iframe":case"object":case"embed":qt("load",e);break;case"video":case"audio":for(a=0;a<Ni.length;a++)qt(Ni[a],e);break;case"source":qt("error",e);break;case"img":case"image":case"link":qt("error",e),qt("load",e);break;case"details":qt("toggle",e);break;case"input":qt("invalid",e),Bp(e,i.value,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name,!0),kl(e);break;case"select":qt("invalid",e);break;case"textarea":qt("invalid",e),jp(e,i.value,i.defaultValue,i.children),kl(e)}a=i.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||e.textContent===""+a||i.suppressHydrationWarning===!0||jg(e.textContent,a)?(i.popover!=null&&(qt("beforetoggle",e),qt("toggle",e)),i.onScroll!=null&&qt("scroll",e),i.onScrollEnd!=null&&qt("scrollend",e),i.onClick!=null&&(e.onclick=Ms),e=!0):e=!1,e||br(t)}function vh(t){for(an=t.return;an;)switch(an.tag){case 5:case 13:Kn=!1;return;case 27:case 3:Kn=!0;return;default:an=an.return}}function li(t){if(t!==an)return!1;if(!Ft)return vh(t),Ft=!0,!1;var e=t.tag,a;if((a=e!==3&&e!==27)&&((a=e===5)&&(a=t.type,a=!(a!=="form"&&a!=="button")||Nf(t.type,t.memoizedProps)),a=!a),a&&Ae&&br(t),vh(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(l(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(a=t.data,a==="/$"){if(e===0){Ae=In(t.nextSibling);break t}e--}else a!=="$"&&a!=="$!"&&a!=="$?"||e++;t=t.nextSibling}Ae=null}}else e===27?(e=Ae,Za(t.type)?(t=_f,_f=null,Ae=t):Ae=e):Ae=an?In(t.stateNode.nextSibling):null;return!0}function si(){Ae=an=null,Ft=!1}function bh(){var t=vr;return t!==null&&(fn===null?fn=t:fn.push.apply(fn,t),vr=null),t}function ui(t){vr===null?vr=[t]:vr.push(t)}var yc=X(null),Sr=null,ga=null;function $a(t,e,a){nt(yc,e._currentValue),e._currentValue=a}function ya(t){t._currentValue=yc.current,et(yc)}function vc(t,e,a){for(;t!==null;){var i=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,i!==null&&(i.childLanes|=e)):i!==null&&(i.childLanes&e)!==e&&(i.childLanes|=e),t===a)break;t=t.return}}function bc(t,e,a,i){var s=t.child;for(s!==null&&(s.return=t);s!==null;){var f=s.dependencies;if(f!==null){var g=s.child;f=f.firstContext;t:for(;f!==null;){var b=f;f=s;for(var N=0;N<e.length;N++)if(b.context===e[N]){f.lanes|=a,b=f.alternate,b!==null&&(b.lanes|=a),vc(f.return,a,t),i||(g=null);break t}f=b.next}}else if(s.tag===18){if(g=s.return,g===null)throw Error(l(341));g.lanes|=a,f=g.alternate,f!==null&&(f.lanes|=a),vc(g,a,t),g=null}else g=s.child;if(g!==null)g.return=s;else for(g=s;g!==null;){if(g===t){g=null;break}if(s=g.sibling,s!==null){s.return=g.return,g=s;break}g=g.return}s=g}}function ci(t,e,a,i){t=null;for(var s=e,f=!1;s!==null;){if(!f){if((s.flags&524288)!==0)f=!0;else if((s.flags&262144)!==0)break}if(s.tag===10){var g=s.alternate;if(g===null)throw Error(l(387));if(g=g.memoizedProps,g!==null){var b=s.type;gn(s.pendingProps.value,g.value)||(t!==null?t.push(b):t=[b])}}else if(s===Tt.current){if(g=s.alternate,g===null)throw Error(l(387));g.memoizedState.memoizedState!==s.memoizedState.memoizedState&&(t!==null?t.push(Li):t=[Li])}s=s.return}t!==null&&bc(e,t,a,i),e.flags|=262144}function Ql(t){for(t=t.firstContext;t!==null;){if(!gn(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function xr(t){Sr=t,ga=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Je(t){return Sh(Sr,t)}function Zl(t,e){return Sr===null&&xr(t),Sh(t,e)}function Sh(t,e){var a=e._currentValue;if(e={context:e,memoizedValue:a,next:null},ga===null){if(t===null)throw Error(l(308));ga=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else ga=ga.next=e;return a}var r1=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(a,i){t.push(i)}};this.abort=function(){e.aborted=!0,t.forEach(function(a){return a()})}},o1=n.unstable_scheduleCallback,i1=n.unstable_NormalPriority,He={$$typeof:M,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Sc(){return{controller:new r1,data:new Map,refCount:0}}function fi(t){t.refCount--,t.refCount===0&&o1(i1,function(){t.controller.abort()})}var di=null,xc=0,to=0,eo=null;function l1(t,e){if(di===null){var a=di=[];xc=0,to=Ef(),eo={status:"pending",value:void 0,then:function(i){a.push(i)}}}return xc++,e.then(xh,xh),e}function xh(){if(--xc===0&&di!==null){eo!==null&&(eo.status="fulfilled");var t=di;di=null,to=0,eo=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function s1(t,e){var a=[],i={status:"pending",value:null,reason:null,then:function(s){a.push(s)}};return t.then(function(){i.status="fulfilled",i.value=e;for(var s=0;s<a.length;s++)(0,a[s])(e)},function(s){for(i.status="rejected",i.reason=s,s=0;s<a.length;s++)(0,a[s])(void 0)}),i}var Ch=B.S;B.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&l1(t,e),Ch!==null&&Ch(t,e)};var Cr=X(null);function Cc(){var t=Cr.current;return t!==null?t:ve.pooledCache}function Fl(t,e){e===null?nt(Cr,Cr.current):nt(Cr,e.pool)}function Th(){var t=Cc();return t===null?null:{parent:He._currentValue,pool:t}}var pi=Error(l(460)),Eh=Error(l(474)),Jl=Error(l(542)),Tc={then:function(){}};function Rh(t){return t=t.status,t==="fulfilled"||t==="rejected"}function ts(){}function wh(t,e,a){switch(a=t[a],a===void 0?t.push(e):a!==e&&(e.then(ts,ts),e=a),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Oh(t),t;default:if(typeof e.status=="string")e.then(ts,ts);else{if(t=ve,t!==null&&100<t.shellSuspendCounter)throw Error(l(482));t=e,t.status="pending",t.then(function(i){if(e.status==="pending"){var s=e;s.status="fulfilled",s.value=i}},function(i){if(e.status==="pending"){var s=e;s.status="rejected",s.reason=i}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Oh(t),t}throw hi=e,pi}}var hi=null;function Ah(){if(hi===null)throw Error(l(459));var t=hi;return hi=null,t}function Oh(t){if(t===pi||t===Jl)throw Error(l(483))}var _a=!1;function Ec(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Rc(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Ua(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function La(t,e,a){var i=t.updateQueue;if(i===null)return null;if(i=i.shared,(oe&2)!==0){var s=i.pending;return s===null?e.next=e:(e.next=s.next,s.next=e),i.pending=e,e=Xl(t),hh(t,null,a),e}return Yl(t,i,e,a),Xl(t)}function mi(t,e,a){if(e=e.updateQueue,e!==null&&(e=e.shared,(a&4194048)!==0)){var i=e.lanes;i&=t.pendingLanes,a|=i,e.lanes=a,Dl(t,a)}}function wc(t,e){var a=t.updateQueue,i=t.alternate;if(i!==null&&(i=i.updateQueue,a===i)){var s=null,f=null;if(a=a.firstBaseUpdate,a!==null){do{var g={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};f===null?s=f=g:f=f.next=g,a=a.next}while(a!==null);f===null?s=f=e:f=f.next=e}else s=f=e;a={baseState:i.baseState,firstBaseUpdate:s,lastBaseUpdate:f,shared:i.shared,callbacks:i.callbacks},t.updateQueue=a;return}t=a.lastBaseUpdate,t===null?a.firstBaseUpdate=e:t.next=e,a.lastBaseUpdate=e}var Ac=!1;function gi(){if(Ac){var t=eo;if(t!==null)throw t}}function yi(t,e,a,i){Ac=!1;var s=t.updateQueue;_a=!1;var f=s.firstBaseUpdate,g=s.lastBaseUpdate,b=s.shared.pending;if(b!==null){s.shared.pending=null;var N=b,G=N.next;N.next=null,g===null?f=G:g.next=G,g=N;var tt=t.alternate;tt!==null&&(tt=tt.updateQueue,b=tt.lastBaseUpdate,b!==g&&(b===null?tt.firstBaseUpdate=G:b.next=G,tt.lastBaseUpdate=N))}if(f!==null){var rt=s.baseState;g=0,tt=G=N=null,b=f;do{var Y=b.lane&-536870913,I=Y!==b.lane;if(I?(It&Y)===Y:(i&Y)===Y){Y!==0&&Y===to&&(Ac=!0),tt!==null&&(tt=tt.next={lane:0,tag:b.tag,payload:b.payload,callback:null,next:null});t:{var Rt=t,xt=b;Y=e;var fe=a;switch(xt.tag){case 1:if(Rt=xt.payload,typeof Rt=="function"){rt=Rt.call(fe,rt,Y);break t}rt=Rt;break t;case 3:Rt.flags=Rt.flags&-65537|128;case 0:if(Rt=xt.payload,Y=typeof Rt=="function"?Rt.call(fe,rt,Y):Rt,Y==null)break t;rt=y({},rt,Y);break t;case 2:_a=!0}}Y=b.callback,Y!==null&&(t.flags|=64,I&&(t.flags|=8192),I=s.callbacks,I===null?s.callbacks=[Y]:I.push(Y))}else I={lane:Y,tag:b.tag,payload:b.payload,callback:b.callback,next:null},tt===null?(G=tt=I,N=rt):tt=tt.next=I,g|=Y;if(b=b.next,b===null){if(b=s.shared.pending,b===null)break;I=b,b=I.next,I.next=null,s.lastBaseUpdate=I,s.shared.pending=null}}while(!0);tt===null&&(N=rt),s.baseState=N,s.firstBaseUpdate=G,s.lastBaseUpdate=tt,f===null&&(s.shared.lanes=0),Ia|=g,t.lanes=g,t.memoizedState=rt}}function Mh(t,e){if(typeof t!="function")throw Error(l(191,t));t.call(e)}function zh(t,e){var a=t.callbacks;if(a!==null)for(t.callbacks=null,t=0;t<a.length;t++)Mh(a[t],e)}var no=X(null),es=X(0);function Dh(t,e){t=Ea,nt(es,t),nt(no,e),Ea=t|e.baseLanes}function Oc(){nt(es,Ea),nt(no,no.current)}function Mc(){Ea=es.current,et(no),et(es)}var Ha=0,kt=null,ue=null,ke=null,ns=!1,ao=!1,Tr=!1,as=0,vi=0,ro=null,u1=0;function Be(){throw Error(l(321))}function zc(t,e){if(e===null)return!1;for(var a=0;a<e.length&&a<t.length;a++)if(!gn(t[a],e[a]))return!1;return!0}function Dc(t,e,a,i,s,f){return Ha=f,kt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,B.H=t===null||t.memoizedState===null?hm:mm,Tr=!1,f=a(i,s),Tr=!1,ao&&(f=Nh(e,a,i,s)),Bh(t),f}function Bh(t){B.H=us;var e=ue!==null&&ue.next!==null;if(Ha=0,ke=ue=kt=null,ns=!1,vi=0,ro=null,e)throw Error(l(300));t===null||Ve||(t=t.dependencies,t!==null&&Ql(t)&&(Ve=!0))}function Nh(t,e,a,i){kt=t;var s=0;do{if(ao&&(ro=null),vi=0,ao=!1,25<=s)throw Error(l(301));if(s+=1,ke=ue=null,t.updateQueue!=null){var f=t.updateQueue;f.lastEffect=null,f.events=null,f.stores=null,f.memoCache!=null&&(f.memoCache.index=0)}B.H=g1,f=e(a,i)}while(ao);return f}function c1(){var t=B.H,e=t.useState()[0];return e=typeof e.then=="function"?bi(e):e,t=t.useState()[0],(ue!==null?ue.memoizedState:null)!==t&&(kt.flags|=1024),e}function Bc(){var t=as!==0;return as=0,t}function Nc(t,e,a){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~a}function jc(t){if(ns){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}ns=!1}Ha=0,ke=ue=kt=null,ao=!1,vi=as=0,ro=null}function un(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ke===null?kt.memoizedState=ke=t:ke=ke.next=t,ke}function $e(){if(ue===null){var t=kt.alternate;t=t!==null?t.memoizedState:null}else t=ue.next;var e=ke===null?kt.memoizedState:ke.next;if(e!==null)ke=e,ue=t;else{if(t===null)throw kt.alternate===null?Error(l(467)):Error(l(310));ue=t,t={memoizedState:ue.memoizedState,baseState:ue.baseState,baseQueue:ue.baseQueue,queue:ue.queue,next:null},ke===null?kt.memoizedState=ke=t:ke=ke.next=t}return ke}function kc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function bi(t){var e=vi;return vi+=1,ro===null&&(ro=[]),t=wh(ro,t,e),e=kt,(ke===null?e.memoizedState:ke.next)===null&&(e=e.alternate,B.H=e===null||e.memoizedState===null?hm:mm),t}function rs(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return bi(t);if(t.$$typeof===M)return Je(t)}throw Error(l(438,String(t)))}function $c(t){var e=null,a=kt.updateQueue;if(a!==null&&(e=a.memoCache),e==null){var i=kt.alternate;i!==null&&(i=i.updateQueue,i!==null&&(i=i.memoCache,i!=null&&(e={data:i.data.map(function(s){return s.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),a===null&&(a=kc(),kt.updateQueue=a),a.memoCache=e,a=e.data[e.index],a===void 0)for(a=e.data[e.index]=Array(t),i=0;i<t;i++)a[i]=v;return e.index++,a}function va(t,e){return typeof e=="function"?e(t):e}function os(t){var e=$e();return _c(e,ue,t)}function _c(t,e,a){var i=t.queue;if(i===null)throw Error(l(311));i.lastRenderedReducer=a;var s=t.baseQueue,f=i.pending;if(f!==null){if(s!==null){var g=s.next;s.next=f.next,f.next=g}e.baseQueue=s=f,i.pending=null}if(f=t.baseState,s===null)t.memoizedState=f;else{e=s.next;var b=g=null,N=null,G=e,tt=!1;do{var rt=G.lane&-536870913;if(rt!==G.lane?(It&rt)===rt:(Ha&rt)===rt){var Y=G.revertLane;if(Y===0)N!==null&&(N=N.next={lane:0,revertLane:0,action:G.action,hasEagerState:G.hasEagerState,eagerState:G.eagerState,next:null}),rt===to&&(tt=!0);else if((Ha&Y)===Y){G=G.next,Y===to&&(tt=!0);continue}else rt={lane:0,revertLane:G.revertLane,action:G.action,hasEagerState:G.hasEagerState,eagerState:G.eagerState,next:null},N===null?(b=N=rt,g=f):N=N.next=rt,kt.lanes|=Y,Ia|=Y;rt=G.action,Tr&&a(f,rt),f=G.hasEagerState?G.eagerState:a(f,rt)}else Y={lane:rt,revertLane:G.revertLane,action:G.action,hasEagerState:G.hasEagerState,eagerState:G.eagerState,next:null},N===null?(b=N=Y,g=f):N=N.next=Y,kt.lanes|=rt,Ia|=rt;G=G.next}while(G!==null&&G!==e);if(N===null?g=f:N.next=b,!gn(f,t.memoizedState)&&(Ve=!0,tt&&(a=eo,a!==null)))throw a;t.memoizedState=f,t.baseState=g,t.baseQueue=N,i.lastRenderedState=f}return s===null&&(i.lanes=0),[t.memoizedState,i.dispatch]}function Uc(t){var e=$e(),a=e.queue;if(a===null)throw Error(l(311));a.lastRenderedReducer=t;var i=a.dispatch,s=a.pending,f=e.memoizedState;if(s!==null){a.pending=null;var g=s=s.next;do f=t(f,g.action),g=g.next;while(g!==s);gn(f,e.memoizedState)||(Ve=!0),e.memoizedState=f,e.baseQueue===null&&(e.baseState=f),a.lastRenderedState=f}return[f,i]}function jh(t,e,a){var i=kt,s=$e(),f=Ft;if(f){if(a===void 0)throw Error(l(407));a=a()}else a=e();var g=!gn((ue||s).memoizedState,a);g&&(s.memoizedState=a,Ve=!0),s=s.queue;var b=_h.bind(null,i,s,t);if(Si(2048,8,b,[t]),s.getSnapshot!==e||g||ke!==null&&ke.memoizedState.tag&1){if(i.flags|=2048,oo(9,is(),$h.bind(null,i,s,a,e),null),ve===null)throw Error(l(349));f||(Ha&124)!==0||kh(i,e,a)}return a}function kh(t,e,a){t.flags|=16384,t={getSnapshot:e,value:a},e=kt.updateQueue,e===null?(e=kc(),kt.updateQueue=e,e.stores=[t]):(a=e.stores,a===null?e.stores=[t]:a.push(t))}function $h(t,e,a,i){e.value=a,e.getSnapshot=i,Uh(e)&&Lh(t)}function _h(t,e,a){return a(function(){Uh(e)&&Lh(t)})}function Uh(t){var e=t.getSnapshot;t=t.value;try{var a=e();return!gn(t,a)}catch{return!0}}function Lh(t){var e=Qr(t,2);e!==null&&Cn(e,t,2)}function Lc(t){var e=un();if(typeof t=="function"){var a=t;if(t=a(),Tr){Ee(!0);try{a()}finally{Ee(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:va,lastRenderedState:t},e}function Hh(t,e,a,i){return t.baseState=a,_c(t,ue,typeof i=="function"?i:va)}function f1(t,e,a,i,s){if(ss(t))throw Error(l(485));if(t=e.action,t!==null){var f={payload:s,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(g){f.listeners.push(g)}};B.T!==null?a(!0):f.isTransition=!1,i(f),a=e.pending,a===null?(f.next=e.pending=f,qh(e,f)):(f.next=a.next,e.pending=a.next=f)}}function qh(t,e){var a=e.action,i=e.payload,s=t.state;if(e.isTransition){var f=B.T,g={};B.T=g;try{var b=a(s,i),N=B.S;N!==null&&N(g,b),Ph(t,e,b)}catch(G){Hc(t,e,G)}finally{B.T=f}}else try{f=a(s,i),Ph(t,e,f)}catch(G){Hc(t,e,G)}}function Ph(t,e,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(i){Gh(t,e,i)},function(i){return Hc(t,e,i)}):Gh(t,e,a)}function Gh(t,e,a){e.status="fulfilled",e.value=a,Vh(e),t.state=a,e=t.pending,e!==null&&(a=e.next,a===e?t.pending=null:(a=a.next,e.next=a,qh(t,a)))}function Hc(t,e,a){var i=t.pending;if(t.pending=null,i!==null){i=i.next;do e.status="rejected",e.reason=a,Vh(e),e=e.next;while(e!==i)}t.action=null}function Vh(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Yh(t,e){return e}function Xh(t,e){if(Ft){var a=ve.formState;if(a!==null){t:{var i=kt;if(Ft){if(Ae){e:{for(var s=Ae,f=Kn;s.nodeType!==8;){if(!f){s=null;break e}if(s=In(s.nextSibling),s===null){s=null;break e}}f=s.data,s=f==="F!"||f==="F"?s:null}if(s){Ae=In(s.nextSibling),i=s.data==="F!";break t}}br(i)}i=!1}i&&(e=a[0])}}return a=un(),a.memoizedState=a.baseState=e,i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Yh,lastRenderedState:e},a.queue=i,a=fm.bind(null,kt,i),i.dispatch=a,i=Lc(!1),f=Yc.bind(null,kt,!1,i.queue),i=un(),s={state:e,dispatch:null,action:t,pending:null},i.queue=s,a=f1.bind(null,kt,s,f,a),s.dispatch=a,i.memoizedState=t,[e,a,!1]}function Ih(t){var e=$e();return Wh(e,ue,t)}function Wh(t,e,a){if(e=_c(t,e,Yh)[0],t=os(va)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var i=bi(e)}catch(g){throw g===pi?Jl:g}else i=e;e=$e();var s=e.queue,f=s.dispatch;return a!==e.memoizedState&&(kt.flags|=2048,oo(9,is(),d1.bind(null,s,a),null)),[i,f,t]}function d1(t,e){t.action=e}function Kh(t){var e=$e(),a=ue;if(a!==null)return Wh(e,a,t);$e(),e=e.memoizedState,a=$e();var i=a.queue.dispatch;return a.memoizedState=t,[e,i,!1]}function oo(t,e,a,i){return t={tag:t,create:a,deps:i,inst:e,next:null},e=kt.updateQueue,e===null&&(e=kc(),kt.updateQueue=e),a=e.lastEffect,a===null?e.lastEffect=t.next=t:(i=a.next,a.next=t,t.next=i,e.lastEffect=t),t}function is(){return{destroy:void 0,resource:void 0}}function Qh(){return $e().memoizedState}function ls(t,e,a,i){var s=un();i=i===void 0?null:i,kt.flags|=t,s.memoizedState=oo(1|e,is(),a,i)}function Si(t,e,a,i){var s=$e();i=i===void 0?null:i;var f=s.memoizedState.inst;ue!==null&&i!==null&&zc(i,ue.memoizedState.deps)?s.memoizedState=oo(e,f,a,i):(kt.flags|=t,s.memoizedState=oo(1|e,f,a,i))}function Zh(t,e){ls(8390656,8,t,e)}function Fh(t,e){Si(2048,8,t,e)}function Jh(t,e){return Si(4,2,t,e)}function tm(t,e){return Si(4,4,t,e)}function em(t,e){if(typeof e=="function"){t=t();var a=e(t);return function(){typeof a=="function"?a():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function nm(t,e,a){a=a!=null?a.concat([t]):null,Si(4,4,em.bind(null,e,t),a)}function qc(){}function am(t,e){var a=$e();e=e===void 0?null:e;var i=a.memoizedState;return e!==null&&zc(e,i[1])?i[0]:(a.memoizedState=[t,e],t)}function rm(t,e){var a=$e();e=e===void 0?null:e;var i=a.memoizedState;if(e!==null&&zc(e,i[1]))return i[0];if(i=t(),Tr){Ee(!0);try{t()}finally{Ee(!1)}}return a.memoizedState=[i,e],i}function Pc(t,e,a){return a===void 0||(Ha&1073741824)!==0?t.memoizedState=e:(t.memoizedState=a,t=lg(),kt.lanes|=t,Ia|=t,a)}function om(t,e,a,i){return gn(a,e)?a:no.current!==null?(t=Pc(t,a,i),gn(t,e)||(Ve=!0),t):(Ha&42)===0?(Ve=!0,t.memoizedState=a):(t=lg(),kt.lanes|=t,Ia|=t,e)}function im(t,e,a,i,s){var f=V.p;V.p=f!==0&&8>f?f:8;var g=B.T,b={};B.T=b,Yc(t,!1,e,a);try{var N=s(),G=B.S;if(G!==null&&G(b,N),N!==null&&typeof N=="object"&&typeof N.then=="function"){var tt=s1(N,i);xi(t,e,tt,xn(t))}else xi(t,e,i,xn(t))}catch(rt){xi(t,e,{then:function(){},status:"rejected",reason:rt},xn())}finally{V.p=f,B.T=g}}function p1(){}function Gc(t,e,a,i){if(t.tag!==5)throw Error(l(476));var s=lm(t).queue;im(t,s,e,ot,a===null?p1:function(){return sm(t),a(i)})}function lm(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:ot,baseState:ot,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:va,lastRenderedState:ot},next:null};var a={};return e.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:va,lastRenderedState:a},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function sm(t){var e=lm(t).next.queue;xi(t,e,{},xn())}function Vc(){return Je(Li)}function um(){return $e().memoizedState}function cm(){return $e().memoizedState}function h1(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var a=xn();t=Ua(a);var i=La(e,t,a);i!==null&&(Cn(i,e,a),mi(i,e,a)),e={cache:Sc()},t.payload=e;return}e=e.return}}function m1(t,e,a){var i=xn();a={lane:i,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},ss(t)?dm(e,a):(a=cc(t,e,a,i),a!==null&&(Cn(a,t,i),pm(a,e,i)))}function fm(t,e,a){var i=xn();xi(t,e,a,i)}function xi(t,e,a,i){var s={lane:i,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(ss(t))dm(e,s);else{var f=t.alternate;if(t.lanes===0&&(f===null||f.lanes===0)&&(f=e.lastRenderedReducer,f!==null))try{var g=e.lastRenderedState,b=f(g,a);if(s.hasEagerState=!0,s.eagerState=b,gn(b,g))return Yl(t,e,s,0),ve===null&&Vl(),!1}catch{}finally{}if(a=cc(t,e,s,i),a!==null)return Cn(a,t,i),pm(a,e,i),!0}return!1}function Yc(t,e,a,i){if(i={lane:2,revertLane:Ef(),action:i,hasEagerState:!1,eagerState:null,next:null},ss(t)){if(e)throw Error(l(479))}else e=cc(t,a,i,2),e!==null&&Cn(e,t,2)}function ss(t){var e=t.alternate;return t===kt||e!==null&&e===kt}function dm(t,e){ao=ns=!0;var a=t.pending;a===null?e.next=e:(e.next=a.next,a.next=e),t.pending=e}function pm(t,e,a){if((a&4194048)!==0){var i=e.lanes;i&=t.pendingLanes,a|=i,e.lanes=a,Dl(t,a)}}var us={readContext:Je,use:rs,useCallback:Be,useContext:Be,useEffect:Be,useImperativeHandle:Be,useLayoutEffect:Be,useInsertionEffect:Be,useMemo:Be,useReducer:Be,useRef:Be,useState:Be,useDebugValue:Be,useDeferredValue:Be,useTransition:Be,useSyncExternalStore:Be,useId:Be,useHostTransitionStatus:Be,useFormState:Be,useActionState:Be,useOptimistic:Be,useMemoCache:Be,useCacheRefresh:Be},hm={readContext:Je,use:rs,useCallback:function(t,e){return un().memoizedState=[t,e===void 0?null:e],t},useContext:Je,useEffect:Zh,useImperativeHandle:function(t,e,a){a=a!=null?a.concat([t]):null,ls(4194308,4,em.bind(null,e,t),a)},useLayoutEffect:function(t,e){return ls(4194308,4,t,e)},useInsertionEffect:function(t,e){ls(4,2,t,e)},useMemo:function(t,e){var a=un();e=e===void 0?null:e;var i=t();if(Tr){Ee(!0);try{t()}finally{Ee(!1)}}return a.memoizedState=[i,e],i},useReducer:function(t,e,a){var i=un();if(a!==void 0){var s=a(e);if(Tr){Ee(!0);try{a(e)}finally{Ee(!1)}}}else s=e;return i.memoizedState=i.baseState=s,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:s},i.queue=t,t=t.dispatch=m1.bind(null,kt,t),[i.memoizedState,t]},useRef:function(t){var e=un();return t={current:t},e.memoizedState=t},useState:function(t){t=Lc(t);var e=t.queue,a=fm.bind(null,kt,e);return e.dispatch=a,[t.memoizedState,a]},useDebugValue:qc,useDeferredValue:function(t,e){var a=un();return Pc(a,t,e)},useTransition:function(){var t=Lc(!1);return t=im.bind(null,kt,t.queue,!0,!1),un().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,a){var i=kt,s=un();if(Ft){if(a===void 0)throw Error(l(407));a=a()}else{if(a=e(),ve===null)throw Error(l(349));(It&124)!==0||kh(i,e,a)}s.memoizedState=a;var f={value:a,getSnapshot:e};return s.queue=f,Zh(_h.bind(null,i,f,t),[t]),i.flags|=2048,oo(9,is(),$h.bind(null,i,f,a,e),null),a},useId:function(){var t=un(),e=ve.identifierPrefix;if(Ft){var a=ma,i=ha;a=(i&~(1<<32-ae(i)-1)).toString(32)+a,e="«"+e+"R"+a,a=as++,0<a&&(e+="H"+a.toString(32)),e+="»"}else a=u1++,e="«"+e+"r"+a.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:Vc,useFormState:Xh,useActionState:Xh,useOptimistic:function(t){var e=un();e.memoizedState=e.baseState=t;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=a,e=Yc.bind(null,kt,!0,a),a.dispatch=e,[t,e]},useMemoCache:$c,useCacheRefresh:function(){return un().memoizedState=h1.bind(null,kt)}},mm={readContext:Je,use:rs,useCallback:am,useContext:Je,useEffect:Fh,useImperativeHandle:nm,useInsertionEffect:Jh,useLayoutEffect:tm,useMemo:rm,useReducer:os,useRef:Qh,useState:function(){return os(va)},useDebugValue:qc,useDeferredValue:function(t,e){var a=$e();return om(a,ue.memoizedState,t,e)},useTransition:function(){var t=os(va)[0],e=$e().memoizedState;return[typeof t=="boolean"?t:bi(t),e]},useSyncExternalStore:jh,useId:um,useHostTransitionStatus:Vc,useFormState:Ih,useActionState:Ih,useOptimistic:function(t,e){var a=$e();return Hh(a,ue,t,e)},useMemoCache:$c,useCacheRefresh:cm},g1={readContext:Je,use:rs,useCallback:am,useContext:Je,useEffect:Fh,useImperativeHandle:nm,useInsertionEffect:Jh,useLayoutEffect:tm,useMemo:rm,useReducer:Uc,useRef:Qh,useState:function(){return Uc(va)},useDebugValue:qc,useDeferredValue:function(t,e){var a=$e();return ue===null?Pc(a,t,e):om(a,ue.memoizedState,t,e)},useTransition:function(){var t=Uc(va)[0],e=$e().memoizedState;return[typeof t=="boolean"?t:bi(t),e]},useSyncExternalStore:jh,useId:um,useHostTransitionStatus:Vc,useFormState:Kh,useActionState:Kh,useOptimistic:function(t,e){var a=$e();return ue!==null?Hh(a,ue,t,e):(a.baseState=t,[t,a.queue.dispatch])},useMemoCache:$c,useCacheRefresh:cm},io=null,Ci=0;function cs(t){var e=Ci;return Ci+=1,io===null&&(io=[]),wh(io,t,e)}function Ti(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function fs(t,e){throw e.$$typeof===S?Error(l(525)):(t=Object.prototype.toString.call(e),Error(l(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function gm(t){var e=t._init;return e(t._payload)}function ym(t){function e(L,k){if(t){var q=L.deletions;q===null?(L.deletions=[k],L.flags|=16):q.push(k)}}function a(L,k){if(!t)return null;for(;k!==null;)e(L,k),k=k.sibling;return null}function i(L){for(var k=new Map;L!==null;)L.key!==null?k.set(L.key,L):k.set(L.index,L),L=L.sibling;return k}function s(L,k){return L=pa(L,k),L.index=0,L.sibling=null,L}function f(L,k,q){return L.index=q,t?(q=L.alternate,q!==null?(q=q.index,q<k?(L.flags|=67108866,k):q):(L.flags|=67108866,k)):(L.flags|=1048576,k)}function g(L){return t&&L.alternate===null&&(L.flags|=67108866),L}function b(L,k,q,at){return k===null||k.tag!==6?(k=dc(q,L.mode,at),k.return=L,k):(k=s(k,q),k.return=L,k)}function N(L,k,q,at){var pt=q.type;return pt===x?tt(L,k,q.props.children,at,q.key):k!==null&&(k.elementType===pt||typeof pt=="object"&&pt!==null&&pt.$$typeof===P&&gm(pt)===k.type)?(k=s(k,q.props),Ti(k,q),k.return=L,k):(k=Il(q.type,q.key,q.props,null,L.mode,at),Ti(k,q),k.return=L,k)}function G(L,k,q,at){return k===null||k.tag!==4||k.stateNode.containerInfo!==q.containerInfo||k.stateNode.implementation!==q.implementation?(k=pc(q,L.mode,at),k.return=L,k):(k=s(k,q.children||[]),k.return=L,k)}function tt(L,k,q,at,pt){return k===null||k.tag!==7?(k=mr(q,L.mode,at,pt),k.return=L,k):(k=s(k,q),k.return=L,k)}function rt(L,k,q){if(typeof k=="string"&&k!==""||typeof k=="number"||typeof k=="bigint")return k=dc(""+k,L.mode,q),k.return=L,k;if(typeof k=="object"&&k!==null){switch(k.$$typeof){case E:return q=Il(k.type,k.key,k.props,null,L.mode,q),Ti(q,k),q.return=L,q;case R:return k=pc(k,L.mode,q),k.return=L,k;case P:var at=k._init;return k=at(k._payload),rt(L,k,q)}if(F(k)||W(k))return k=mr(k,L.mode,q,null),k.return=L,k;if(typeof k.then=="function")return rt(L,cs(k),q);if(k.$$typeof===M)return rt(L,Zl(L,k),q);fs(L,k)}return null}function Y(L,k,q,at){var pt=k!==null?k.key:null;if(typeof q=="string"&&q!==""||typeof q=="number"||typeof q=="bigint")return pt!==null?null:b(L,k,""+q,at);if(typeof q=="object"&&q!==null){switch(q.$$typeof){case E:return q.key===pt?N(L,k,q,at):null;case R:return q.key===pt?G(L,k,q,at):null;case P:return pt=q._init,q=pt(q._payload),Y(L,k,q,at)}if(F(q)||W(q))return pt!==null?null:tt(L,k,q,at,null);if(typeof q.then=="function")return Y(L,k,cs(q),at);if(q.$$typeof===M)return Y(L,k,Zl(L,q),at);fs(L,q)}return null}function I(L,k,q,at,pt){if(typeof at=="string"&&at!==""||typeof at=="number"||typeof at=="bigint")return L=L.get(q)||null,b(k,L,""+at,pt);if(typeof at=="object"&&at!==null){switch(at.$$typeof){case E:return L=L.get(at.key===null?q:at.key)||null,N(k,L,at,pt);case R:return L=L.get(at.key===null?q:at.key)||null,G(k,L,at,pt);case P:var Ut=at._init;return at=Ut(at._payload),I(L,k,q,at,pt)}if(F(at)||W(at))return L=L.get(q)||null,tt(k,L,at,pt,null);if(typeof at.then=="function")return I(L,k,q,cs(at),pt);if(at.$$typeof===M)return I(L,k,q,Zl(k,at),pt);fs(k,at)}return null}function Rt(L,k,q,at){for(var pt=null,Ut=null,vt=k,Ct=k=0,Xe=null;vt!==null&&Ct<q.length;Ct++){vt.index>Ct?(Xe=vt,vt=null):Xe=vt.sibling;var Wt=Y(L,vt,q[Ct],at);if(Wt===null){vt===null&&(vt=Xe);break}t&&vt&&Wt.alternate===null&&e(L,vt),k=f(Wt,k,Ct),Ut===null?pt=Wt:Ut.sibling=Wt,Ut=Wt,vt=Xe}if(Ct===q.length)return a(L,vt),Ft&&yr(L,Ct),pt;if(vt===null){for(;Ct<q.length;Ct++)vt=rt(L,q[Ct],at),vt!==null&&(k=f(vt,k,Ct),Ut===null?pt=vt:Ut.sibling=vt,Ut=vt);return Ft&&yr(L,Ct),pt}for(vt=i(vt);Ct<q.length;Ct++)Xe=I(vt,L,Ct,q[Ct],at),Xe!==null&&(t&&Xe.alternate!==null&&vt.delete(Xe.key===null?Ct:Xe.key),k=f(Xe,k,Ct),Ut===null?pt=Xe:Ut.sibling=Xe,Ut=Xe);return t&&vt.forEach(function(nr){return e(L,nr)}),Ft&&yr(L,Ct),pt}function xt(L,k,q,at){if(q==null)throw Error(l(151));for(var pt=null,Ut=null,vt=k,Ct=k=0,Xe=null,Wt=q.next();vt!==null&&!Wt.done;Ct++,Wt=q.next()){vt.index>Ct?(Xe=vt,vt=null):Xe=vt.sibling;var nr=Y(L,vt,Wt.value,at);if(nr===null){vt===null&&(vt=Xe);break}t&&vt&&nr.alternate===null&&e(L,vt),k=f(nr,k,Ct),Ut===null?pt=nr:Ut.sibling=nr,Ut=nr,vt=Xe}if(Wt.done)return a(L,vt),Ft&&yr(L,Ct),pt;if(vt===null){for(;!Wt.done;Ct++,Wt=q.next())Wt=rt(L,Wt.value,at),Wt!==null&&(k=f(Wt,k,Ct),Ut===null?pt=Wt:Ut.sibling=Wt,Ut=Wt);return Ft&&yr(L,Ct),pt}for(vt=i(vt);!Wt.done;Ct++,Wt=q.next())Wt=I(vt,L,Ct,Wt.value,at),Wt!==null&&(t&&Wt.alternate!==null&&vt.delete(Wt.key===null?Ct:Wt.key),k=f(Wt,k,Ct),Ut===null?pt=Wt:Ut.sibling=Wt,Ut=Wt);return t&&vt.forEach(function(yS){return e(L,yS)}),Ft&&yr(L,Ct),pt}function fe(L,k,q,at){if(typeof q=="object"&&q!==null&&q.type===x&&q.key===null&&(q=q.props.children),typeof q=="object"&&q!==null){switch(q.$$typeof){case E:t:{for(var pt=q.key;k!==null;){if(k.key===pt){if(pt=q.type,pt===x){if(k.tag===7){a(L,k.sibling),at=s(k,q.props.children),at.return=L,L=at;break t}}else if(k.elementType===pt||typeof pt=="object"&&pt!==null&&pt.$$typeof===P&&gm(pt)===k.type){a(L,k.sibling),at=s(k,q.props),Ti(at,q),at.return=L,L=at;break t}a(L,k);break}else e(L,k);k=k.sibling}q.type===x?(at=mr(q.props.children,L.mode,at,q.key),at.return=L,L=at):(at=Il(q.type,q.key,q.props,null,L.mode,at),Ti(at,q),at.return=L,L=at)}return g(L);case R:t:{for(pt=q.key;k!==null;){if(k.key===pt)if(k.tag===4&&k.stateNode.containerInfo===q.containerInfo&&k.stateNode.implementation===q.implementation){a(L,k.sibling),at=s(k,q.children||[]),at.return=L,L=at;break t}else{a(L,k);break}else e(L,k);k=k.sibling}at=pc(q,L.mode,at),at.return=L,L=at}return g(L);case P:return pt=q._init,q=pt(q._payload),fe(L,k,q,at)}if(F(q))return Rt(L,k,q,at);if(W(q)){if(pt=W(q),typeof pt!="function")throw Error(l(150));return q=pt.call(q),xt(L,k,q,at)}if(typeof q.then=="function")return fe(L,k,cs(q),at);if(q.$$typeof===M)return fe(L,k,Zl(L,q),at);fs(L,q)}return typeof q=="string"&&q!==""||typeof q=="number"||typeof q=="bigint"?(q=""+q,k!==null&&k.tag===6?(a(L,k.sibling),at=s(k,q),at.return=L,L=at):(a(L,k),at=dc(q,L.mode,at),at.return=L,L=at),g(L)):a(L,k)}return function(L,k,q,at){try{Ci=0;var pt=fe(L,k,q,at);return io=null,pt}catch(vt){if(vt===pi||vt===Jl)throw vt;var Ut=yn(29,vt,null,L.mode);return Ut.lanes=at,Ut.return=L,Ut}finally{}}}var lo=ym(!0),vm=ym(!1),$n=X(null),Qn=null;function qa(t){var e=t.alternate;nt(qe,qe.current&1),nt($n,t),Qn===null&&(e===null||no.current!==null||e.memoizedState!==null)&&(Qn=t)}function bm(t){if(t.tag===22){if(nt(qe,qe.current),nt($n,t),Qn===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Qn=t)}}else Pa()}function Pa(){nt(qe,qe.current),nt($n,$n.current)}function ba(t){et($n),Qn===t&&(Qn=null),et(qe)}var qe=X(0);function ds(t){for(var e=t;e!==null;){if(e.tag===13){var a=e.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||$f(a)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Xc(t,e,a,i){e=t.memoizedState,a=a(i,e),a=a==null?e:y({},e,a),t.memoizedState=a,t.lanes===0&&(t.updateQueue.baseState=a)}var Ic={enqueueSetState:function(t,e,a){t=t._reactInternals;var i=xn(),s=Ua(i);s.payload=e,a!=null&&(s.callback=a),e=La(t,s,i),e!==null&&(Cn(e,t,i),mi(e,t,i))},enqueueReplaceState:function(t,e,a){t=t._reactInternals;var i=xn(),s=Ua(i);s.tag=1,s.payload=e,a!=null&&(s.callback=a),e=La(t,s,i),e!==null&&(Cn(e,t,i),mi(e,t,i))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var a=xn(),i=Ua(a);i.tag=2,e!=null&&(i.callback=e),e=La(t,i,a),e!==null&&(Cn(e,t,a),mi(e,t,a))}};function Sm(t,e,a,i,s,f,g){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(i,f,g):e.prototype&&e.prototype.isPureReactComponent?!oi(a,i)||!oi(s,f):!0}function xm(t,e,a,i){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(a,i),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(a,i),e.state!==t&&Ic.enqueueReplaceState(e,e.state,null)}function Er(t,e){var a=e;if("ref"in e){a={};for(var i in e)i!=="ref"&&(a[i]=e[i])}if(t=t.defaultProps){a===e&&(a=y({},a));for(var s in t)a[s]===void 0&&(a[s]=t[s])}return a}var ps=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Cm(t){ps(t)}function Tm(t){console.error(t)}function Em(t){ps(t)}function hs(t,e){try{var a=t.onUncaughtError;a(e.value,{componentStack:e.stack})}catch(i){setTimeout(function(){throw i})}}function Rm(t,e,a){try{var i=t.onCaughtError;i(a.value,{componentStack:a.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(s){setTimeout(function(){throw s})}}function Wc(t,e,a){return a=Ua(a),a.tag=3,a.payload={element:null},a.callback=function(){hs(t,e)},a}function wm(t){return t=Ua(t),t.tag=3,t}function Am(t,e,a,i){var s=a.type.getDerivedStateFromError;if(typeof s=="function"){var f=i.value;t.payload=function(){return s(f)},t.callback=function(){Rm(e,a,i)}}var g=a.stateNode;g!==null&&typeof g.componentDidCatch=="function"&&(t.callback=function(){Rm(e,a,i),typeof s!="function"&&(Wa===null?Wa=new Set([this]):Wa.add(this));var b=i.stack;this.componentDidCatch(i.value,{componentStack:b!==null?b:""})})}function y1(t,e,a,i,s){if(a.flags|=32768,i!==null&&typeof i=="object"&&typeof i.then=="function"){if(e=a.alternate,e!==null&&ci(e,a,s,!0),a=$n.current,a!==null){switch(a.tag){case 13:return Qn===null?bf():a.alternate===null&&Oe===0&&(Oe=3),a.flags&=-257,a.flags|=65536,a.lanes=s,i===Tc?a.flags|=16384:(e=a.updateQueue,e===null?a.updateQueue=new Set([i]):e.add(i),xf(t,i,s)),!1;case 22:return a.flags|=65536,i===Tc?a.flags|=16384:(e=a.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([i])},a.updateQueue=e):(a=e.retryQueue,a===null?e.retryQueue=new Set([i]):a.add(i)),xf(t,i,s)),!1}throw Error(l(435,a.tag))}return xf(t,i,s),bf(),!1}if(Ft)return e=$n.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=s,i!==gc&&(t=Error(l(422),{cause:i}),ui(Bn(t,a)))):(i!==gc&&(e=Error(l(423),{cause:i}),ui(Bn(e,a))),t=t.current.alternate,t.flags|=65536,s&=-s,t.lanes|=s,i=Bn(i,a),s=Wc(t.stateNode,i,s),wc(t,s),Oe!==4&&(Oe=2)),!1;var f=Error(l(520),{cause:i});if(f=Bn(f,a),zi===null?zi=[f]:zi.push(f),Oe!==4&&(Oe=2),e===null)return!0;i=Bn(i,a),a=e;do{switch(a.tag){case 3:return a.flags|=65536,t=s&-s,a.lanes|=t,t=Wc(a.stateNode,i,t),wc(a,t),!1;case 1:if(e=a.type,f=a.stateNode,(a.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(Wa===null||!Wa.has(f))))return a.flags|=65536,s&=-s,a.lanes|=s,s=wm(s),Am(s,t,a,i),wc(a,s),!1}a=a.return}while(a!==null);return!1}var Om=Error(l(461)),Ve=!1;function Ie(t,e,a,i){e.child=t===null?vm(e,null,a,i):lo(e,t.child,a,i)}function Mm(t,e,a,i,s){a=a.render;var f=e.ref;if("ref"in i){var g={};for(var b in i)b!=="ref"&&(g[b]=i[b])}else g=i;return xr(e),i=Dc(t,e,a,g,f,s),b=Bc(),t!==null&&!Ve?(Nc(t,e,s),Sa(t,e,s)):(Ft&&b&&hc(e),e.flags|=1,Ie(t,e,i,s),e.child)}function zm(t,e,a,i,s){if(t===null){var f=a.type;return typeof f=="function"&&!fc(f)&&f.defaultProps===void 0&&a.compare===null?(e.tag=15,e.type=f,Dm(t,e,f,i,s)):(t=Il(a.type,null,i,e,e.mode,s),t.ref=e.ref,t.return=e,e.child=t)}if(f=t.child,!nf(t,s)){var g=f.memoizedProps;if(a=a.compare,a=a!==null?a:oi,a(g,i)&&t.ref===e.ref)return Sa(t,e,s)}return e.flags|=1,t=pa(f,i),t.ref=e.ref,t.return=e,e.child=t}function Dm(t,e,a,i,s){if(t!==null){var f=t.memoizedProps;if(oi(f,i)&&t.ref===e.ref)if(Ve=!1,e.pendingProps=i=f,nf(t,s))(t.flags&131072)!==0&&(Ve=!0);else return e.lanes=t.lanes,Sa(t,e,s)}return Kc(t,e,a,i,s)}function Bm(t,e,a){var i=e.pendingProps,s=i.children,f=t!==null?t.memoizedState:null;if(i.mode==="hidden"){if((e.flags&128)!==0){if(i=f!==null?f.baseLanes|a:a,t!==null){for(s=e.child=t.child,f=0;s!==null;)f=f|s.lanes|s.childLanes,s=s.sibling;e.childLanes=f&~i}else e.childLanes=0,e.child=null;return Nm(t,e,i,a)}if((a&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Fl(e,f!==null?f.cachePool:null),f!==null?Dh(e,f):Oc(),bm(e);else return e.lanes=e.childLanes=536870912,Nm(t,e,f!==null?f.baseLanes|a:a,a)}else f!==null?(Fl(e,f.cachePool),Dh(e,f),Pa(),e.memoizedState=null):(t!==null&&Fl(e,null),Oc(),Pa());return Ie(t,e,s,a),e.child}function Nm(t,e,a,i){var s=Cc();return s=s===null?null:{parent:He._currentValue,pool:s},e.memoizedState={baseLanes:a,cachePool:s},t!==null&&Fl(e,null),Oc(),bm(e),t!==null&&ci(t,e,i,!0),null}function ms(t,e){var a=e.ref;if(a===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(l(284));(t===null||t.ref!==a)&&(e.flags|=4194816)}}function Kc(t,e,a,i,s){return xr(e),a=Dc(t,e,a,i,void 0,s),i=Bc(),t!==null&&!Ve?(Nc(t,e,s),Sa(t,e,s)):(Ft&&i&&hc(e),e.flags|=1,Ie(t,e,a,s),e.child)}function jm(t,e,a,i,s,f){return xr(e),e.updateQueue=null,a=Nh(e,i,a,s),Bh(t),i=Bc(),t!==null&&!Ve?(Nc(t,e,f),Sa(t,e,f)):(Ft&&i&&hc(e),e.flags|=1,Ie(t,e,a,f),e.child)}function km(t,e,a,i,s){if(xr(e),e.stateNode===null){var f=Zr,g=a.contextType;typeof g=="object"&&g!==null&&(f=Je(g)),f=new a(i,f),e.memoizedState=f.state!==null&&f.state!==void 0?f.state:null,f.updater=Ic,e.stateNode=f,f._reactInternals=e,f=e.stateNode,f.props=i,f.state=e.memoizedState,f.refs={},Ec(e),g=a.contextType,f.context=typeof g=="object"&&g!==null?Je(g):Zr,f.state=e.memoizedState,g=a.getDerivedStateFromProps,typeof g=="function"&&(Xc(e,a,g,i),f.state=e.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof f.getSnapshotBeforeUpdate=="function"||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(g=f.state,typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount(),g!==f.state&&Ic.enqueueReplaceState(f,f.state,null),yi(e,i,f,s),gi(),f.state=e.memoizedState),typeof f.componentDidMount=="function"&&(e.flags|=4194308),i=!0}else if(t===null){f=e.stateNode;var b=e.memoizedProps,N=Er(a,b);f.props=N;var G=f.context,tt=a.contextType;g=Zr,typeof tt=="object"&&tt!==null&&(g=Je(tt));var rt=a.getDerivedStateFromProps;tt=typeof rt=="function"||typeof f.getSnapshotBeforeUpdate=="function",b=e.pendingProps!==b,tt||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(b||G!==g)&&xm(e,f,i,g),_a=!1;var Y=e.memoizedState;f.state=Y,yi(e,i,f,s),gi(),G=e.memoizedState,b||Y!==G||_a?(typeof rt=="function"&&(Xc(e,a,rt,i),G=e.memoizedState),(N=_a||Sm(e,a,N,i,Y,G,g))?(tt||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount()),typeof f.componentDidMount=="function"&&(e.flags|=4194308)):(typeof f.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=i,e.memoizedState=G),f.props=i,f.state=G,f.context=g,i=N):(typeof f.componentDidMount=="function"&&(e.flags|=4194308),i=!1)}else{f=e.stateNode,Rc(t,e),g=e.memoizedProps,tt=Er(a,g),f.props=tt,rt=e.pendingProps,Y=f.context,G=a.contextType,N=Zr,typeof G=="object"&&G!==null&&(N=Je(G)),b=a.getDerivedStateFromProps,(G=typeof b=="function"||typeof f.getSnapshotBeforeUpdate=="function")||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(g!==rt||Y!==N)&&xm(e,f,i,N),_a=!1,Y=e.memoizedState,f.state=Y,yi(e,i,f,s),gi();var I=e.memoizedState;g!==rt||Y!==I||_a||t!==null&&t.dependencies!==null&&Ql(t.dependencies)?(typeof b=="function"&&(Xc(e,a,b,i),I=e.memoizedState),(tt=_a||Sm(e,a,tt,i,Y,I,N)||t!==null&&t.dependencies!==null&&Ql(t.dependencies))?(G||typeof f.UNSAFE_componentWillUpdate!="function"&&typeof f.componentWillUpdate!="function"||(typeof f.componentWillUpdate=="function"&&f.componentWillUpdate(i,I,N),typeof f.UNSAFE_componentWillUpdate=="function"&&f.UNSAFE_componentWillUpdate(i,I,N)),typeof f.componentDidUpdate=="function"&&(e.flags|=4),typeof f.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof f.componentDidUpdate!="function"||g===t.memoizedProps&&Y===t.memoizedState||(e.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||g===t.memoizedProps&&Y===t.memoizedState||(e.flags|=1024),e.memoizedProps=i,e.memoizedState=I),f.props=i,f.state=I,f.context=N,i=tt):(typeof f.componentDidUpdate!="function"||g===t.memoizedProps&&Y===t.memoizedState||(e.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||g===t.memoizedProps&&Y===t.memoizedState||(e.flags|=1024),i=!1)}return f=i,ms(t,e),i=(e.flags&128)!==0,f||i?(f=e.stateNode,a=i&&typeof a.getDerivedStateFromError!="function"?null:f.render(),e.flags|=1,t!==null&&i?(e.child=lo(e,t.child,null,s),e.child=lo(e,null,a,s)):Ie(t,e,a,s),e.memoizedState=f.state,t=e.child):t=Sa(t,e,s),t}function $m(t,e,a,i){return si(),e.flags|=256,Ie(t,e,a,i),e.child}var Qc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Zc(t){return{baseLanes:t,cachePool:Th()}}function Fc(t,e,a){return t=t!==null?t.childLanes&~a:0,e&&(t|=_n),t}function _m(t,e,a){var i=e.pendingProps,s=!1,f=(e.flags&128)!==0,g;if((g=f)||(g=t!==null&&t.memoizedState===null?!1:(qe.current&2)!==0),g&&(s=!0,e.flags&=-129),g=(e.flags&32)!==0,e.flags&=-33,t===null){if(Ft){if(s?qa(e):Pa(),Ft){var b=Ae,N;if(N=b){t:{for(N=b,b=Kn;N.nodeType!==8;){if(!b){b=null;break t}if(N=In(N.nextSibling),N===null){b=null;break t}}b=N}b!==null?(e.memoizedState={dehydrated:b,treeContext:gr!==null?{id:ha,overflow:ma}:null,retryLane:536870912,hydrationErrors:null},N=yn(18,null,null,0),N.stateNode=b,N.return=e,e.child=N,an=e,Ae=null,N=!0):N=!1}N||br(e)}if(b=e.memoizedState,b!==null&&(b=b.dehydrated,b!==null))return $f(b)?e.lanes=32:e.lanes=536870912,null;ba(e)}return b=i.children,i=i.fallback,s?(Pa(),s=e.mode,b=gs({mode:"hidden",children:b},s),i=mr(i,s,a,null),b.return=e,i.return=e,b.sibling=i,e.child=b,s=e.child,s.memoizedState=Zc(a),s.childLanes=Fc(t,g,a),e.memoizedState=Qc,i):(qa(e),Jc(e,b))}if(N=t.memoizedState,N!==null&&(b=N.dehydrated,b!==null)){if(f)e.flags&256?(qa(e),e.flags&=-257,e=tf(t,e,a)):e.memoizedState!==null?(Pa(),e.child=t.child,e.flags|=128,e=null):(Pa(),s=i.fallback,b=e.mode,i=gs({mode:"visible",children:i.children},b),s=mr(s,b,a,null),s.flags|=2,i.return=e,s.return=e,i.sibling=s,e.child=i,lo(e,t.child,null,a),i=e.child,i.memoizedState=Zc(a),i.childLanes=Fc(t,g,a),e.memoizedState=Qc,e=s);else if(qa(e),$f(b)){if(g=b.nextSibling&&b.nextSibling.dataset,g)var G=g.dgst;g=G,i=Error(l(419)),i.stack="",i.digest=g,ui({value:i,source:null,stack:null}),e=tf(t,e,a)}else if(Ve||ci(t,e,a,!1),g=(a&t.childLanes)!==0,Ve||g){if(g=ve,g!==null&&(i=a&-a,i=(i&42)!==0?1:Ko(i),i=(i&(g.suspendedLanes|a))!==0?0:i,i!==0&&i!==N.retryLane))throw N.retryLane=i,Qr(t,i),Cn(g,t,i),Om;b.data==="$?"||bf(),e=tf(t,e,a)}else b.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=N.treeContext,Ae=In(b.nextSibling),an=e,Ft=!0,vr=null,Kn=!1,t!==null&&(jn[kn++]=ha,jn[kn++]=ma,jn[kn++]=gr,ha=t.id,ma=t.overflow,gr=e),e=Jc(e,i.children),e.flags|=4096);return e}return s?(Pa(),s=i.fallback,b=e.mode,N=t.child,G=N.sibling,i=pa(N,{mode:"hidden",children:i.children}),i.subtreeFlags=N.subtreeFlags&65011712,G!==null?s=pa(G,s):(s=mr(s,b,a,null),s.flags|=2),s.return=e,i.return=e,i.sibling=s,e.child=i,i=s,s=e.child,b=t.child.memoizedState,b===null?b=Zc(a):(N=b.cachePool,N!==null?(G=He._currentValue,N=N.parent!==G?{parent:G,pool:G}:N):N=Th(),b={baseLanes:b.baseLanes|a,cachePool:N}),s.memoizedState=b,s.childLanes=Fc(t,g,a),e.memoizedState=Qc,i):(qa(e),a=t.child,t=a.sibling,a=pa(a,{mode:"visible",children:i.children}),a.return=e,a.sibling=null,t!==null&&(g=e.deletions,g===null?(e.deletions=[t],e.flags|=16):g.push(t)),e.child=a,e.memoizedState=null,a)}function Jc(t,e){return e=gs({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function gs(t,e){return t=yn(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function tf(t,e,a){return lo(e,t.child,null,a),t=Jc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Um(t,e,a){t.lanes|=e;var i=t.alternate;i!==null&&(i.lanes|=e),vc(t.return,e,a)}function ef(t,e,a,i,s){var f=t.memoizedState;f===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:i,tail:a,tailMode:s}:(f.isBackwards=e,f.rendering=null,f.renderingStartTime=0,f.last=i,f.tail=a,f.tailMode=s)}function Lm(t,e,a){var i=e.pendingProps,s=i.revealOrder,f=i.tail;if(Ie(t,e,i.children,a),i=qe.current,(i&2)!==0)i=i&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Um(t,a,e);else if(t.tag===19)Um(t,a,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}i&=1}switch(nt(qe,i),s){case"forwards":for(a=e.child,s=null;a!==null;)t=a.alternate,t!==null&&ds(t)===null&&(s=a),a=a.sibling;a=s,a===null?(s=e.child,e.child=null):(s=a.sibling,a.sibling=null),ef(e,!1,s,a,f);break;case"backwards":for(a=null,s=e.child,e.child=null;s!==null;){if(t=s.alternate,t!==null&&ds(t)===null){e.child=s;break}t=s.sibling,s.sibling=a,a=s,s=t}ef(e,!0,a,null,f);break;case"together":ef(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Sa(t,e,a){if(t!==null&&(e.dependencies=t.dependencies),Ia|=e.lanes,(a&e.childLanes)===0)if(t!==null){if(ci(t,e,a,!1),(a&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(l(153));if(e.child!==null){for(t=e.child,a=pa(t,t.pendingProps),e.child=a,a.return=e;t.sibling!==null;)t=t.sibling,a=a.sibling=pa(t,t.pendingProps),a.return=e;a.sibling=null}return e.child}function nf(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Ql(t)))}function v1(t,e,a){switch(e.tag){case 3:bt(e,e.stateNode.containerInfo),$a(e,He,t.memoizedState.cache),si();break;case 27:case 5:yt(e);break;case 4:bt(e,e.stateNode.containerInfo);break;case 10:$a(e,e.type,e.memoizedProps.value);break;case 13:var i=e.memoizedState;if(i!==null)return i.dehydrated!==null?(qa(e),e.flags|=128,null):(a&e.child.childLanes)!==0?_m(t,e,a):(qa(e),t=Sa(t,e,a),t!==null?t.sibling:null);qa(e);break;case 19:var s=(t.flags&128)!==0;if(i=(a&e.childLanes)!==0,i||(ci(t,e,a,!1),i=(a&e.childLanes)!==0),s){if(i)return Lm(t,e,a);e.flags|=128}if(s=e.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),nt(qe,qe.current),i)break;return null;case 22:case 23:return e.lanes=0,Bm(t,e,a);case 24:$a(e,He,t.memoizedState.cache)}return Sa(t,e,a)}function Hm(t,e,a){if(t!==null)if(t.memoizedProps!==e.pendingProps)Ve=!0;else{if(!nf(t,a)&&(e.flags&128)===0)return Ve=!1,v1(t,e,a);Ve=(t.flags&131072)!==0}else Ve=!1,Ft&&(e.flags&1048576)!==0&&gh(e,Kl,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var i=e.elementType,s=i._init;if(i=s(i._payload),e.type=i,typeof i=="function")fc(i)?(t=Er(i,t),e.tag=1,e=km(null,e,i,t,a)):(e.tag=0,e=Kc(null,e,i,t,a));else{if(i!=null){if(s=i.$$typeof,s===O){e.tag=11,e=Mm(null,e,i,t,a);break t}else if(s===H){e.tag=14,e=zm(null,e,i,t,a);break t}}throw e=it(i)||i,Error(l(306,e,""))}}return e;case 0:return Kc(t,e,e.type,e.pendingProps,a);case 1:return i=e.type,s=Er(i,e.pendingProps),km(t,e,i,s,a);case 3:t:{if(bt(e,e.stateNode.containerInfo),t===null)throw Error(l(387));i=e.pendingProps;var f=e.memoizedState;s=f.element,Rc(t,e),yi(e,i,null,a);var g=e.memoizedState;if(i=g.cache,$a(e,He,i),i!==f.cache&&bc(e,[He],a,!0),gi(),i=g.element,f.isDehydrated)if(f={element:i,isDehydrated:!1,cache:g.cache},e.updateQueue.baseState=f,e.memoizedState=f,e.flags&256){e=$m(t,e,i,a);break t}else if(i!==s){s=Bn(Error(l(424)),e),ui(s),e=$m(t,e,i,a);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Ae=In(t.firstChild),an=e,Ft=!0,vr=null,Kn=!0,a=vm(e,null,i,a),e.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(si(),i===s){e=Sa(t,e,a);break t}Ie(t,e,i,a)}e=e.child}return e;case 26:return ms(t,e),t===null?(a=Vg(e.type,null,e.pendingProps,null))?e.memoizedState=a:Ft||(a=e.type,t=e.pendingProps,i=zs(ft.current).createElement(a),i[Fe]=e,i[ln]=t,Ke(i,a,t),Ge(i),e.stateNode=i):e.memoizedState=Vg(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return yt(e),t===null&&Ft&&(i=e.stateNode=qg(e.type,e.pendingProps,ft.current),an=e,Kn=!0,s=Ae,Za(e.type)?(_f=s,Ae=In(i.firstChild)):Ae=s),Ie(t,e,e.pendingProps.children,a),ms(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&Ft&&((s=i=Ae)&&(i=X1(i,e.type,e.pendingProps,Kn),i!==null?(e.stateNode=i,an=e,Ae=In(i.firstChild),Kn=!1,s=!0):s=!1),s||br(e)),yt(e),s=e.type,f=e.pendingProps,g=t!==null?t.memoizedProps:null,i=f.children,Nf(s,f)?i=null:g!==null&&Nf(s,g)&&(e.flags|=32),e.memoizedState!==null&&(s=Dc(t,e,c1,null,null,a),Li._currentValue=s),ms(t,e),Ie(t,e,i,a),e.child;case 6:return t===null&&Ft&&((t=a=Ae)&&(a=I1(a,e.pendingProps,Kn),a!==null?(e.stateNode=a,an=e,Ae=null,t=!0):t=!1),t||br(e)),null;case 13:return _m(t,e,a);case 4:return bt(e,e.stateNode.containerInfo),i=e.pendingProps,t===null?e.child=lo(e,null,i,a):Ie(t,e,i,a),e.child;case 11:return Mm(t,e,e.type,e.pendingProps,a);case 7:return Ie(t,e,e.pendingProps,a),e.child;case 8:return Ie(t,e,e.pendingProps.children,a),e.child;case 12:return Ie(t,e,e.pendingProps.children,a),e.child;case 10:return i=e.pendingProps,$a(e,e.type,i.value),Ie(t,e,i.children,a),e.child;case 9:return s=e.type._context,i=e.pendingProps.children,xr(e),s=Je(s),i=i(s),e.flags|=1,Ie(t,e,i,a),e.child;case 14:return zm(t,e,e.type,e.pendingProps,a);case 15:return Dm(t,e,e.type,e.pendingProps,a);case 19:return Lm(t,e,a);case 31:return i=e.pendingProps,a=e.mode,i={mode:i.mode,children:i.children},t===null?(a=gs(i,a),a.ref=e.ref,e.child=a,a.return=e,e=a):(a=pa(t.child,i),a.ref=e.ref,e.child=a,a.return=e,e=a),e;case 22:return Bm(t,e,a);case 24:return xr(e),i=Je(He),t===null?(s=Cc(),s===null&&(s=ve,f=Sc(),s.pooledCache=f,f.refCount++,f!==null&&(s.pooledCacheLanes|=a),s=f),e.memoizedState={parent:i,cache:s},Ec(e),$a(e,He,s)):((t.lanes&a)!==0&&(Rc(t,e),yi(e,null,null,a),gi()),s=t.memoizedState,f=e.memoizedState,s.parent!==i?(s={parent:i,cache:i},e.memoizedState=s,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=s),$a(e,He,i)):(i=f.cache,$a(e,He,i),i!==s.cache&&bc(e,[He],a,!0))),Ie(t,e,e.pendingProps.children,a),e.child;case 29:throw e.pendingProps}throw Error(l(156,e.tag))}function xa(t){t.flags|=4}function qm(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Kg(e)){if(e=$n.current,e!==null&&((It&4194048)===It?Qn!==null:(It&62914560)!==It&&(It&536870912)===0||e!==Qn))throw hi=Tc,Eh;t.flags|=8192}}function ys(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Ml():536870912,t.lanes|=e,fo|=e)}function Ei(t,e){if(!Ft)switch(t.tailMode){case"hidden":e=t.tail;for(var a=null;e!==null;)e.alternate!==null&&(a=e),e=e.sibling;a===null?t.tail=null:a.sibling=null;break;case"collapsed":a=t.tail;for(var i=null;a!==null;)a.alternate!==null&&(i=a),a=a.sibling;i===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:i.sibling=null}}function Re(t){var e=t.alternate!==null&&t.alternate.child===t.child,a=0,i=0;if(e)for(var s=t.child;s!==null;)a|=s.lanes|s.childLanes,i|=s.subtreeFlags&65011712,i|=s.flags&65011712,s.return=t,s=s.sibling;else for(s=t.child;s!==null;)a|=s.lanes|s.childLanes,i|=s.subtreeFlags,i|=s.flags,s.return=t,s=s.sibling;return t.subtreeFlags|=i,t.childLanes=a,e}function b1(t,e,a){var i=e.pendingProps;switch(mc(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Re(e),null;case 1:return Re(e),null;case 3:return a=e.stateNode,i=null,t!==null&&(i=t.memoizedState.cache),e.memoizedState.cache!==i&&(e.flags|=2048),ya(He),At(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(t===null||t.child===null)&&(li(e)?xa(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,bh())),Re(e),null;case 26:return a=e.memoizedState,t===null?(xa(e),a!==null?(Re(e),qm(e,a)):(Re(e),e.flags&=-16777217)):a?a!==t.memoizedState?(xa(e),Re(e),qm(e,a)):(Re(e),e.flags&=-16777217):(t.memoizedProps!==i&&xa(e),Re(e),e.flags&=-16777217),null;case 27:wt(e),a=ft.current;var s=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==i&&xa(e);else{if(!i){if(e.stateNode===null)throw Error(l(166));return Re(e),null}t=lt.current,li(e)?yh(e):(t=qg(s,i,a),e.stateNode=t,xa(e))}return Re(e),null;case 5:if(wt(e),a=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==i&&xa(e);else{if(!i){if(e.stateNode===null)throw Error(l(166));return Re(e),null}if(t=lt.current,li(e))yh(e);else{switch(s=zs(ft.current),t){case 1:t=s.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:t=s.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":t=s.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":t=s.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":t=s.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof i.is=="string"?s.createElement("select",{is:i.is}):s.createElement("select"),i.multiple?t.multiple=!0:i.size&&(t.size=i.size);break;default:t=typeof i.is=="string"?s.createElement(a,{is:i.is}):s.createElement(a)}}t[Fe]=e,t[ln]=i;t:for(s=e.child;s!==null;){if(s.tag===5||s.tag===6)t.appendChild(s.stateNode);else if(s.tag!==4&&s.tag!==27&&s.child!==null){s.child.return=s,s=s.child;continue}if(s===e)break t;for(;s.sibling===null;){if(s.return===null||s.return===e)break t;s=s.return}s.sibling.return=s.return,s=s.sibling}e.stateNode=t;t:switch(Ke(t,a,i),a){case"button":case"input":case"select":case"textarea":t=!!i.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&xa(e)}}return Re(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==i&&xa(e);else{if(typeof i!="string"&&e.stateNode===null)throw Error(l(166));if(t=ft.current,li(e)){if(t=e.stateNode,a=e.memoizedProps,i=null,s=an,s!==null)switch(s.tag){case 27:case 5:i=s.memoizedProps}t[Fe]=e,t=!!(t.nodeValue===a||i!==null&&i.suppressHydrationWarning===!0||jg(t.nodeValue,a)),t||br(e)}else t=zs(t).createTextNode(i),t[Fe]=e,e.stateNode=t}return Re(e),null;case 13:if(i=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(s=li(e),i!==null&&i.dehydrated!==null){if(t===null){if(!s)throw Error(l(318));if(s=e.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(l(317));s[Fe]=e}else si(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Re(e),s=!1}else s=bh(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=s),s=!0;if(!s)return e.flags&256?(ba(e),e):(ba(e),null)}if(ba(e),(e.flags&128)!==0)return e.lanes=a,e;if(a=i!==null,t=t!==null&&t.memoizedState!==null,a){i=e.child,s=null,i.alternate!==null&&i.alternate.memoizedState!==null&&i.alternate.memoizedState.cachePool!==null&&(s=i.alternate.memoizedState.cachePool.pool);var f=null;i.memoizedState!==null&&i.memoizedState.cachePool!==null&&(f=i.memoizedState.cachePool.pool),f!==s&&(i.flags|=2048)}return a!==t&&a&&(e.child.flags|=8192),ys(e,e.updateQueue),Re(e),null;case 4:return At(),t===null&&Of(e.stateNode.containerInfo),Re(e),null;case 10:return ya(e.type),Re(e),null;case 19:if(et(qe),s=e.memoizedState,s===null)return Re(e),null;if(i=(e.flags&128)!==0,f=s.rendering,f===null)if(i)Ei(s,!1);else{if(Oe!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(f=ds(t),f!==null){for(e.flags|=128,Ei(s,!1),t=f.updateQueue,e.updateQueue=t,ys(e,t),e.subtreeFlags=0,t=a,a=e.child;a!==null;)mh(a,t),a=a.sibling;return nt(qe,qe.current&1|2),e.child}t=t.sibling}s.tail!==null&&Lt()>Ss&&(e.flags|=128,i=!0,Ei(s,!1),e.lanes=4194304)}else{if(!i)if(t=ds(f),t!==null){if(e.flags|=128,i=!0,t=t.updateQueue,e.updateQueue=t,ys(e,t),Ei(s,!0),s.tail===null&&s.tailMode==="hidden"&&!f.alternate&&!Ft)return Re(e),null}else 2*Lt()-s.renderingStartTime>Ss&&a!==536870912&&(e.flags|=128,i=!0,Ei(s,!1),e.lanes=4194304);s.isBackwards?(f.sibling=e.child,e.child=f):(t=s.last,t!==null?t.sibling=f:e.child=f,s.last=f)}return s.tail!==null?(e=s.tail,s.rendering=e,s.tail=e.sibling,s.renderingStartTime=Lt(),e.sibling=null,t=qe.current,nt(qe,i?t&1|2:t&1),e):(Re(e),null);case 22:case 23:return ba(e),Mc(),i=e.memoizedState!==null,t!==null?t.memoizedState!==null!==i&&(e.flags|=8192):i&&(e.flags|=8192),i?(a&536870912)!==0&&(e.flags&128)===0&&(Re(e),e.subtreeFlags&6&&(e.flags|=8192)):Re(e),a=e.updateQueue,a!==null&&ys(e,a.retryQueue),a=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),i=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(i=e.memoizedState.cachePool.pool),i!==a&&(e.flags|=2048),t!==null&&et(Cr),null;case 24:return a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),ya(He),Re(e),null;case 25:return null;case 30:return null}throw Error(l(156,e.tag))}function S1(t,e){switch(mc(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return ya(He),At(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return wt(e),null;case 13:if(ba(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(l(340));si()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return et(qe),null;case 4:return At(),null;case 10:return ya(e.type),null;case 22:case 23:return ba(e),Mc(),t!==null&&et(Cr),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return ya(He),null;case 25:return null;default:return null}}function Pm(t,e){switch(mc(e),e.tag){case 3:ya(He),At();break;case 26:case 27:case 5:wt(e);break;case 4:At();break;case 13:ba(e);break;case 19:et(qe);break;case 10:ya(e.type);break;case 22:case 23:ba(e),Mc(),t!==null&&et(Cr);break;case 24:ya(He)}}function Ri(t,e){try{var a=e.updateQueue,i=a!==null?a.lastEffect:null;if(i!==null){var s=i.next;a=s;do{if((a.tag&t)===t){i=void 0;var f=a.create,g=a.inst;i=f(),g.destroy=i}a=a.next}while(a!==s)}}catch(b){he(e,e.return,b)}}function Ga(t,e,a){try{var i=e.updateQueue,s=i!==null?i.lastEffect:null;if(s!==null){var f=s.next;i=f;do{if((i.tag&t)===t){var g=i.inst,b=g.destroy;if(b!==void 0){g.destroy=void 0,s=e;var N=a,G=b;try{G()}catch(tt){he(s,N,tt)}}}i=i.next}while(i!==f)}}catch(tt){he(e,e.return,tt)}}function Gm(t){var e=t.updateQueue;if(e!==null){var a=t.stateNode;try{zh(e,a)}catch(i){he(t,t.return,i)}}}function Vm(t,e,a){a.props=Er(t.type,t.memoizedProps),a.state=t.memoizedState;try{a.componentWillUnmount()}catch(i){he(t,e,i)}}function wi(t,e){try{var a=t.ref;if(a!==null){switch(t.tag){case 26:case 27:case 5:var i=t.stateNode;break;case 30:i=t.stateNode;break;default:i=t.stateNode}typeof a=="function"?t.refCleanup=a(i):a.current=i}}catch(s){he(t,e,s)}}function Zn(t,e){var a=t.ref,i=t.refCleanup;if(a!==null)if(typeof i=="function")try{i()}catch(s){he(t,e,s)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(s){he(t,e,s)}else a.current=null}function Ym(t){var e=t.type,a=t.memoizedProps,i=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":a.autoFocus&&i.focus();break t;case"img":a.src?i.src=a.src:a.srcSet&&(i.srcset=a.srcSet)}}catch(s){he(t,t.return,s)}}function af(t,e,a){try{var i=t.stateNode;q1(i,t.type,a,e),i[ln]=e}catch(s){he(t,t.return,s)}}function Xm(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Za(t.type)||t.tag===4}function rf(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Xm(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Za(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function of(t,e,a){var i=t.tag;if(i===5||i===6)t=t.stateNode,e?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(t,e):(e=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,e.appendChild(t),a=a._reactRootContainer,a!=null||e.onclick!==null||(e.onclick=Ms));else if(i!==4&&(i===27&&Za(t.type)&&(a=t.stateNode,e=null),t=t.child,t!==null))for(of(t,e,a),t=t.sibling;t!==null;)of(t,e,a),t=t.sibling}function vs(t,e,a){var i=t.tag;if(i===5||i===6)t=t.stateNode,e?a.insertBefore(t,e):a.appendChild(t);else if(i!==4&&(i===27&&Za(t.type)&&(a=t.stateNode),t=t.child,t!==null))for(vs(t,e,a),t=t.sibling;t!==null;)vs(t,e,a),t=t.sibling}function Im(t){var e=t.stateNode,a=t.memoizedProps;try{for(var i=t.type,s=e.attributes;s.length;)e.removeAttributeNode(s[0]);Ke(e,i,a),e[Fe]=t,e[ln]=a}catch(f){he(t,t.return,f)}}var Ca=!1,Ne=!1,lf=!1,Wm=typeof WeakSet=="function"?WeakSet:Set,Ye=null;function x1(t,e){if(t=t.containerInfo,Df=$s,t=oh(t),rc(t)){if("selectionStart"in t)var a={start:t.selectionStart,end:t.selectionEnd};else t:{a=(a=t.ownerDocument)&&a.defaultView||window;var i=a.getSelection&&a.getSelection();if(i&&i.rangeCount!==0){a=i.anchorNode;var s=i.anchorOffset,f=i.focusNode;i=i.focusOffset;try{a.nodeType,f.nodeType}catch{a=null;break t}var g=0,b=-1,N=-1,G=0,tt=0,rt=t,Y=null;e:for(;;){for(var I;rt!==a||s!==0&&rt.nodeType!==3||(b=g+s),rt!==f||i!==0&&rt.nodeType!==3||(N=g+i),rt.nodeType===3&&(g+=rt.nodeValue.length),(I=rt.firstChild)!==null;)Y=rt,rt=I;for(;;){if(rt===t)break e;if(Y===a&&++G===s&&(b=g),Y===f&&++tt===i&&(N=g),(I=rt.nextSibling)!==null)break;rt=Y,Y=rt.parentNode}rt=I}a=b===-1||N===-1?null:{start:b,end:N}}else a=null}a=a||{start:0,end:0}}else a=null;for(Bf={focusedElem:t,selectionRange:a},$s=!1,Ye=e;Ye!==null;)if(e=Ye,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Ye=t;else for(;Ye!==null;){switch(e=Ye,f=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&f!==null){t=void 0,a=e,s=f.memoizedProps,f=f.memoizedState,i=a.stateNode;try{var Rt=Er(a.type,s,a.elementType===a.type);t=i.getSnapshotBeforeUpdate(Rt,f),i.__reactInternalSnapshotBeforeUpdate=t}catch(xt){he(a,a.return,xt)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,a=t.nodeType,a===9)kf(t);else if(a===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":kf(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(l(163))}if(t=e.sibling,t!==null){t.return=e.return,Ye=t;break}Ye=e.return}}function Km(t,e,a){var i=a.flags;switch(a.tag){case 0:case 11:case 15:Va(t,a),i&4&&Ri(5,a);break;case 1:if(Va(t,a),i&4)if(t=a.stateNode,e===null)try{t.componentDidMount()}catch(g){he(a,a.return,g)}else{var s=Er(a.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(s,e,t.__reactInternalSnapshotBeforeUpdate)}catch(g){he(a,a.return,g)}}i&64&&Gm(a),i&512&&wi(a,a.return);break;case 3:if(Va(t,a),i&64&&(t=a.updateQueue,t!==null)){if(e=null,a.child!==null)switch(a.child.tag){case 27:case 5:e=a.child.stateNode;break;case 1:e=a.child.stateNode}try{zh(t,e)}catch(g){he(a,a.return,g)}}break;case 27:e===null&&i&4&&Im(a);case 26:case 5:Va(t,a),e===null&&i&4&&Ym(a),i&512&&wi(a,a.return);break;case 12:Va(t,a);break;case 13:Va(t,a),i&4&&Fm(t,a),i&64&&(t=a.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(a=z1.bind(null,a),W1(t,a))));break;case 22:if(i=a.memoizedState!==null||Ca,!i){e=e!==null&&e.memoizedState!==null||Ne,s=Ca;var f=Ne;Ca=i,(Ne=e)&&!f?Ya(t,a,(a.subtreeFlags&8772)!==0):Va(t,a),Ca=s,Ne=f}break;case 30:break;default:Va(t,a)}}function Qm(t){var e=t.alternate;e!==null&&(t.alternate=null,Qm(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Lu(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var xe=null,cn=!1;function Ta(t,e,a){for(a=a.child;a!==null;)Zm(t,e,a),a=a.sibling}function Zm(t,e,a){if(ne&&typeof ne.onCommitFiberUnmount=="function")try{ne.onCommitFiberUnmount(Le,a)}catch{}switch(a.tag){case 26:Ne||Zn(a,e),Ta(t,e,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Ne||Zn(a,e);var i=xe,s=cn;Za(a.type)&&(xe=a.stateNode,cn=!1),Ta(t,e,a),ki(a.stateNode),xe=i,cn=s;break;case 5:Ne||Zn(a,e);case 6:if(i=xe,s=cn,xe=null,Ta(t,e,a),xe=i,cn=s,xe!==null)if(cn)try{(xe.nodeType===9?xe.body:xe.nodeName==="HTML"?xe.ownerDocument.body:xe).removeChild(a.stateNode)}catch(f){he(a,e,f)}else try{xe.removeChild(a.stateNode)}catch(f){he(a,e,f)}break;case 18:xe!==null&&(cn?(t=xe,Lg(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,a.stateNode),Gi(t)):Lg(xe,a.stateNode));break;case 4:i=xe,s=cn,xe=a.stateNode.containerInfo,cn=!0,Ta(t,e,a),xe=i,cn=s;break;case 0:case 11:case 14:case 15:Ne||Ga(2,a,e),Ne||Ga(4,a,e),Ta(t,e,a);break;case 1:Ne||(Zn(a,e),i=a.stateNode,typeof i.componentWillUnmount=="function"&&Vm(a,e,i)),Ta(t,e,a);break;case 21:Ta(t,e,a);break;case 22:Ne=(i=Ne)||a.memoizedState!==null,Ta(t,e,a),Ne=i;break;default:Ta(t,e,a)}}function Fm(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Gi(t)}catch(a){he(e,e.return,a)}}function C1(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Wm),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Wm),e;default:throw Error(l(435,t.tag))}}function sf(t,e){var a=C1(t);e.forEach(function(i){var s=D1.bind(null,t,i);a.has(i)||(a.add(i),i.then(s,s))})}function vn(t,e){var a=e.deletions;if(a!==null)for(var i=0;i<a.length;i++){var s=a[i],f=t,g=e,b=g;t:for(;b!==null;){switch(b.tag){case 27:if(Za(b.type)){xe=b.stateNode,cn=!1;break t}break;case 5:xe=b.stateNode,cn=!1;break t;case 3:case 4:xe=b.stateNode.containerInfo,cn=!0;break t}b=b.return}if(xe===null)throw Error(l(160));Zm(f,g,s),xe=null,cn=!1,f=s.alternate,f!==null&&(f.return=null),s.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Jm(e,t),e=e.sibling}var Xn=null;function Jm(t,e){var a=t.alternate,i=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:vn(e,t),bn(t),i&4&&(Ga(3,t,t.return),Ri(3,t),Ga(5,t,t.return));break;case 1:vn(e,t),bn(t),i&512&&(Ne||a===null||Zn(a,a.return)),i&64&&Ca&&(t=t.updateQueue,t!==null&&(i=t.callbacks,i!==null&&(a=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=a===null?i:a.concat(i))));break;case 26:var s=Xn;if(vn(e,t),bn(t),i&512&&(Ne||a===null||Zn(a,a.return)),i&4){var f=a!==null?a.memoizedState:null;if(i=t.memoizedState,a===null)if(i===null)if(t.stateNode===null){t:{i=t.type,a=t.memoizedProps,s=s.ownerDocument||s;e:switch(i){case"title":f=s.getElementsByTagName("title")[0],(!f||f[Qo]||f[Fe]||f.namespaceURI==="http://www.w3.org/2000/svg"||f.hasAttribute("itemprop"))&&(f=s.createElement(i),s.head.insertBefore(f,s.querySelector("head > title"))),Ke(f,i,a),f[Fe]=t,Ge(f),i=f;break t;case"link":var g=Ig("link","href",s).get(i+(a.href||""));if(g){for(var b=0;b<g.length;b++)if(f=g[b],f.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&f.getAttribute("rel")===(a.rel==null?null:a.rel)&&f.getAttribute("title")===(a.title==null?null:a.title)&&f.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){g.splice(b,1);break e}}f=s.createElement(i),Ke(f,i,a),s.head.appendChild(f);break;case"meta":if(g=Ig("meta","content",s).get(i+(a.content||""))){for(b=0;b<g.length;b++)if(f=g[b],f.getAttribute("content")===(a.content==null?null:""+a.content)&&f.getAttribute("name")===(a.name==null?null:a.name)&&f.getAttribute("property")===(a.property==null?null:a.property)&&f.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&f.getAttribute("charset")===(a.charSet==null?null:a.charSet)){g.splice(b,1);break e}}f=s.createElement(i),Ke(f,i,a),s.head.appendChild(f);break;default:throw Error(l(468,i))}f[Fe]=t,Ge(f),i=f}t.stateNode=i}else Wg(s,t.type,t.stateNode);else t.stateNode=Xg(s,i,t.memoizedProps);else f!==i?(f===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):f.count--,i===null?Wg(s,t.type,t.stateNode):Xg(s,i,t.memoizedProps)):i===null&&t.stateNode!==null&&af(t,t.memoizedProps,a.memoizedProps)}break;case 27:vn(e,t),bn(t),i&512&&(Ne||a===null||Zn(a,a.return)),a!==null&&i&4&&af(t,t.memoizedProps,a.memoizedProps);break;case 5:if(vn(e,t),bn(t),i&512&&(Ne||a===null||Zn(a,a.return)),t.flags&32){s=t.stateNode;try{Gr(s,"")}catch(I){he(t,t.return,I)}}i&4&&t.stateNode!=null&&(s=t.memoizedProps,af(t,s,a!==null?a.memoizedProps:s)),i&1024&&(lf=!0);break;case 6:if(vn(e,t),bn(t),i&4){if(t.stateNode===null)throw Error(l(162));i=t.memoizedProps,a=t.stateNode;try{a.nodeValue=i}catch(I){he(t,t.return,I)}}break;case 3:if(Ns=null,s=Xn,Xn=Ds(e.containerInfo),vn(e,t),Xn=s,bn(t),i&4&&a!==null&&a.memoizedState.isDehydrated)try{Gi(e.containerInfo)}catch(I){he(t,t.return,I)}lf&&(lf=!1,tg(t));break;case 4:i=Xn,Xn=Ds(t.stateNode.containerInfo),vn(e,t),bn(t),Xn=i;break;case 12:vn(e,t),bn(t);break;case 13:vn(e,t),bn(t),t.child.flags&8192&&t.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(hf=Lt()),i&4&&(i=t.updateQueue,i!==null&&(t.updateQueue=null,sf(t,i)));break;case 22:s=t.memoizedState!==null;var N=a!==null&&a.memoizedState!==null,G=Ca,tt=Ne;if(Ca=G||s,Ne=tt||N,vn(e,t),Ne=tt,Ca=G,bn(t),i&8192)t:for(e=t.stateNode,e._visibility=s?e._visibility&-2:e._visibility|1,s&&(a===null||N||Ca||Ne||Rr(t)),a=null,e=t;;){if(e.tag===5||e.tag===26){if(a===null){N=a=e;try{if(f=N.stateNode,s)g=f.style,typeof g.setProperty=="function"?g.setProperty("display","none","important"):g.display="none";else{b=N.stateNode;var rt=N.memoizedProps.style,Y=rt!=null&&rt.hasOwnProperty("display")?rt.display:null;b.style.display=Y==null||typeof Y=="boolean"?"":(""+Y).trim()}}catch(I){he(N,N.return,I)}}}else if(e.tag===6){if(a===null){N=e;try{N.stateNode.nodeValue=s?"":N.memoizedProps}catch(I){he(N,N.return,I)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;a===e&&(a=null),e=e.return}a===e&&(a=null),e.sibling.return=e.return,e=e.sibling}i&4&&(i=t.updateQueue,i!==null&&(a=i.retryQueue,a!==null&&(i.retryQueue=null,sf(t,a))));break;case 19:vn(e,t),bn(t),i&4&&(i=t.updateQueue,i!==null&&(t.updateQueue=null,sf(t,i)));break;case 30:break;case 21:break;default:vn(e,t),bn(t)}}function bn(t){var e=t.flags;if(e&2){try{for(var a,i=t.return;i!==null;){if(Xm(i)){a=i;break}i=i.return}if(a==null)throw Error(l(160));switch(a.tag){case 27:var s=a.stateNode,f=rf(t);vs(t,f,s);break;case 5:var g=a.stateNode;a.flags&32&&(Gr(g,""),a.flags&=-33);var b=rf(t);vs(t,b,g);break;case 3:case 4:var N=a.stateNode.containerInfo,G=rf(t);of(t,G,N);break;default:throw Error(l(161))}}catch(tt){he(t,t.return,tt)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function tg(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;tg(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function Va(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Km(t,e.alternate,e),e=e.sibling}function Rr(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:Ga(4,e,e.return),Rr(e);break;case 1:Zn(e,e.return);var a=e.stateNode;typeof a.componentWillUnmount=="function"&&Vm(e,e.return,a),Rr(e);break;case 27:ki(e.stateNode);case 26:case 5:Zn(e,e.return),Rr(e);break;case 22:e.memoizedState===null&&Rr(e);break;case 30:Rr(e);break;default:Rr(e)}t=t.sibling}}function Ya(t,e,a){for(a=a&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var i=e.alternate,s=t,f=e,g=f.flags;switch(f.tag){case 0:case 11:case 15:Ya(s,f,a),Ri(4,f);break;case 1:if(Ya(s,f,a),i=f,s=i.stateNode,typeof s.componentDidMount=="function")try{s.componentDidMount()}catch(G){he(i,i.return,G)}if(i=f,s=i.updateQueue,s!==null){var b=i.stateNode;try{var N=s.shared.hiddenCallbacks;if(N!==null)for(s.shared.hiddenCallbacks=null,s=0;s<N.length;s++)Mh(N[s],b)}catch(G){he(i,i.return,G)}}a&&g&64&&Gm(f),wi(f,f.return);break;case 27:Im(f);case 26:case 5:Ya(s,f,a),a&&i===null&&g&4&&Ym(f),wi(f,f.return);break;case 12:Ya(s,f,a);break;case 13:Ya(s,f,a),a&&g&4&&Fm(s,f);break;case 22:f.memoizedState===null&&Ya(s,f,a),wi(f,f.return);break;case 30:break;default:Ya(s,f,a)}e=e.sibling}}function uf(t,e){var a=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==a&&(t!=null&&t.refCount++,a!=null&&fi(a))}function cf(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&fi(t))}function Fn(t,e,a,i){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)eg(t,e,a,i),e=e.sibling}function eg(t,e,a,i){var s=e.flags;switch(e.tag){case 0:case 11:case 15:Fn(t,e,a,i),s&2048&&Ri(9,e);break;case 1:Fn(t,e,a,i);break;case 3:Fn(t,e,a,i),s&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&fi(t)));break;case 12:if(s&2048){Fn(t,e,a,i),t=e.stateNode;try{var f=e.memoizedProps,g=f.id,b=f.onPostCommit;typeof b=="function"&&b(g,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(N){he(e,e.return,N)}}else Fn(t,e,a,i);break;case 13:Fn(t,e,a,i);break;case 23:break;case 22:f=e.stateNode,g=e.alternate,e.memoizedState!==null?f._visibility&2?Fn(t,e,a,i):Ai(t,e):f._visibility&2?Fn(t,e,a,i):(f._visibility|=2,so(t,e,a,i,(e.subtreeFlags&10256)!==0)),s&2048&&uf(g,e);break;case 24:Fn(t,e,a,i),s&2048&&cf(e.alternate,e);break;default:Fn(t,e,a,i)}}function so(t,e,a,i,s){for(s=s&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var f=t,g=e,b=a,N=i,G=g.flags;switch(g.tag){case 0:case 11:case 15:so(f,g,b,N,s),Ri(8,g);break;case 23:break;case 22:var tt=g.stateNode;g.memoizedState!==null?tt._visibility&2?so(f,g,b,N,s):Ai(f,g):(tt._visibility|=2,so(f,g,b,N,s)),s&&G&2048&&uf(g.alternate,g);break;case 24:so(f,g,b,N,s),s&&G&2048&&cf(g.alternate,g);break;default:so(f,g,b,N,s)}e=e.sibling}}function Ai(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var a=t,i=e,s=i.flags;switch(i.tag){case 22:Ai(a,i),s&2048&&uf(i.alternate,i);break;case 24:Ai(a,i),s&2048&&cf(i.alternate,i);break;default:Ai(a,i)}e=e.sibling}}var Oi=8192;function uo(t){if(t.subtreeFlags&Oi)for(t=t.child;t!==null;)ng(t),t=t.sibling}function ng(t){switch(t.tag){case 26:uo(t),t.flags&Oi&&t.memoizedState!==null&&lS(Xn,t.memoizedState,t.memoizedProps);break;case 5:uo(t);break;case 3:case 4:var e=Xn;Xn=Ds(t.stateNode.containerInfo),uo(t),Xn=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Oi,Oi=16777216,uo(t),Oi=e):uo(t));break;default:uo(t)}}function ag(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Mi(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var a=0;a<e.length;a++){var i=e[a];Ye=i,og(i,t)}ag(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)rg(t),t=t.sibling}function rg(t){switch(t.tag){case 0:case 11:case 15:Mi(t),t.flags&2048&&Ga(9,t,t.return);break;case 3:Mi(t);break;case 12:Mi(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,bs(t)):Mi(t);break;default:Mi(t)}}function bs(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var a=0;a<e.length;a++){var i=e[a];Ye=i,og(i,t)}ag(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:Ga(8,e,e.return),bs(e);break;case 22:a=e.stateNode,a._visibility&2&&(a._visibility&=-3,bs(e));break;default:bs(e)}t=t.sibling}}function og(t,e){for(;Ye!==null;){var a=Ye;switch(a.tag){case 0:case 11:case 15:Ga(8,a,e);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var i=a.memoizedState.cachePool.pool;i!=null&&i.refCount++}break;case 24:fi(a.memoizedState.cache)}if(i=a.child,i!==null)i.return=a,Ye=i;else t:for(a=t;Ye!==null;){i=Ye;var s=i.sibling,f=i.return;if(Qm(i),i===a){Ye=null;break t}if(s!==null){s.return=f,Ye=s;break t}Ye=f}}}var T1={getCacheForType:function(t){var e=Je(He),a=e.data.get(t);return a===void 0&&(a=t(),e.data.set(t,a)),a}},E1=typeof WeakMap=="function"?WeakMap:Map,oe=0,ve=null,Ht=null,It=0,ie=0,Sn=null,Xa=!1,co=!1,ff=!1,Ea=0,Oe=0,Ia=0,wr=0,df=0,_n=0,fo=0,zi=null,fn=null,pf=!1,hf=0,Ss=1/0,xs=null,Wa=null,We=0,Ka=null,po=null,ho=0,mf=0,gf=null,ig=null,Di=0,yf=null;function xn(){if((oe&2)!==0&&It!==0)return It&-It;if(B.T!==null){var t=to;return t!==0?t:Ef()}return on()}function lg(){_n===0&&(_n=(It&536870912)===0||Ft?ur():536870912);var t=$n.current;return t!==null&&(t.flags|=32),_n}function Cn(t,e,a){(t===ve&&(ie===2||ie===9)||t.cancelPendingCommit!==null)&&(mo(t,0),Qa(t,It,_n,!1)),cr(t,a),((oe&2)===0||t!==ve)&&(t===ve&&((oe&2)===0&&(wr|=a),Oe===4&&Qa(t,It,_n,!1)),Jn(t))}function sg(t,e,a){if((oe&6)!==0)throw Error(l(327));var i=!a&&(e&124)===0&&(e&t.expiredLanes)===0||be(t,e),s=i?A1(t,e):Sf(t,e,!0),f=i;do{if(s===0){co&&!i&&Qa(t,e,0,!1);break}else{if(a=t.current.alternate,f&&!R1(a)){s=Sf(t,e,!1),f=!1;continue}if(s===2){if(f=e,t.errorRecoveryDisabledLanes&f)var g=0;else g=t.pendingLanes&-536870913,g=g!==0?g:g&536870912?536870912:0;if(g!==0){e=g;t:{var b=t;s=zi;var N=b.current.memoizedState.isDehydrated;if(N&&(mo(b,g).flags|=256),g=Sf(b,g,!1),g!==2){if(ff&&!N){b.errorRecoveryDisabledLanes|=f,wr|=f,s=4;break t}f=fn,fn=s,f!==null&&(fn===null?fn=f:fn.push.apply(fn,f))}s=g}if(f=!1,s!==2)continue}}if(s===1){mo(t,0),Qa(t,e,0,!0);break}t:{switch(i=t,f=s,f){case 0:case 1:throw Error(l(345));case 4:if((e&4194048)!==e)break;case 6:Qa(i,e,_n,!Xa);break t;case 2:fn=null;break;case 3:case 5:break;default:throw Error(l(329))}if((e&62914560)===e&&(s=hf+300-Lt(),10<s)){if(Qa(i,e,_n,!Xa),Xt(i,0,!0)!==0)break t;i.timeoutHandle=_g(ug.bind(null,i,a,fn,xs,pf,e,_n,wr,fo,Xa,f,2,-0,0),s);break t}ug(i,a,fn,xs,pf,e,_n,wr,fo,Xa,f,0,-0,0)}}break}while(!0);Jn(t)}function ug(t,e,a,i,s,f,g,b,N,G,tt,rt,Y,I){if(t.timeoutHandle=-1,rt=e.subtreeFlags,(rt&8192||(rt&16785408)===16785408)&&(Ui={stylesheets:null,count:0,unsuspend:iS},ng(e),rt=sS(),rt!==null)){t.cancelPendingCommit=rt(gg.bind(null,t,e,f,a,i,s,g,b,N,tt,1,Y,I)),Qa(t,f,g,!G);return}gg(t,e,f,a,i,s,g,b,N)}function R1(t){for(var e=t;;){var a=e.tag;if((a===0||a===11||a===15)&&e.flags&16384&&(a=e.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var i=0;i<a.length;i++){var s=a[i],f=s.getSnapshot;s=s.value;try{if(!gn(f(),s))return!1}catch{return!1}}if(a=e.child,e.subtreeFlags&16384&&a!==null)a.return=e,e=a;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Qa(t,e,a,i){e&=~df,e&=~wr,t.suspendedLanes|=e,t.pingedLanes&=~e,i&&(t.warmLanes|=e),i=t.expirationTimes;for(var s=e;0<s;){var f=31-ae(s),g=1<<f;i[f]=-1,s&=~g}a!==0&&zl(t,a,e)}function Cs(){return(oe&6)===0?(Bi(0),!1):!0}function vf(){if(Ht!==null){if(ie===0)var t=Ht.return;else t=Ht,ga=Sr=null,jc(t),io=null,Ci=0,t=Ht;for(;t!==null;)Pm(t.alternate,t),t=t.return;Ht=null}}function mo(t,e){var a=t.timeoutHandle;a!==-1&&(t.timeoutHandle=-1,G1(a)),a=t.cancelPendingCommit,a!==null&&(t.cancelPendingCommit=null,a()),vf(),ve=t,Ht=a=pa(t.current,null),It=e,ie=0,Sn=null,Xa=!1,co=be(t,e),ff=!1,fo=_n=df=wr=Ia=Oe=0,fn=zi=null,pf=!1,(e&8)!==0&&(e|=e&32);var i=t.entangledLanes;if(i!==0)for(t=t.entanglements,i&=e;0<i;){var s=31-ae(i),f=1<<s;e|=t[s],i&=~f}return Ea=e,Vl(),a}function cg(t,e){kt=null,B.H=us,e===pi||e===Jl?(e=Ah(),ie=3):e===Eh?(e=Ah(),ie=4):ie=e===Om?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,Sn=e,Ht===null&&(Oe=1,hs(t,Bn(e,t.current)))}function fg(){var t=B.H;return B.H=us,t===null?us:t}function dg(){var t=B.A;return B.A=T1,t}function bf(){Oe=4,Xa||(It&4194048)!==It&&$n.current!==null||(co=!0),(Ia&134217727)===0&&(wr&134217727)===0||ve===null||Qa(ve,It,_n,!1)}function Sf(t,e,a){var i=oe;oe|=2;var s=fg(),f=dg();(ve!==t||It!==e)&&(xs=null,mo(t,e)),e=!1;var g=Oe;t:do try{if(ie!==0&&Ht!==null){var b=Ht,N=Sn;switch(ie){case 8:vf(),g=6;break t;case 3:case 2:case 9:case 6:$n.current===null&&(e=!0);var G=ie;if(ie=0,Sn=null,go(t,b,N,G),a&&co){g=0;break t}break;default:G=ie,ie=0,Sn=null,go(t,b,N,G)}}w1(),g=Oe;break}catch(tt){cg(t,tt)}while(!0);return e&&t.shellSuspendCounter++,ga=Sr=null,oe=i,B.H=s,B.A=f,Ht===null&&(ve=null,It=0,Vl()),g}function w1(){for(;Ht!==null;)pg(Ht)}function A1(t,e){var a=oe;oe|=2;var i=fg(),s=dg();ve!==t||It!==e?(xs=null,Ss=Lt()+500,mo(t,e)):co=be(t,e);t:do try{if(ie!==0&&Ht!==null){e=Ht;var f=Sn;e:switch(ie){case 1:ie=0,Sn=null,go(t,e,f,1);break;case 2:case 9:if(Rh(f)){ie=0,Sn=null,hg(e);break}e=function(){ie!==2&&ie!==9||ve!==t||(ie=7),Jn(t)},f.then(e,e);break t;case 3:ie=7;break t;case 4:ie=5;break t;case 7:Rh(f)?(ie=0,Sn=null,hg(e)):(ie=0,Sn=null,go(t,e,f,7));break;case 5:var g=null;switch(Ht.tag){case 26:g=Ht.memoizedState;case 5:case 27:var b=Ht;if(!g||Kg(g)){ie=0,Sn=null;var N=b.sibling;if(N!==null)Ht=N;else{var G=b.return;G!==null?(Ht=G,Ts(G)):Ht=null}break e}}ie=0,Sn=null,go(t,e,f,5);break;case 6:ie=0,Sn=null,go(t,e,f,6);break;case 8:vf(),Oe=6;break t;default:throw Error(l(462))}}O1();break}catch(tt){cg(t,tt)}while(!0);return ga=Sr=null,B.H=i,B.A=s,oe=a,Ht!==null?0:(ve=null,It=0,Vl(),Oe)}function O1(){for(;Ht!==null&&!Vt();)pg(Ht)}function pg(t){var e=Hm(t.alternate,t,Ea);t.memoizedProps=t.pendingProps,e===null?Ts(t):Ht=e}function hg(t){var e=t,a=e.alternate;switch(e.tag){case 15:case 0:e=jm(a,e,e.pendingProps,e.type,void 0,It);break;case 11:e=jm(a,e,e.pendingProps,e.type.render,e.ref,It);break;case 5:jc(e);default:Pm(a,e),e=Ht=mh(e,Ea),e=Hm(a,e,Ea)}t.memoizedProps=t.pendingProps,e===null?Ts(t):Ht=e}function go(t,e,a,i){ga=Sr=null,jc(e),io=null,Ci=0;var s=e.return;try{if(y1(t,s,e,a,It)){Oe=1,hs(t,Bn(a,t.current)),Ht=null;return}}catch(f){if(s!==null)throw Ht=s,f;Oe=1,hs(t,Bn(a,t.current)),Ht=null;return}e.flags&32768?(Ft||i===1?t=!0:co||(It&536870912)!==0?t=!1:(Xa=t=!0,(i===2||i===9||i===3||i===6)&&(i=$n.current,i!==null&&i.tag===13&&(i.flags|=16384))),mg(e,t)):Ts(e)}function Ts(t){var e=t;do{if((e.flags&32768)!==0){mg(e,Xa);return}t=e.return;var a=b1(e.alternate,e,Ea);if(a!==null){Ht=a;return}if(e=e.sibling,e!==null){Ht=e;return}Ht=e=t}while(e!==null);Oe===0&&(Oe=5)}function mg(t,e){do{var a=S1(t.alternate,t);if(a!==null){a.flags&=32767,Ht=a;return}if(a=t.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!e&&(t=t.sibling,t!==null)){Ht=t;return}Ht=t=a}while(t!==null);Oe=6,Ht=null}function gg(t,e,a,i,s,f,g,b,N){t.cancelPendingCommit=null;do Es();while(We!==0);if((oe&6)!==0)throw Error(l(327));if(e!==null){if(e===t.current)throw Error(l(177));if(f=e.lanes|e.childLanes,f|=uc,_u(t,a,f,g,b,N),t===ve&&(Ht=ve=null,It=0),po=e,Ka=t,ho=a,mf=f,gf=s,ig=i,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,B1(_t,function(){return xg(),null})):(t.callbackNode=null,t.callbackPriority=0),i=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||i){i=B.T,B.T=null,s=V.p,V.p=2,g=oe,oe|=4;try{x1(t,e,a)}finally{oe=g,V.p=s,B.T=i}}We=1,yg(),vg(),bg()}}function yg(){if(We===1){We=0;var t=Ka,e=po,a=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||a){a=B.T,B.T=null;var i=V.p;V.p=2;var s=oe;oe|=4;try{Jm(e,t);var f=Bf,g=oh(t.containerInfo),b=f.focusedElem,N=f.selectionRange;if(g!==b&&b&&b.ownerDocument&&rh(b.ownerDocument.documentElement,b)){if(N!==null&&rc(b)){var G=N.start,tt=N.end;if(tt===void 0&&(tt=G),"selectionStart"in b)b.selectionStart=G,b.selectionEnd=Math.min(tt,b.value.length);else{var rt=b.ownerDocument||document,Y=rt&&rt.defaultView||window;if(Y.getSelection){var I=Y.getSelection(),Rt=b.textContent.length,xt=Math.min(N.start,Rt),fe=N.end===void 0?xt:Math.min(N.end,Rt);!I.extend&&xt>fe&&(g=fe,fe=xt,xt=g);var L=ah(b,xt),k=ah(b,fe);if(L&&k&&(I.rangeCount!==1||I.anchorNode!==L.node||I.anchorOffset!==L.offset||I.focusNode!==k.node||I.focusOffset!==k.offset)){var q=rt.createRange();q.setStart(L.node,L.offset),I.removeAllRanges(),xt>fe?(I.addRange(q),I.extend(k.node,k.offset)):(q.setEnd(k.node,k.offset),I.addRange(q))}}}}for(rt=[],I=b;I=I.parentNode;)I.nodeType===1&&rt.push({element:I,left:I.scrollLeft,top:I.scrollTop});for(typeof b.focus=="function"&&b.focus(),b=0;b<rt.length;b++){var at=rt[b];at.element.scrollLeft=at.left,at.element.scrollTop=at.top}}$s=!!Df,Bf=Df=null}finally{oe=s,V.p=i,B.T=a}}t.current=e,We=2}}function vg(){if(We===2){We=0;var t=Ka,e=po,a=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||a){a=B.T,B.T=null;var i=V.p;V.p=2;var s=oe;oe|=4;try{Km(t,e.alternate,e)}finally{oe=s,V.p=i,B.T=a}}We=3}}function bg(){if(We===4||We===3){We=0,Te();var t=Ka,e=po,a=ho,i=ig;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?We=5:(We=0,po=Ka=null,Sg(t,t.pendingLanes));var s=t.pendingLanes;if(s===0&&(Wa=null),Ot(a),e=e.stateNode,ne&&typeof ne.onCommitFiberRoot=="function")try{ne.onCommitFiberRoot(Le,e,void 0,(e.current.flags&128)===128)}catch{}if(i!==null){e=B.T,s=V.p,V.p=2,B.T=null;try{for(var f=t.onRecoverableError,g=0;g<i.length;g++){var b=i[g];f(b.value,{componentStack:b.stack})}}finally{B.T=e,V.p=s}}(ho&3)!==0&&Es(),Jn(t),s=t.pendingLanes,(a&4194090)!==0&&(s&42)!==0?t===yf?Di++:(Di=0,yf=t):Di=0,Bi(0)}}function Sg(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,fi(e)))}function Es(t){return yg(),vg(),bg(),xg()}function xg(){if(We!==5)return!1;var t=Ka,e=mf;mf=0;var a=Ot(ho),i=B.T,s=V.p;try{V.p=32>a?32:a,B.T=null,a=gf,gf=null;var f=Ka,g=ho;if(We=0,po=Ka=null,ho=0,(oe&6)!==0)throw Error(l(331));var b=oe;if(oe|=4,rg(f.current),eg(f,f.current,g,a),oe=b,Bi(0,!1),ne&&typeof ne.onPostCommitFiberRoot=="function")try{ne.onPostCommitFiberRoot(Le,f)}catch{}return!0}finally{V.p=s,B.T=i,Sg(t,e)}}function Cg(t,e,a){e=Bn(a,e),e=Wc(t.stateNode,e,2),t=La(t,e,2),t!==null&&(cr(t,2),Jn(t))}function he(t,e,a){if(t.tag===3)Cg(t,t,a);else for(;e!==null;){if(e.tag===3){Cg(e,t,a);break}else if(e.tag===1){var i=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(Wa===null||!Wa.has(i))){t=Bn(a,t),a=wm(2),i=La(e,a,2),i!==null&&(Am(a,i,e,t),cr(i,2),Jn(i));break}}e=e.return}}function xf(t,e,a){var i=t.pingCache;if(i===null){i=t.pingCache=new E1;var s=new Set;i.set(e,s)}else s=i.get(e),s===void 0&&(s=new Set,i.set(e,s));s.has(a)||(ff=!0,s.add(a),t=M1.bind(null,t,e,a),e.then(t,t))}function M1(t,e,a){var i=t.pingCache;i!==null&&i.delete(e),t.pingedLanes|=t.suspendedLanes&a,t.warmLanes&=~a,ve===t&&(It&a)===a&&(Oe===4||Oe===3&&(It&62914560)===It&&300>Lt()-hf?(oe&2)===0&&mo(t,0):df|=a,fo===It&&(fo=0)),Jn(t)}function Tg(t,e){e===0&&(e=Ml()),t=Qr(t,e),t!==null&&(cr(t,e),Jn(t))}function z1(t){var e=t.memoizedState,a=0;e!==null&&(a=e.retryLane),Tg(t,a)}function D1(t,e){var a=0;switch(t.tag){case 13:var i=t.stateNode,s=t.memoizedState;s!==null&&(a=s.retryLane);break;case 19:i=t.stateNode;break;case 22:i=t.stateNode._retryCache;break;default:throw Error(l(314))}i!==null&&i.delete(e),Tg(t,a)}function B1(t,e){return $t(t,e)}var Rs=null,yo=null,Cf=!1,ws=!1,Tf=!1,Ar=0;function Jn(t){t!==yo&&t.next===null&&(yo===null?Rs=yo=t:yo=yo.next=t),ws=!0,Cf||(Cf=!0,j1())}function Bi(t,e){if(!Tf&&ws){Tf=!0;do for(var a=!1,i=Rs;i!==null;){if(t!==0){var s=i.pendingLanes;if(s===0)var f=0;else{var g=i.suspendedLanes,b=i.pingedLanes;f=(1<<31-ae(42|t)+1)-1,f&=s&~(g&~b),f=f&201326741?f&201326741|1:f?f|2:0}f!==0&&(a=!0,Ag(i,f))}else f=It,f=Xt(i,i===ve?f:0,i.cancelPendingCommit!==null||i.timeoutHandle!==-1),(f&3)===0||be(i,f)||(a=!0,Ag(i,f));i=i.next}while(a);Tf=!1}}function N1(){Eg()}function Eg(){ws=Cf=!1;var t=0;Ar!==0&&(P1()&&(t=Ar),Ar=0);for(var e=Lt(),a=null,i=Rs;i!==null;){var s=i.next,f=Rg(i,e);f===0?(i.next=null,a===null?Rs=s:a.next=s,s===null&&(yo=a)):(a=i,(t!==0||(f&3)!==0)&&(ws=!0)),i=s}Bi(t)}function Rg(t,e){for(var a=t.suspendedLanes,i=t.pingedLanes,s=t.expirationTimes,f=t.pendingLanes&-62914561;0<f;){var g=31-ae(f),b=1<<g,N=s[g];N===-1?((b&a)===0||(b&i)!==0)&&(s[g]=mn(b,e)):N<=e&&(t.expiredLanes|=b),f&=~b}if(e=ve,a=It,a=Xt(t,t===e?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),i=t.callbackNode,a===0||t===e&&(ie===2||ie===9)||t.cancelPendingCommit!==null)return i!==null&&i!==null&&Et(i),t.callbackNode=null,t.callbackPriority=0;if((a&3)===0||be(t,a)){if(e=a&-a,e===t.callbackPriority)return e;switch(i!==null&&Et(i),Ot(a)){case 2:case 8:a=Yt;break;case 32:a=_t;break;case 268435456:a=Ue;break;default:a=_t}return i=wg.bind(null,t),a=$t(a,i),t.callbackPriority=e,t.callbackNode=a,e}return i!==null&&i!==null&&Et(i),t.callbackPriority=2,t.callbackNode=null,2}function wg(t,e){if(We!==0&&We!==5)return t.callbackNode=null,t.callbackPriority=0,null;var a=t.callbackNode;if(Es()&&t.callbackNode!==a)return null;var i=It;return i=Xt(t,t===ve?i:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),i===0?null:(sg(t,i,e),Rg(t,Lt()),t.callbackNode!=null&&t.callbackNode===a?wg.bind(null,t):null)}function Ag(t,e){if(Es())return null;sg(t,e,!0)}function j1(){V1(function(){(oe&6)!==0?$t(Qt,N1):Eg()})}function Ef(){return Ar===0&&(Ar=ur()),Ar}function Og(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:_l(""+t)}function Mg(t,e){var a=e.ownerDocument.createElement("input");return a.name=e.name,a.value=e.value,t.id&&a.setAttribute("form",t.id),e.parentNode.insertBefore(a,e),t=new FormData(t),a.parentNode.removeChild(a),t}function k1(t,e,a,i,s){if(e==="submit"&&a&&a.stateNode===s){var f=Og((s[ln]||null).action),g=i.submitter;g&&(e=(e=g[ln]||null)?Og(e.formAction):g.getAttribute("formAction"),e!==null&&(f=e,g=null));var b=new ql("action","action",null,i,s);t.push({event:b,listeners:[{instance:null,listener:function(){if(i.defaultPrevented){if(Ar!==0){var N=g?Mg(s,g):new FormData(s);Gc(a,{pending:!0,data:N,method:s.method,action:f},null,N)}}else typeof f=="function"&&(b.preventDefault(),N=g?Mg(s,g):new FormData(s),Gc(a,{pending:!0,data:N,method:s.method,action:f},f,N))},currentTarget:s}]})}}for(var Rf=0;Rf<sc.length;Rf++){var wf=sc[Rf],$1=wf.toLowerCase(),_1=wf[0].toUpperCase()+wf.slice(1);Yn($1,"on"+_1)}Yn(sh,"onAnimationEnd"),Yn(uh,"onAnimationIteration"),Yn(ch,"onAnimationStart"),Yn("dblclick","onDoubleClick"),Yn("focusin","onFocus"),Yn("focusout","onBlur"),Yn(t1,"onTransitionRun"),Yn(e1,"onTransitionStart"),Yn(n1,"onTransitionCancel"),Yn(fh,"onTransitionEnd"),Hr("onMouseEnter",["mouseout","mouseover"]),Hr("onMouseLeave",["mouseout","mouseover"]),Hr("onPointerEnter",["pointerout","pointerover"]),Hr("onPointerLeave",["pointerout","pointerover"]),fr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),fr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),fr("onBeforeInput",["compositionend","keypress","textInput","paste"]),fr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),fr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),fr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ni="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),U1=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ni));function zg(t,e){e=(e&4)!==0;for(var a=0;a<t.length;a++){var i=t[a],s=i.event;i=i.listeners;t:{var f=void 0;if(e)for(var g=i.length-1;0<=g;g--){var b=i[g],N=b.instance,G=b.currentTarget;if(b=b.listener,N!==f&&s.isPropagationStopped())break t;f=b,s.currentTarget=G;try{f(s)}catch(tt){ps(tt)}s.currentTarget=null,f=N}else for(g=0;g<i.length;g++){if(b=i[g],N=b.instance,G=b.currentTarget,b=b.listener,N!==f&&s.isPropagationStopped())break t;f=b,s.currentTarget=G;try{f(s)}catch(tt){ps(tt)}s.currentTarget=null,f=N}}}}function qt(t,e){var a=e[Uu];a===void 0&&(a=e[Uu]=new Set);var i=t+"__bubble";a.has(i)||(Dg(e,t,2,!1),a.add(i))}function Af(t,e,a){var i=0;e&&(i|=4),Dg(a,t,i,e)}var As="_reactListening"+Math.random().toString(36).slice(2);function Of(t){if(!t[As]){t[As]=!0,Ep.forEach(function(a){a!=="selectionchange"&&(U1.has(a)||Af(a,!1,t),Af(a,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[As]||(e[As]=!0,Af("selectionchange",!1,e))}}function Dg(t,e,a,i){switch(ey(e)){case 2:var s=fS;break;case 8:s=dS;break;default:s=Pf}a=s.bind(null,e,a,t),s=void 0,!Ku||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(s=!0),i?s!==void 0?t.addEventListener(e,a,{capture:!0,passive:s}):t.addEventListener(e,a,!0):s!==void 0?t.addEventListener(e,a,{passive:s}):t.addEventListener(e,a,!1)}function Mf(t,e,a,i,s){var f=i;if((e&1)===0&&(e&2)===0&&i!==null)t:for(;;){if(i===null)return;var g=i.tag;if(g===3||g===4){var b=i.stateNode.containerInfo;if(b===s)break;if(g===4)for(g=i.return;g!==null;){var N=g.tag;if((N===3||N===4)&&g.stateNode.containerInfo===s)return;g=g.return}for(;b!==null;){if(g=_r(b),g===null)return;if(N=g.tag,N===5||N===6||N===26||N===27){i=f=g;continue t}b=b.parentNode}}i=i.return}Up(function(){var G=f,tt=Iu(a),rt=[];t:{var Y=dh.get(t);if(Y!==void 0){var I=ql,Rt=t;switch(t){case"keypress":if(Ll(a)===0)break t;case"keydown":case"keyup":I=Bb;break;case"focusin":Rt="focus",I=Ju;break;case"focusout":Rt="blur",I=Ju;break;case"beforeblur":case"afterblur":I=Ju;break;case"click":if(a.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":I=qp;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":I=Sb;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":I=kb;break;case sh:case uh:case ch:I=Tb;break;case fh:I=_b;break;case"scroll":case"scrollend":I=vb;break;case"wheel":I=Lb;break;case"copy":case"cut":case"paste":I=Rb;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":I=Gp;break;case"toggle":case"beforetoggle":I=qb}var xt=(e&4)!==0,fe=!xt&&(t==="scroll"||t==="scrollend"),L=xt?Y!==null?Y+"Capture":null:Y;xt=[];for(var k=G,q;k!==null;){var at=k;if(q=at.stateNode,at=at.tag,at!==5&&at!==26&&at!==27||q===null||L===null||(at=Fo(k,L),at!=null&&xt.push(ji(k,at,q))),fe)break;k=k.return}0<xt.length&&(Y=new I(Y,Rt,null,a,tt),rt.push({event:Y,listeners:xt}))}}if((e&7)===0){t:{if(Y=t==="mouseover"||t==="pointerover",I=t==="mouseout"||t==="pointerout",Y&&a!==Xu&&(Rt=a.relatedTarget||a.fromElement)&&(_r(Rt)||Rt[$r]))break t;if((I||Y)&&(Y=tt.window===tt?tt:(Y=tt.ownerDocument)?Y.defaultView||Y.parentWindow:window,I?(Rt=a.relatedTarget||a.toElement,I=G,Rt=Rt?_r(Rt):null,Rt!==null&&(fe=c(Rt),xt=Rt.tag,Rt!==fe||xt!==5&&xt!==27&&xt!==6)&&(Rt=null)):(I=null,Rt=G),I!==Rt)){if(xt=qp,at="onMouseLeave",L="onMouseEnter",k="mouse",(t==="pointerout"||t==="pointerover")&&(xt=Gp,at="onPointerLeave",L="onPointerEnter",k="pointer"),fe=I==null?Y:Zo(I),q=Rt==null?Y:Zo(Rt),Y=new xt(at,k+"leave",I,a,tt),Y.target=fe,Y.relatedTarget=q,at=null,_r(tt)===G&&(xt=new xt(L,k+"enter",Rt,a,tt),xt.target=q,xt.relatedTarget=fe,at=xt),fe=at,I&&Rt)e:{for(xt=I,L=Rt,k=0,q=xt;q;q=vo(q))k++;for(q=0,at=L;at;at=vo(at))q++;for(;0<k-q;)xt=vo(xt),k--;for(;0<q-k;)L=vo(L),q--;for(;k--;){if(xt===L||L!==null&&xt===L.alternate)break e;xt=vo(xt),L=vo(L)}xt=null}else xt=null;I!==null&&Bg(rt,Y,I,xt,!1),Rt!==null&&fe!==null&&Bg(rt,fe,Rt,xt,!0)}}t:{if(Y=G?Zo(G):window,I=Y.nodeName&&Y.nodeName.toLowerCase(),I==="select"||I==="input"&&Y.type==="file")var pt=Zp;else if(Kp(Y))if(Fp)pt=Zb;else{pt=Kb;var Ut=Wb}else I=Y.nodeName,!I||I.toLowerCase()!=="input"||Y.type!=="checkbox"&&Y.type!=="radio"?G&&Yu(G.elementType)&&(pt=Zp):pt=Qb;if(pt&&(pt=pt(t,G))){Qp(rt,pt,a,tt);break t}Ut&&Ut(t,Y,G),t==="focusout"&&G&&Y.type==="number"&&G.memoizedProps.value!=null&&Vu(Y,"number",Y.value)}switch(Ut=G?Zo(G):window,t){case"focusin":(Kp(Ut)||Ut.contentEditable==="true")&&(Ir=Ut,oc=G,ii=null);break;case"focusout":ii=oc=Ir=null;break;case"mousedown":ic=!0;break;case"contextmenu":case"mouseup":case"dragend":ic=!1,ih(rt,a,tt);break;case"selectionchange":if(Jb)break;case"keydown":case"keyup":ih(rt,a,tt)}var vt;if(ec)t:{switch(t){case"compositionstart":var Ct="onCompositionStart";break t;case"compositionend":Ct="onCompositionEnd";break t;case"compositionupdate":Ct="onCompositionUpdate";break t}Ct=void 0}else Xr?Ip(t,a)&&(Ct="onCompositionEnd"):t==="keydown"&&a.keyCode===229&&(Ct="onCompositionStart");Ct&&(Vp&&a.locale!=="ko"&&(Xr||Ct!=="onCompositionStart"?Ct==="onCompositionEnd"&&Xr&&(vt=Lp()):(ka=tt,Qu="value"in ka?ka.value:ka.textContent,Xr=!0)),Ut=Os(G,Ct),0<Ut.length&&(Ct=new Pp(Ct,t,null,a,tt),rt.push({event:Ct,listeners:Ut}),vt?Ct.data=vt:(vt=Wp(a),vt!==null&&(Ct.data=vt)))),(vt=Gb?Vb(t,a):Yb(t,a))&&(Ct=Os(G,"onBeforeInput"),0<Ct.length&&(Ut=new Pp("onBeforeInput","beforeinput",null,a,tt),rt.push({event:Ut,listeners:Ct}),Ut.data=vt)),k1(rt,t,G,a,tt)}zg(rt,e)})}function ji(t,e,a){return{instance:t,listener:e,currentTarget:a}}function Os(t,e){for(var a=e+"Capture",i=[];t!==null;){var s=t,f=s.stateNode;if(s=s.tag,s!==5&&s!==26&&s!==27||f===null||(s=Fo(t,a),s!=null&&i.unshift(ji(t,s,f)),s=Fo(t,e),s!=null&&i.push(ji(t,s,f))),t.tag===3)return i;t=t.return}return[]}function vo(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Bg(t,e,a,i,s){for(var f=e._reactName,g=[];a!==null&&a!==i;){var b=a,N=b.alternate,G=b.stateNode;if(b=b.tag,N!==null&&N===i)break;b!==5&&b!==26&&b!==27||G===null||(N=G,s?(G=Fo(a,f),G!=null&&g.unshift(ji(a,G,N))):s||(G=Fo(a,f),G!=null&&g.push(ji(a,G,N)))),a=a.return}g.length!==0&&t.push({event:e,listeners:g})}var L1=/\r\n?/g,H1=/\u0000|\uFFFD/g;function Ng(t){return(typeof t=="string"?t:""+t).replace(L1,`
`).replace(H1,"")}function jg(t,e){return e=Ng(e),Ng(t)===e}function Ms(){}function ce(t,e,a,i,s,f){switch(a){case"children":typeof i=="string"?e==="body"||e==="textarea"&&i===""||Gr(t,i):(typeof i=="number"||typeof i=="bigint")&&e!=="body"&&Gr(t,""+i);break;case"className":jl(t,"class",i);break;case"tabIndex":jl(t,"tabindex",i);break;case"dir":case"role":case"viewBox":case"width":case"height":jl(t,a,i);break;case"style":$p(t,i,f);break;case"data":if(e!=="object"){jl(t,"data",i);break}case"src":case"href":if(i===""&&(e!=="a"||a!=="href")){t.removeAttribute(a);break}if(i==null||typeof i=="function"||typeof i=="symbol"||typeof i=="boolean"){t.removeAttribute(a);break}i=_l(""+i),t.setAttribute(a,i);break;case"action":case"formAction":if(typeof i=="function"){t.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof f=="function"&&(a==="formAction"?(e!=="input"&&ce(t,e,"name",s.name,s,null),ce(t,e,"formEncType",s.formEncType,s,null),ce(t,e,"formMethod",s.formMethod,s,null),ce(t,e,"formTarget",s.formTarget,s,null)):(ce(t,e,"encType",s.encType,s,null),ce(t,e,"method",s.method,s,null),ce(t,e,"target",s.target,s,null)));if(i==null||typeof i=="symbol"||typeof i=="boolean"){t.removeAttribute(a);break}i=_l(""+i),t.setAttribute(a,i);break;case"onClick":i!=null&&(t.onclick=Ms);break;case"onScroll":i!=null&&qt("scroll",t);break;case"onScrollEnd":i!=null&&qt("scrollend",t);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(l(61));if(a=i.__html,a!=null){if(s.children!=null)throw Error(l(60));t.innerHTML=a}}break;case"multiple":t.multiple=i&&typeof i!="function"&&typeof i!="symbol";break;case"muted":t.muted=i&&typeof i!="function"&&typeof i!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(i==null||typeof i=="function"||typeof i=="boolean"||typeof i=="symbol"){t.removeAttribute("xlink:href");break}a=_l(""+i),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":i!=null&&typeof i!="function"&&typeof i!="symbol"?t.setAttribute(a,""+i):t.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":i&&typeof i!="function"&&typeof i!="symbol"?t.setAttribute(a,""):t.removeAttribute(a);break;case"capture":case"download":i===!0?t.setAttribute(a,""):i!==!1&&i!=null&&typeof i!="function"&&typeof i!="symbol"?t.setAttribute(a,i):t.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":i!=null&&typeof i!="function"&&typeof i!="symbol"&&!isNaN(i)&&1<=i?t.setAttribute(a,i):t.removeAttribute(a);break;case"rowSpan":case"start":i==null||typeof i=="function"||typeof i=="symbol"||isNaN(i)?t.removeAttribute(a):t.setAttribute(a,i);break;case"popover":qt("beforetoggle",t),qt("toggle",t),Nl(t,"popover",i);break;case"xlinkActuate":fa(t,"http://www.w3.org/1999/xlink","xlink:actuate",i);break;case"xlinkArcrole":fa(t,"http://www.w3.org/1999/xlink","xlink:arcrole",i);break;case"xlinkRole":fa(t,"http://www.w3.org/1999/xlink","xlink:role",i);break;case"xlinkShow":fa(t,"http://www.w3.org/1999/xlink","xlink:show",i);break;case"xlinkTitle":fa(t,"http://www.w3.org/1999/xlink","xlink:title",i);break;case"xlinkType":fa(t,"http://www.w3.org/1999/xlink","xlink:type",i);break;case"xmlBase":fa(t,"http://www.w3.org/XML/1998/namespace","xml:base",i);break;case"xmlLang":fa(t,"http://www.w3.org/XML/1998/namespace","xml:lang",i);break;case"xmlSpace":fa(t,"http://www.w3.org/XML/1998/namespace","xml:space",i);break;case"is":Nl(t,"is",i);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=gb.get(a)||a,Nl(t,a,i))}}function zf(t,e,a,i,s,f){switch(a){case"style":$p(t,i,f);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(l(61));if(a=i.__html,a!=null){if(s.children!=null)throw Error(l(60));t.innerHTML=a}}break;case"children":typeof i=="string"?Gr(t,i):(typeof i=="number"||typeof i=="bigint")&&Gr(t,""+i);break;case"onScroll":i!=null&&qt("scroll",t);break;case"onScrollEnd":i!=null&&qt("scrollend",t);break;case"onClick":i!=null&&(t.onclick=Ms);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Rp.hasOwnProperty(a))t:{if(a[0]==="o"&&a[1]==="n"&&(s=a.endsWith("Capture"),e=a.slice(2,s?a.length-7:void 0),f=t[ln]||null,f=f!=null?f[a]:null,typeof f=="function"&&t.removeEventListener(e,f,s),typeof i=="function")){typeof f!="function"&&f!==null&&(a in t?t[a]=null:t.hasAttribute(a)&&t.removeAttribute(a)),t.addEventListener(e,i,s);break t}a in t?t[a]=i:i===!0?t.setAttribute(a,""):Nl(t,a,i)}}}function Ke(t,e,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":qt("error",t),qt("load",t);var i=!1,s=!1,f;for(f in a)if(a.hasOwnProperty(f)){var g=a[f];if(g!=null)switch(f){case"src":i=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(l(137,e));default:ce(t,e,f,g,a,null)}}s&&ce(t,e,"srcSet",a.srcSet,a,null),i&&ce(t,e,"src",a.src,a,null);return;case"input":qt("invalid",t);var b=f=g=s=null,N=null,G=null;for(i in a)if(a.hasOwnProperty(i)){var tt=a[i];if(tt!=null)switch(i){case"name":s=tt;break;case"type":g=tt;break;case"checked":N=tt;break;case"defaultChecked":G=tt;break;case"value":f=tt;break;case"defaultValue":b=tt;break;case"children":case"dangerouslySetInnerHTML":if(tt!=null)throw Error(l(137,e));break;default:ce(t,e,i,tt,a,null)}}Bp(t,f,b,N,G,g,s,!1),kl(t);return;case"select":qt("invalid",t),i=g=f=null;for(s in a)if(a.hasOwnProperty(s)&&(b=a[s],b!=null))switch(s){case"value":f=b;break;case"defaultValue":g=b;break;case"multiple":i=b;default:ce(t,e,s,b,a,null)}e=f,a=g,t.multiple=!!i,e!=null?Pr(t,!!i,e,!1):a!=null&&Pr(t,!!i,a,!0);return;case"textarea":qt("invalid",t),f=s=i=null;for(g in a)if(a.hasOwnProperty(g)&&(b=a[g],b!=null))switch(g){case"value":i=b;break;case"defaultValue":s=b;break;case"children":f=b;break;case"dangerouslySetInnerHTML":if(b!=null)throw Error(l(91));break;default:ce(t,e,g,b,a,null)}jp(t,i,s,f),kl(t);return;case"option":for(N in a)if(a.hasOwnProperty(N)&&(i=a[N],i!=null))switch(N){case"selected":t.selected=i&&typeof i!="function"&&typeof i!="symbol";break;default:ce(t,e,N,i,a,null)}return;case"dialog":qt("beforetoggle",t),qt("toggle",t),qt("cancel",t),qt("close",t);break;case"iframe":case"object":qt("load",t);break;case"video":case"audio":for(i=0;i<Ni.length;i++)qt(Ni[i],t);break;case"image":qt("error",t),qt("load",t);break;case"details":qt("toggle",t);break;case"embed":case"source":case"link":qt("error",t),qt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(G in a)if(a.hasOwnProperty(G)&&(i=a[G],i!=null))switch(G){case"children":case"dangerouslySetInnerHTML":throw Error(l(137,e));default:ce(t,e,G,i,a,null)}return;default:if(Yu(e)){for(tt in a)a.hasOwnProperty(tt)&&(i=a[tt],i!==void 0&&zf(t,e,tt,i,a,void 0));return}}for(b in a)a.hasOwnProperty(b)&&(i=a[b],i!=null&&ce(t,e,b,i,a,null))}function q1(t,e,a,i){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var s=null,f=null,g=null,b=null,N=null,G=null,tt=null;for(I in a){var rt=a[I];if(a.hasOwnProperty(I)&&rt!=null)switch(I){case"checked":break;case"value":break;case"defaultValue":N=rt;default:i.hasOwnProperty(I)||ce(t,e,I,null,i,rt)}}for(var Y in i){var I=i[Y];if(rt=a[Y],i.hasOwnProperty(Y)&&(I!=null||rt!=null))switch(Y){case"type":f=I;break;case"name":s=I;break;case"checked":G=I;break;case"defaultChecked":tt=I;break;case"value":g=I;break;case"defaultValue":b=I;break;case"children":case"dangerouslySetInnerHTML":if(I!=null)throw Error(l(137,e));break;default:I!==rt&&ce(t,e,Y,I,i,rt)}}Gu(t,g,b,N,G,tt,f,s);return;case"select":I=g=b=Y=null;for(f in a)if(N=a[f],a.hasOwnProperty(f)&&N!=null)switch(f){case"value":break;case"multiple":I=N;default:i.hasOwnProperty(f)||ce(t,e,f,null,i,N)}for(s in i)if(f=i[s],N=a[s],i.hasOwnProperty(s)&&(f!=null||N!=null))switch(s){case"value":Y=f;break;case"defaultValue":b=f;break;case"multiple":g=f;default:f!==N&&ce(t,e,s,f,i,N)}e=b,a=g,i=I,Y!=null?Pr(t,!!a,Y,!1):!!i!=!!a&&(e!=null?Pr(t,!!a,e,!0):Pr(t,!!a,a?[]:"",!1));return;case"textarea":I=Y=null;for(b in a)if(s=a[b],a.hasOwnProperty(b)&&s!=null&&!i.hasOwnProperty(b))switch(b){case"value":break;case"children":break;default:ce(t,e,b,null,i,s)}for(g in i)if(s=i[g],f=a[g],i.hasOwnProperty(g)&&(s!=null||f!=null))switch(g){case"value":Y=s;break;case"defaultValue":I=s;break;case"children":break;case"dangerouslySetInnerHTML":if(s!=null)throw Error(l(91));break;default:s!==f&&ce(t,e,g,s,i,f)}Np(t,Y,I);return;case"option":for(var Rt in a)if(Y=a[Rt],a.hasOwnProperty(Rt)&&Y!=null&&!i.hasOwnProperty(Rt))switch(Rt){case"selected":t.selected=!1;break;default:ce(t,e,Rt,null,i,Y)}for(N in i)if(Y=i[N],I=a[N],i.hasOwnProperty(N)&&Y!==I&&(Y!=null||I!=null))switch(N){case"selected":t.selected=Y&&typeof Y!="function"&&typeof Y!="symbol";break;default:ce(t,e,N,Y,i,I)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var xt in a)Y=a[xt],a.hasOwnProperty(xt)&&Y!=null&&!i.hasOwnProperty(xt)&&ce(t,e,xt,null,i,Y);for(G in i)if(Y=i[G],I=a[G],i.hasOwnProperty(G)&&Y!==I&&(Y!=null||I!=null))switch(G){case"children":case"dangerouslySetInnerHTML":if(Y!=null)throw Error(l(137,e));break;default:ce(t,e,G,Y,i,I)}return;default:if(Yu(e)){for(var fe in a)Y=a[fe],a.hasOwnProperty(fe)&&Y!==void 0&&!i.hasOwnProperty(fe)&&zf(t,e,fe,void 0,i,Y);for(tt in i)Y=i[tt],I=a[tt],!i.hasOwnProperty(tt)||Y===I||Y===void 0&&I===void 0||zf(t,e,tt,Y,i,I);return}}for(var L in a)Y=a[L],a.hasOwnProperty(L)&&Y!=null&&!i.hasOwnProperty(L)&&ce(t,e,L,null,i,Y);for(rt in i)Y=i[rt],I=a[rt],!i.hasOwnProperty(rt)||Y===I||Y==null&&I==null||ce(t,e,rt,Y,i,I)}var Df=null,Bf=null;function zs(t){return t.nodeType===9?t:t.ownerDocument}function kg(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function $g(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function Nf(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var jf=null;function P1(){var t=window.event;return t&&t.type==="popstate"?t===jf?!1:(jf=t,!0):(jf=null,!1)}var _g=typeof setTimeout=="function"?setTimeout:void 0,G1=typeof clearTimeout=="function"?clearTimeout:void 0,Ug=typeof Promise=="function"?Promise:void 0,V1=typeof queueMicrotask=="function"?queueMicrotask:typeof Ug<"u"?function(t){return Ug.resolve(null).then(t).catch(Y1)}:_g;function Y1(t){setTimeout(function(){throw t})}function Za(t){return t==="head"}function Lg(t,e){var a=e,i=0,s=0;do{var f=a.nextSibling;if(t.removeChild(a),f&&f.nodeType===8)if(a=f.data,a==="/$"){if(0<i&&8>i){a=i;var g=t.ownerDocument;if(a&1&&ki(g.documentElement),a&2&&ki(g.body),a&4)for(a=g.head,ki(a),g=a.firstChild;g;){var b=g.nextSibling,N=g.nodeName;g[Qo]||N==="SCRIPT"||N==="STYLE"||N==="LINK"&&g.rel.toLowerCase()==="stylesheet"||a.removeChild(g),g=b}}if(s===0){t.removeChild(f),Gi(e);return}s--}else a==="$"||a==="$?"||a==="$!"?s++:i=a.charCodeAt(0)-48;else i=0;a=f}while(a);Gi(e)}function kf(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var a=e;switch(e=e.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":kf(a),Lu(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}t.removeChild(a)}}function X1(t,e,a,i){for(;t.nodeType===1;){var s=a;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!i&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(i){if(!t[Qo])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(f=t.getAttribute("rel"),f==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(f!==s.rel||t.getAttribute("href")!==(s.href==null||s.href===""?null:s.href)||t.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin)||t.getAttribute("title")!==(s.title==null?null:s.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(f=t.getAttribute("src"),(f!==(s.src==null?null:s.src)||t.getAttribute("type")!==(s.type==null?null:s.type)||t.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin))&&f&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var f=s.name==null?null:""+s.name;if(s.type==="hidden"&&t.getAttribute("name")===f)return t}else return t;if(t=In(t.nextSibling),t===null)break}return null}function I1(t,e,a){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!a||(t=In(t.nextSibling),t===null))return null;return t}function $f(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function W1(t,e){var a=t.ownerDocument;if(t.data!=="$?"||a.readyState==="complete")e();else{var i=function(){e(),a.removeEventListener("DOMContentLoaded",i)};a.addEventListener("DOMContentLoaded",i),t._reactRetry=i}}function In(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var _f=null;function Hg(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var a=t.data;if(a==="$"||a==="$!"||a==="$?"){if(e===0)return t;e--}else a==="/$"&&e++}t=t.previousSibling}return null}function qg(t,e,a){switch(e=zs(a),t){case"html":if(t=e.documentElement,!t)throw Error(l(452));return t;case"head":if(t=e.head,!t)throw Error(l(453));return t;case"body":if(t=e.body,!t)throw Error(l(454));return t;default:throw Error(l(451))}}function ki(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);Lu(t)}var Un=new Map,Pg=new Set;function Ds(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Ra=V.d;V.d={f:K1,r:Q1,D:Z1,C:F1,L:J1,m:tS,X:nS,S:eS,M:aS};function K1(){var t=Ra.f(),e=Cs();return t||e}function Q1(t){var e=Ur(t);e!==null&&e.tag===5&&e.type==="form"?sm(e):Ra.r(t)}var bo=typeof document>"u"?null:document;function Gg(t,e,a){var i=bo;if(i&&typeof e=="string"&&e){var s=Dn(e);s='link[rel="'+t+'"][href="'+s+'"]',typeof a=="string"&&(s+='[crossorigin="'+a+'"]'),Pg.has(s)||(Pg.add(s),t={rel:t,crossOrigin:a,href:e},i.querySelector(s)===null&&(e=i.createElement("link"),Ke(e,"link",t),Ge(e),i.head.appendChild(e)))}}function Z1(t){Ra.D(t),Gg("dns-prefetch",t,null)}function F1(t,e){Ra.C(t,e),Gg("preconnect",t,e)}function J1(t,e,a){Ra.L(t,e,a);var i=bo;if(i&&t&&e){var s='link[rel="preload"][as="'+Dn(e)+'"]';e==="image"&&a&&a.imageSrcSet?(s+='[imagesrcset="'+Dn(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(s+='[imagesizes="'+Dn(a.imageSizes)+'"]')):s+='[href="'+Dn(t)+'"]';var f=s;switch(e){case"style":f=So(t);break;case"script":f=xo(t)}Un.has(f)||(t=y({rel:"preload",href:e==="image"&&a&&a.imageSrcSet?void 0:t,as:e},a),Un.set(f,t),i.querySelector(s)!==null||e==="style"&&i.querySelector($i(f))||e==="script"&&i.querySelector(_i(f))||(e=i.createElement("link"),Ke(e,"link",t),Ge(e),i.head.appendChild(e)))}}function tS(t,e){Ra.m(t,e);var a=bo;if(a&&t){var i=e&&typeof e.as=="string"?e.as:"script",s='link[rel="modulepreload"][as="'+Dn(i)+'"][href="'+Dn(t)+'"]',f=s;switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":f=xo(t)}if(!Un.has(f)&&(t=y({rel:"modulepreload",href:t},e),Un.set(f,t),a.querySelector(s)===null)){switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(_i(f)))return}i=a.createElement("link"),Ke(i,"link",t),Ge(i),a.head.appendChild(i)}}}function eS(t,e,a){Ra.S(t,e,a);var i=bo;if(i&&t){var s=Lr(i).hoistableStyles,f=So(t);e=e||"default";var g=s.get(f);if(!g){var b={loading:0,preload:null};if(g=i.querySelector($i(f)))b.loading=5;else{t=y({rel:"stylesheet",href:t,"data-precedence":e},a),(a=Un.get(f))&&Uf(t,a);var N=g=i.createElement("link");Ge(N),Ke(N,"link",t),N._p=new Promise(function(G,tt){N.onload=G,N.onerror=tt}),N.addEventListener("load",function(){b.loading|=1}),N.addEventListener("error",function(){b.loading|=2}),b.loading|=4,Bs(g,e,i)}g={type:"stylesheet",instance:g,count:1,state:b},s.set(f,g)}}}function nS(t,e){Ra.X(t,e);var a=bo;if(a&&t){var i=Lr(a).hoistableScripts,s=xo(t),f=i.get(s);f||(f=a.querySelector(_i(s)),f||(t=y({src:t,async:!0},e),(e=Un.get(s))&&Lf(t,e),f=a.createElement("script"),Ge(f),Ke(f,"link",t),a.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},i.set(s,f))}}function aS(t,e){Ra.M(t,e);var a=bo;if(a&&t){var i=Lr(a).hoistableScripts,s=xo(t),f=i.get(s);f||(f=a.querySelector(_i(s)),f||(t=y({src:t,async:!0,type:"module"},e),(e=Un.get(s))&&Lf(t,e),f=a.createElement("script"),Ge(f),Ke(f,"link",t),a.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},i.set(s,f))}}function Vg(t,e,a,i){var s=(s=ft.current)?Ds(s):null;if(!s)throw Error(l(446));switch(t){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(e=So(a.href),a=Lr(s).hoistableStyles,i=a.get(e),i||(i={type:"style",instance:null,count:0,state:null},a.set(e,i)),i):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){t=So(a.href);var f=Lr(s).hoistableStyles,g=f.get(t);if(g||(s=s.ownerDocument||s,g={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},f.set(t,g),(f=s.querySelector($i(t)))&&!f._p&&(g.instance=f,g.state.loading=5),Un.has(t)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},Un.set(t,a),f||rS(s,t,a,g.state))),e&&i===null)throw Error(l(528,""));return g}if(e&&i!==null)throw Error(l(529,""));return null;case"script":return e=a.async,a=a.src,typeof a=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=xo(a),a=Lr(s).hoistableScripts,i=a.get(e),i||(i={type:"script",instance:null,count:0,state:null},a.set(e,i)),i):{type:"void",instance:null,count:0,state:null};default:throw Error(l(444,t))}}function So(t){return'href="'+Dn(t)+'"'}function $i(t){return'link[rel="stylesheet"]['+t+"]"}function Yg(t){return y({},t,{"data-precedence":t.precedence,precedence:null})}function rS(t,e,a,i){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?i.loading=1:(e=t.createElement("link"),i.preload=e,e.addEventListener("load",function(){return i.loading|=1}),e.addEventListener("error",function(){return i.loading|=2}),Ke(e,"link",a),Ge(e),t.head.appendChild(e))}function xo(t){return'[src="'+Dn(t)+'"]'}function _i(t){return"script[async]"+t}function Xg(t,e,a){if(e.count++,e.instance===null)switch(e.type){case"style":var i=t.querySelector('style[data-href~="'+Dn(a.href)+'"]');if(i)return e.instance=i,Ge(i),i;var s=y({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return i=(t.ownerDocument||t).createElement("style"),Ge(i),Ke(i,"style",s),Bs(i,a.precedence,t),e.instance=i;case"stylesheet":s=So(a.href);var f=t.querySelector($i(s));if(f)return e.state.loading|=4,e.instance=f,Ge(f),f;i=Yg(a),(s=Un.get(s))&&Uf(i,s),f=(t.ownerDocument||t).createElement("link"),Ge(f);var g=f;return g._p=new Promise(function(b,N){g.onload=b,g.onerror=N}),Ke(f,"link",i),e.state.loading|=4,Bs(f,a.precedence,t),e.instance=f;case"script":return f=xo(a.src),(s=t.querySelector(_i(f)))?(e.instance=s,Ge(s),s):(i=a,(s=Un.get(f))&&(i=y({},a),Lf(i,s)),t=t.ownerDocument||t,s=t.createElement("script"),Ge(s),Ke(s,"link",i),t.head.appendChild(s),e.instance=s);case"void":return null;default:throw Error(l(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(i=e.instance,e.state.loading|=4,Bs(i,a.precedence,t));return e.instance}function Bs(t,e,a){for(var i=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),s=i.length?i[i.length-1]:null,f=s,g=0;g<i.length;g++){var b=i[g];if(b.dataset.precedence===e)f=b;else if(f!==s)break}f?f.parentNode.insertBefore(t,f.nextSibling):(e=a.nodeType===9?a.head:a,e.insertBefore(t,e.firstChild))}function Uf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function Lf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Ns=null;function Ig(t,e,a){if(Ns===null){var i=new Map,s=Ns=new Map;s.set(a,i)}else s=Ns,i=s.get(a),i||(i=new Map,s.set(a,i));if(i.has(t))return i;for(i.set(t,null),a=a.getElementsByTagName(t),s=0;s<a.length;s++){var f=a[s];if(!(f[Qo]||f[Fe]||t==="link"&&f.getAttribute("rel")==="stylesheet")&&f.namespaceURI!=="http://www.w3.org/2000/svg"){var g=f.getAttribute(e)||"";g=t+g;var b=i.get(g);b?b.push(f):i.set(g,[f])}}return i}function Wg(t,e,a){t=t.ownerDocument||t,t.head.insertBefore(a,e==="title"?t.querySelector("head > title"):null)}function oS(t,e,a){if(a===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Kg(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Ui=null;function iS(){}function lS(t,e,a){if(Ui===null)throw Error(l(475));var i=Ui;if(e.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var s=So(a.href),f=t.querySelector($i(s));if(f){t=f._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(i.count++,i=js.bind(i),t.then(i,i)),e.state.loading|=4,e.instance=f,Ge(f);return}f=t.ownerDocument||t,a=Yg(a),(s=Un.get(s))&&Uf(a,s),f=f.createElement("link"),Ge(f);var g=f;g._p=new Promise(function(b,N){g.onload=b,g.onerror=N}),Ke(f,"link",a),e.instance=f}i.stylesheets===null&&(i.stylesheets=new Map),i.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(i.count++,e=js.bind(i),t.addEventListener("load",e),t.addEventListener("error",e))}}function sS(){if(Ui===null)throw Error(l(475));var t=Ui;return t.stylesheets&&t.count===0&&Hf(t,t.stylesheets),0<t.count?function(e){var a=setTimeout(function(){if(t.stylesheets&&Hf(t,t.stylesheets),t.unsuspend){var i=t.unsuspend;t.unsuspend=null,i()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(a)}}:null}function js(){if(this.count--,this.count===0){if(this.stylesheets)Hf(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var ks=null;function Hf(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,ks=new Map,e.forEach(uS,t),ks=null,js.call(t))}function uS(t,e){if(!(e.state.loading&4)){var a=ks.get(t);if(a)var i=a.get(null);else{a=new Map,ks.set(t,a);for(var s=t.querySelectorAll("link[data-precedence],style[data-precedence]"),f=0;f<s.length;f++){var g=s[f];(g.nodeName==="LINK"||g.getAttribute("media")!=="not all")&&(a.set(g.dataset.precedence,g),i=g)}i&&a.set(null,i)}s=e.instance,g=s.getAttribute("data-precedence"),f=a.get(g)||i,f===i&&a.set(null,s),a.set(g,s),this.count++,i=js.bind(this),s.addEventListener("load",i),s.addEventListener("error",i),f?f.parentNode.insertBefore(s,f.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(s,t.firstChild)),e.state.loading|=4}}var Li={$$typeof:M,Provider:null,Consumer:null,_currentValue:ot,_currentValue2:ot,_threadCount:0};function cS(t,e,a,i,s,f,g,b){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Wo(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Wo(0),this.hiddenUpdates=Wo(null),this.identifierPrefix=i,this.onUncaughtError=s,this.onCaughtError=f,this.onRecoverableError=g,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=b,this.incompleteTransitions=new Map}function Qg(t,e,a,i,s,f,g,b,N,G,tt,rt){return t=new cS(t,e,a,g,b,N,G,rt),e=1,f===!0&&(e|=24),f=yn(3,null,null,e),t.current=f,f.stateNode=t,e=Sc(),e.refCount++,t.pooledCache=e,e.refCount++,f.memoizedState={element:i,isDehydrated:a,cache:e},Ec(f),t}function Zg(t){return t?(t=Zr,t):Zr}function Fg(t,e,a,i,s,f){s=Zg(s),i.context===null?i.context=s:i.pendingContext=s,i=Ua(e),i.payload={element:a},f=f===void 0?null:f,f!==null&&(i.callback=f),a=La(t,i,e),a!==null&&(Cn(a,t,e),mi(a,t,e))}function Jg(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var a=t.retryLane;t.retryLane=a!==0&&a<e?a:e}}function qf(t,e){Jg(t,e),(t=t.alternate)&&Jg(t,e)}function ty(t){if(t.tag===13){var e=Qr(t,67108864);e!==null&&Cn(e,t,67108864),qf(t,67108864)}}var $s=!0;function fS(t,e,a,i){var s=B.T;B.T=null;var f=V.p;try{V.p=2,Pf(t,e,a,i)}finally{V.p=f,B.T=s}}function dS(t,e,a,i){var s=B.T;B.T=null;var f=V.p;try{V.p=8,Pf(t,e,a,i)}finally{V.p=f,B.T=s}}function Pf(t,e,a,i){if($s){var s=Gf(i);if(s===null)Mf(t,e,i,_s,a),ny(t,i);else if(hS(s,t,e,a,i))i.stopPropagation();else if(ny(t,i),e&4&&-1<pS.indexOf(t)){for(;s!==null;){var f=Ur(s);if(f!==null)switch(f.tag){case 3:if(f=f.stateNode,f.current.memoizedState.isDehydrated){var g=gt(f.pendingLanes);if(g!==0){var b=f;for(b.pendingLanes|=2,b.entangledLanes|=2;g;){var N=1<<31-ae(g);b.entanglements[1]|=N,g&=~N}Jn(f),(oe&6)===0&&(Ss=Lt()+500,Bi(0))}}break;case 13:b=Qr(f,2),b!==null&&Cn(b,f,2),Cs(),qf(f,2)}if(f=Gf(i),f===null&&Mf(t,e,i,_s,a),f===s)break;s=f}s!==null&&i.stopPropagation()}else Mf(t,e,i,null,a)}}function Gf(t){return t=Iu(t),Vf(t)}var _s=null;function Vf(t){if(_s=null,t=_r(t),t!==null){var e=c(t);if(e===null)t=null;else{var a=e.tag;if(a===13){if(t=d(e),t!==null)return t;t=null}else if(a===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return _s=t,null}function ey(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Kt()){case Qt:return 2;case Yt:return 8;case _t:case dt:return 32;case Ue:return 268435456;default:return 32}default:return 32}}var Yf=!1,Fa=null,Ja=null,tr=null,Hi=new Map,qi=new Map,er=[],pS="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function ny(t,e){switch(t){case"focusin":case"focusout":Fa=null;break;case"dragenter":case"dragleave":Ja=null;break;case"mouseover":case"mouseout":tr=null;break;case"pointerover":case"pointerout":Hi.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":qi.delete(e.pointerId)}}function Pi(t,e,a,i,s,f){return t===null||t.nativeEvent!==f?(t={blockedOn:e,domEventName:a,eventSystemFlags:i,nativeEvent:f,targetContainers:[s]},e!==null&&(e=Ur(e),e!==null&&ty(e)),t):(t.eventSystemFlags|=i,e=t.targetContainers,s!==null&&e.indexOf(s)===-1&&e.push(s),t)}function hS(t,e,a,i,s){switch(e){case"focusin":return Fa=Pi(Fa,t,e,a,i,s),!0;case"dragenter":return Ja=Pi(Ja,t,e,a,i,s),!0;case"mouseover":return tr=Pi(tr,t,e,a,i,s),!0;case"pointerover":var f=s.pointerId;return Hi.set(f,Pi(Hi.get(f)||null,t,e,a,i,s)),!0;case"gotpointercapture":return f=s.pointerId,qi.set(f,Pi(qi.get(f)||null,t,e,a,i,s)),!0}return!1}function ay(t){var e=_r(t.target);if(e!==null){var a=c(e);if(a!==null){if(e=a.tag,e===13){if(e=d(a),e!==null){t.blockedOn=e,Bl(t.priority,function(){if(a.tag===13){var i=xn();i=Ko(i);var s=Qr(a,i);s!==null&&Cn(s,a,i),qf(a,i)}});return}}else if(e===3&&a.stateNode.current.memoizedState.isDehydrated){t.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Us(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var a=Gf(t.nativeEvent);if(a===null){a=t.nativeEvent;var i=new a.constructor(a.type,a);Xu=i,a.target.dispatchEvent(i),Xu=null}else return e=Ur(a),e!==null&&ty(e),t.blockedOn=a,!1;e.shift()}return!0}function ry(t,e,a){Us(t)&&a.delete(e)}function mS(){Yf=!1,Fa!==null&&Us(Fa)&&(Fa=null),Ja!==null&&Us(Ja)&&(Ja=null),tr!==null&&Us(tr)&&(tr=null),Hi.forEach(ry),qi.forEach(ry)}function Ls(t,e){t.blockedOn===e&&(t.blockedOn=null,Yf||(Yf=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,mS)))}var Hs=null;function oy(t){Hs!==t&&(Hs=t,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){Hs===t&&(Hs=null);for(var e=0;e<t.length;e+=3){var a=t[e],i=t[e+1],s=t[e+2];if(typeof i!="function"){if(Vf(i||a)===null)continue;break}var f=Ur(a);f!==null&&(t.splice(e,3),e-=3,Gc(f,{pending:!0,data:s,method:a.method,action:i},i,s))}}))}function Gi(t){function e(N){return Ls(N,t)}Fa!==null&&Ls(Fa,t),Ja!==null&&Ls(Ja,t),tr!==null&&Ls(tr,t),Hi.forEach(e),qi.forEach(e);for(var a=0;a<er.length;a++){var i=er[a];i.blockedOn===t&&(i.blockedOn=null)}for(;0<er.length&&(a=er[0],a.blockedOn===null);)ay(a),a.blockedOn===null&&er.shift();if(a=(t.ownerDocument||t).$$reactFormReplay,a!=null)for(i=0;i<a.length;i+=3){var s=a[i],f=a[i+1],g=s[ln]||null;if(typeof f=="function")g||oy(a);else if(g){var b=null;if(f&&f.hasAttribute("formAction")){if(s=f,g=f[ln]||null)b=g.formAction;else if(Vf(s)!==null)continue}else b=g.action;typeof b=="function"?a[i+1]=b:(a.splice(i,3),i-=3),oy(a)}}}function Xf(t){this._internalRoot=t}qs.prototype.render=Xf.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(l(409));var a=e.current,i=xn();Fg(a,i,t,e,null,null)},qs.prototype.unmount=Xf.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Fg(t.current,2,null,t,null,null),Cs(),e[$r]=null}};function qs(t){this._internalRoot=t}qs.prototype.unstable_scheduleHydration=function(t){if(t){var e=on();t={blockedOn:null,target:t,priority:e};for(var a=0;a<er.length&&e!==0&&e<er[a].priority;a++);er.splice(a,0,t),a===0&&ay(t)}};var iy=r.version;if(iy!=="19.1.0")throw Error(l(527,iy,"19.1.0"));V.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(l(188)):(t=Object.keys(t).join(","),Error(l(268,t)));return t=h(e),t=t!==null?m(t):null,t=t===null?null:t.stateNode,t};var gS={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:B,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ps=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ps.isDisabled&&Ps.supportsFiber)try{Le=Ps.inject(gS),ne=Ps}catch{}}return Xi.createRoot=function(t,e){if(!u(t))throw Error(l(299));var a=!1,i="",s=Cm,f=Tm,g=Em,b=null;return e!=null&&(e.unstable_strictMode===!0&&(a=!0),e.identifierPrefix!==void 0&&(i=e.identifierPrefix),e.onUncaughtError!==void 0&&(s=e.onUncaughtError),e.onCaughtError!==void 0&&(f=e.onCaughtError),e.onRecoverableError!==void 0&&(g=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(b=e.unstable_transitionCallbacks)),e=Qg(t,1,!1,null,null,a,i,s,f,g,b,null),t[$r]=e.current,Of(t),new Xf(e)},Xi.hydrateRoot=function(t,e,a){if(!u(t))throw Error(l(299));var i=!1,s="",f=Cm,g=Tm,b=Em,N=null,G=null;return a!=null&&(a.unstable_strictMode===!0&&(i=!0),a.identifierPrefix!==void 0&&(s=a.identifierPrefix),a.onUncaughtError!==void 0&&(f=a.onUncaughtError),a.onCaughtError!==void 0&&(g=a.onCaughtError),a.onRecoverableError!==void 0&&(b=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(N=a.unstable_transitionCallbacks),a.formState!==void 0&&(G=a.formState)),e=Qg(t,1,!0,e,a??null,i,s,f,g,b,N,G),e.context=Zg(null),a=e.current,i=xn(),i=Ko(i),s=Ua(i),s.callback=null,La(a,s,i),a=i,e.current.lanes=a,cr(e,a),Jn(e),t[$r]=e.current,Of(t),new qs(e)},Xi.version="19.1.0",Xi}var gy;function OS(){if(gy)return Kf.exports;gy=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),Kf.exports=AS(),Kf.exports}var MS=OS();function za(n,...r){const o=new URL(`https://mui.com/production-error/?code=${n}`);return r.forEach(l=>o.searchParams.append("args[]",l)),`Minified MUI error #${n}; visit ${o} for the full message.`}const ia="$$material";function lu(){return lu=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var o=arguments[r];for(var l in o)({}).hasOwnProperty.call(o,l)&&(n[l]=o[l])}return n},lu.apply(null,arguments)}function zS(n){if(n.sheet)return n.sheet;for(var r=0;r<document.styleSheets.length;r++)if(document.styleSheets[r].ownerNode===n)return document.styleSheets[r]}function DS(n){var r=document.createElement("style");return r.setAttribute("data-emotion",n.key),n.nonce!==void 0&&r.setAttribute("nonce",n.nonce),r.appendChild(document.createTextNode("")),r.setAttribute("data-s",""),r}var BS=function(){function n(o){var l=this;this._insertTag=function(u){var c;l.tags.length===0?l.insertionPoint?c=l.insertionPoint.nextSibling:l.prepend?c=l.container.firstChild:c=l.before:c=l.tags[l.tags.length-1].nextSibling,l.container.insertBefore(u,c),l.tags.push(u)},this.isSpeedy=o.speedy===void 0?!0:o.speedy,this.tags=[],this.ctr=0,this.nonce=o.nonce,this.key=o.key,this.container=o.container,this.prepend=o.prepend,this.insertionPoint=o.insertionPoint,this.before=null}var r=n.prototype;return r.hydrate=function(l){l.forEach(this._insertTag)},r.insert=function(l){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(DS(this));var u=this.tags[this.tags.length-1];if(this.isSpeedy){var c=zS(u);try{c.insertRule(l,c.cssRules.length)}catch{}}else u.appendChild(document.createTextNode(l));this.ctr++},r.flush=function(){this.tags.forEach(function(l){var u;return(u=l.parentNode)==null?void 0:u.removeChild(l)}),this.tags=[],this.ctr=0},n}(),en="-ms-",su="-moz-",Jt="-webkit-",Hv="comm",Ud="rule",Ld="decl",NS="@import",qv="@keyframes",jS="@layer",kS=Math.abs,vu=String.fromCharCode,$S=Object.assign;function _S(n,r){return Qe(n,0)^45?(((r<<2^Qe(n,0))<<2^Qe(n,1))<<2^Qe(n,2))<<2^Qe(n,3):0}function Pv(n){return n.trim()}function US(n,r){return(n=r.exec(n))?n[0]:n}function te(n,r,o){return n.replace(r,o)}function pd(n,r){return n.indexOf(r)}function Qe(n,r){return n.charCodeAt(r)|0}function cl(n,r,o){return n.slice(r,o)}function na(n){return n.length}function Hd(n){return n.length}function Gs(n,r){return r.push(n),n}function LS(n,r){return n.map(r).join("")}var bu=1,jo=1,Gv=0,hn=0,_e=0,qo="";function Su(n,r,o,l,u,c,d){return{value:n,root:r,parent:o,type:l,props:u,children:c,line:bu,column:jo,length:d,return:""}}function Ii(n,r){return $S(Su("",null,null,"",null,null,0),n,{length:-n.length},r)}function HS(){return _e}function qS(){return _e=hn>0?Qe(qo,--hn):0,jo--,_e===10&&(jo=1,bu--),_e}function Rn(){return _e=hn<Gv?Qe(qo,hn++):0,jo++,_e===10&&(jo=1,bu++),_e}function la(){return Qe(qo,hn)}function tu(){return hn}function yl(n,r){return cl(qo,n,r)}function fl(n){switch(n){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Vv(n){return bu=jo=1,Gv=na(qo=n),hn=0,[]}function Yv(n){return qo="",n}function eu(n){return Pv(yl(hn-1,hd(n===91?n+2:n===40?n+1:n)))}function PS(n){for(;(_e=la())&&_e<33;)Rn();return fl(n)>2||fl(_e)>3?"":" "}function GS(n,r){for(;--r&&Rn()&&!(_e<48||_e>102||_e>57&&_e<65||_e>70&&_e<97););return yl(n,tu()+(r<6&&la()==32&&Rn()==32))}function hd(n){for(;Rn();)switch(_e){case n:return hn;case 34:case 39:n!==34&&n!==39&&hd(_e);break;case 40:n===41&&hd(n);break;case 92:Rn();break}return hn}function VS(n,r){for(;Rn()&&n+_e!==57;)if(n+_e===84&&la()===47)break;return"/*"+yl(r,hn-1)+"*"+vu(n===47?n:Rn())}function YS(n){for(;!fl(la());)Rn();return yl(n,hn)}function XS(n){return Yv(nu("",null,null,null,[""],n=Vv(n),0,[0],n))}function nu(n,r,o,l,u,c,d,p,h){for(var m=0,y=0,S=d,E=0,R=0,x=0,C=1,D=1,j=1,U=0,M="",O=u,A=c,$=l,H=M;D;)switch(x=U,U=Rn()){case 40:if(x!=108&&Qe(H,S-1)==58){pd(H+=te(eu(U),"&","&\f"),"&\f")!=-1&&(j=-1);break}case 34:case 39:case 91:H+=eu(U);break;case 9:case 10:case 13:case 32:H+=PS(x);break;case 92:H+=GS(tu()-1,7);continue;case 47:switch(la()){case 42:case 47:Gs(IS(VS(Rn(),tu()),r,o),h);break;default:H+="/"}break;case 123*C:p[m++]=na(H)*j;case 125*C:case 59:case 0:switch(U){case 0:case 125:D=0;case 59+y:j==-1&&(H=te(H,/\f/g,"")),R>0&&na(H)-S&&Gs(R>32?vy(H+";",l,o,S-1):vy(te(H," ","")+";",l,o,S-2),h);break;case 59:H+=";";default:if(Gs($=yy(H,r,o,m,y,u,p,M,O=[],A=[],S),c),U===123)if(y===0)nu(H,r,$,$,O,c,S,p,A);else switch(E===99&&Qe(H,3)===110?100:E){case 100:case 108:case 109:case 115:nu(n,$,$,l&&Gs(yy(n,$,$,0,0,u,p,M,u,O=[],S),A),u,A,S,p,l?O:A);break;default:nu(H,$,$,$,[""],A,0,p,A)}}m=y=R=0,C=j=1,M=H="",S=d;break;case 58:S=1+na(H),R=x;default:if(C<1){if(U==123)--C;else if(U==125&&C++==0&&qS()==125)continue}switch(H+=vu(U),U*C){case 38:j=y>0?1:(H+="\f",-1);break;case 44:p[m++]=(na(H)-1)*j,j=1;break;case 64:la()===45&&(H+=eu(Rn())),E=la(),y=S=na(M=H+=YS(tu())),U++;break;case 45:x===45&&na(H)==2&&(C=0)}}return c}function yy(n,r,o,l,u,c,d,p,h,m,y){for(var S=u-1,E=u===0?c:[""],R=Hd(E),x=0,C=0,D=0;x<l;++x)for(var j=0,U=cl(n,S+1,S=kS(C=d[x])),M=n;j<R;++j)(M=Pv(C>0?E[j]+" "+U:te(U,/&\f/g,E[j])))&&(h[D++]=M);return Su(n,r,o,u===0?Ud:p,h,m,y)}function IS(n,r,o){return Su(n,r,o,Hv,vu(HS()),cl(n,2,-2),0)}function vy(n,r,o,l){return Su(n,r,o,Ld,cl(n,0,l),cl(n,l+1,-1),l)}function Do(n,r){for(var o="",l=Hd(n),u=0;u<l;u++)o+=r(n[u],u,n,r)||"";return o}function WS(n,r,o,l){switch(n.type){case jS:if(n.children.length)break;case NS:case Ld:return n.return=n.return||n.value;case Hv:return"";case qv:return n.return=n.value+"{"+Do(n.children,l)+"}";case Ud:n.value=n.props.join(",")}return na(o=Do(n.children,l))?n.return=n.value+"{"+o+"}":""}function KS(n){var r=Hd(n);return function(o,l,u,c){for(var d="",p=0;p<r;p++)d+=n[p](o,l,u,c)||"";return d}}function QS(n){return function(r){r.root||(r=r.return)&&n(r)}}function Xv(n){var r=Object.create(null);return function(o){return r[o]===void 0&&(r[o]=n(o)),r[o]}}var ZS=function(r,o,l){for(var u=0,c=0;u=c,c=la(),u===38&&c===12&&(o[l]=1),!fl(c);)Rn();return yl(r,hn)},FS=function(r,o){var l=-1,u=44;do switch(fl(u)){case 0:u===38&&la()===12&&(o[l]=1),r[l]+=ZS(hn-1,o,l);break;case 2:r[l]+=eu(u);break;case 4:if(u===44){r[++l]=la()===58?"&\f":"",o[l]=r[l].length;break}default:r[l]+=vu(u)}while(u=Rn());return r},JS=function(r,o){return Yv(FS(Vv(r),o))},by=new WeakMap,tx=function(r){if(!(r.type!=="rule"||!r.parent||r.length<1)){for(var o=r.value,l=r.parent,u=r.column===l.column&&r.line===l.line;l.type!=="rule";)if(l=l.parent,!l)return;if(!(r.props.length===1&&o.charCodeAt(0)!==58&&!by.get(l))&&!u){by.set(r,!0);for(var c=[],d=JS(o,c),p=l.props,h=0,m=0;h<d.length;h++)for(var y=0;y<p.length;y++,m++)r.props[m]=c[h]?d[h].replace(/&\f/g,p[y]):p[y]+" "+d[h]}}},ex=function(r){if(r.type==="decl"){var o=r.value;o.charCodeAt(0)===108&&o.charCodeAt(2)===98&&(r.return="",r.value="")}};function Iv(n,r){switch(_S(n,r)){case 5103:return Jt+"print-"+n+n;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return Jt+n+n;case 5349:case 4246:case 4810:case 6968:case 2756:return Jt+n+su+n+en+n+n;case 6828:case 4268:return Jt+n+en+n+n;case 6165:return Jt+n+en+"flex-"+n+n;case 5187:return Jt+n+te(n,/(\w+).+(:[^]+)/,Jt+"box-$1$2"+en+"flex-$1$2")+n;case 5443:return Jt+n+en+"flex-item-"+te(n,/flex-|-self/,"")+n;case 4675:return Jt+n+en+"flex-line-pack"+te(n,/align-content|flex-|-self/,"")+n;case 5548:return Jt+n+en+te(n,"shrink","negative")+n;case 5292:return Jt+n+en+te(n,"basis","preferred-size")+n;case 6060:return Jt+"box-"+te(n,"-grow","")+Jt+n+en+te(n,"grow","positive")+n;case 4554:return Jt+te(n,/([^-])(transform)/g,"$1"+Jt+"$2")+n;case 6187:return te(te(te(n,/(zoom-|grab)/,Jt+"$1"),/(image-set)/,Jt+"$1"),n,"")+n;case 5495:case 3959:return te(n,/(image-set\([^]*)/,Jt+"$1$`$1");case 4968:return te(te(n,/(.+:)(flex-)?(.*)/,Jt+"box-pack:$3"+en+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+Jt+n+n;case 4095:case 3583:case 4068:case 2532:return te(n,/(.+)-inline(.+)/,Jt+"$1$2")+n;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(na(n)-1-r>6)switch(Qe(n,r+1)){case 109:if(Qe(n,r+4)!==45)break;case 102:return te(n,/(.+:)(.+)-([^]+)/,"$1"+Jt+"$2-$3$1"+su+(Qe(n,r+3)==108?"$3":"$2-$3"))+n;case 115:return~pd(n,"stretch")?Iv(te(n,"stretch","fill-available"),r)+n:n}break;case 4949:if(Qe(n,r+1)!==115)break;case 6444:switch(Qe(n,na(n)-3-(~pd(n,"!important")&&10))){case 107:return te(n,":",":"+Jt)+n;case 101:return te(n,/(.+:)([^;!]+)(;|!.+)?/,"$1"+Jt+(Qe(n,14)===45?"inline-":"")+"box$3$1"+Jt+"$2$3$1"+en+"$2box$3")+n}break;case 5936:switch(Qe(n,r+11)){case 114:return Jt+n+en+te(n,/[svh]\w+-[tblr]{2}/,"tb")+n;case 108:return Jt+n+en+te(n,/[svh]\w+-[tblr]{2}/,"tb-rl")+n;case 45:return Jt+n+en+te(n,/[svh]\w+-[tblr]{2}/,"lr")+n}return Jt+n+en+n+n}return n}var nx=function(r,o,l,u){if(r.length>-1&&!r.return)switch(r.type){case Ld:r.return=Iv(r.value,r.length);break;case qv:return Do([Ii(r,{value:te(r.value,"@","@"+Jt)})],u);case Ud:if(r.length)return LS(r.props,function(c){switch(US(c,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return Do([Ii(r,{props:[te(c,/:(read-\w+)/,":"+su+"$1")]})],u);case"::placeholder":return Do([Ii(r,{props:[te(c,/:(plac\w+)/,":"+Jt+"input-$1")]}),Ii(r,{props:[te(c,/:(plac\w+)/,":"+su+"$1")]}),Ii(r,{props:[te(c,/:(plac\w+)/,en+"input-$1")]})],u)}return""})}},ax=[nx],rx=function(r){var o=r.key;if(o==="css"){var l=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(l,function(C){var D=C.getAttribute("data-emotion");D.indexOf(" ")!==-1&&(document.head.appendChild(C),C.setAttribute("data-s",""))})}var u=r.stylisPlugins||ax,c={},d,p=[];d=r.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+o+' "]'),function(C){for(var D=C.getAttribute("data-emotion").split(" "),j=1;j<D.length;j++)c[D[j]]=!0;p.push(C)});var h,m=[tx,ex];{var y,S=[WS,QS(function(C){y.insert(C)})],E=KS(m.concat(u,S)),R=function(D){return Do(XS(D),E)};h=function(D,j,U,M){y=U,R(D?D+"{"+j.styles+"}":j.styles),M&&(x.inserted[j.name]=!0)}}var x={key:o,sheet:new BS({key:o,container:d,nonce:r.nonce,speedy:r.speedy,prepend:r.prepend,insertionPoint:r.insertionPoint}),nonce:r.nonce,inserted:c,registered:{},insert:h};return x.sheet.hydrate(p),x},Jf={exports:{}},ee={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sy;function ox(){if(Sy)return ee;Sy=1;var n=typeof Symbol=="function"&&Symbol.for,r=n?Symbol.for("react.element"):60103,o=n?Symbol.for("react.portal"):60106,l=n?Symbol.for("react.fragment"):60107,u=n?Symbol.for("react.strict_mode"):60108,c=n?Symbol.for("react.profiler"):60114,d=n?Symbol.for("react.provider"):60109,p=n?Symbol.for("react.context"):60110,h=n?Symbol.for("react.async_mode"):60111,m=n?Symbol.for("react.concurrent_mode"):60111,y=n?Symbol.for("react.forward_ref"):60112,S=n?Symbol.for("react.suspense"):60113,E=n?Symbol.for("react.suspense_list"):60120,R=n?Symbol.for("react.memo"):60115,x=n?Symbol.for("react.lazy"):60116,C=n?Symbol.for("react.block"):60121,D=n?Symbol.for("react.fundamental"):60117,j=n?Symbol.for("react.responder"):60118,U=n?Symbol.for("react.scope"):60119;function M(A){if(typeof A=="object"&&A!==null){var $=A.$$typeof;switch($){case r:switch(A=A.type,A){case h:case m:case l:case c:case u:case S:return A;default:switch(A=A&&A.$$typeof,A){case p:case y:case x:case R:case d:return A;default:return $}}case o:return $}}}function O(A){return M(A)===m}return ee.AsyncMode=h,ee.ConcurrentMode=m,ee.ContextConsumer=p,ee.ContextProvider=d,ee.Element=r,ee.ForwardRef=y,ee.Fragment=l,ee.Lazy=x,ee.Memo=R,ee.Portal=o,ee.Profiler=c,ee.StrictMode=u,ee.Suspense=S,ee.isAsyncMode=function(A){return O(A)||M(A)===h},ee.isConcurrentMode=O,ee.isContextConsumer=function(A){return M(A)===p},ee.isContextProvider=function(A){return M(A)===d},ee.isElement=function(A){return typeof A=="object"&&A!==null&&A.$$typeof===r},ee.isForwardRef=function(A){return M(A)===y},ee.isFragment=function(A){return M(A)===l},ee.isLazy=function(A){return M(A)===x},ee.isMemo=function(A){return M(A)===R},ee.isPortal=function(A){return M(A)===o},ee.isProfiler=function(A){return M(A)===c},ee.isStrictMode=function(A){return M(A)===u},ee.isSuspense=function(A){return M(A)===S},ee.isValidElementType=function(A){return typeof A=="string"||typeof A=="function"||A===l||A===m||A===c||A===u||A===S||A===E||typeof A=="object"&&A!==null&&(A.$$typeof===x||A.$$typeof===R||A.$$typeof===d||A.$$typeof===p||A.$$typeof===y||A.$$typeof===D||A.$$typeof===j||A.$$typeof===U||A.$$typeof===C)},ee.typeOf=M,ee}var xy;function ix(){return xy||(xy=1,Jf.exports=ox()),Jf.exports}var td,Cy;function lx(){if(Cy)return td;Cy=1;var n=ix(),r={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},o={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},l={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},u={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},c={};c[n.ForwardRef]=l,c[n.Memo]=u;function d(x){return n.isMemo(x)?u:c[x.$$typeof]||r}var p=Object.defineProperty,h=Object.getOwnPropertyNames,m=Object.getOwnPropertySymbols,y=Object.getOwnPropertyDescriptor,S=Object.getPrototypeOf,E=Object.prototype;function R(x,C,D){if(typeof C!="string"){if(E){var j=S(C);j&&j!==E&&R(x,j,D)}var U=h(C);m&&(U=U.concat(m(C)));for(var M=d(x),O=d(C),A=0;A<U.length;++A){var $=U[A];if(!o[$]&&!(D&&D[$])&&!(O&&O[$])&&!(M&&M[$])){var H=y(C,$);try{p(x,$,H)}catch{}}}}return x}return td=R,td}lx();var sx=!0;function Wv(n,r,o){var l="";return o.split(" ").forEach(function(u){n[u]!==void 0?r.push(n[u]+";"):u&&(l+=u+" ")}),l}var qd=function(r,o,l){var u=r.key+"-"+o.name;(l===!1||sx===!1)&&r.registered[u]===void 0&&(r.registered[u]=o.styles)},Pd=function(r,o,l){qd(r,o,l);var u=r.key+"-"+o.name;if(r.inserted[o.name]===void 0){var c=o;do r.insert(o===c?"."+u:"",c,r.sheet,!0),c=c.next;while(c!==void 0)}};function ux(n){for(var r=0,o,l=0,u=n.length;u>=4;++l,u-=4)o=n.charCodeAt(l)&255|(n.charCodeAt(++l)&255)<<8|(n.charCodeAt(++l)&255)<<16|(n.charCodeAt(++l)&255)<<24,o=(o&65535)*1540483477+((o>>>16)*59797<<16),o^=o>>>24,r=(o&65535)*1540483477+((o>>>16)*59797<<16)^(r&65535)*1540483477+((r>>>16)*59797<<16);switch(u){case 3:r^=(n.charCodeAt(l+2)&255)<<16;case 2:r^=(n.charCodeAt(l+1)&255)<<8;case 1:r^=n.charCodeAt(l)&255,r=(r&65535)*1540483477+((r>>>16)*59797<<16)}return r^=r>>>13,r=(r&65535)*1540483477+((r>>>16)*59797<<16),((r^r>>>15)>>>0).toString(36)}var cx={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},fx=/[A-Z]|^ms/g,dx=/_EMO_([^_]+?)_([^]*?)_EMO_/g,Kv=function(r){return r.charCodeAt(1)===45},Ty=function(r){return r!=null&&typeof r!="boolean"},ed=Xv(function(n){return Kv(n)?n:n.replace(fx,"-$&").toLowerCase()}),Ey=function(r,o){switch(r){case"animation":case"animationName":if(typeof o=="string")return o.replace(dx,function(l,u,c){return aa={name:u,styles:c,next:aa},u})}return cx[r]!==1&&!Kv(r)&&typeof o=="number"&&o!==0?o+"px":o};function dl(n,r,o){if(o==null)return"";var l=o;if(l.__emotion_styles!==void 0)return l;switch(typeof o){case"boolean":return"";case"object":{var u=o;if(u.anim===1)return aa={name:u.name,styles:u.styles,next:aa},u.name;var c=o;if(c.styles!==void 0){var d=c.next;if(d!==void 0)for(;d!==void 0;)aa={name:d.name,styles:d.styles,next:aa},d=d.next;var p=c.styles+";";return p}return px(n,r,o)}case"function":{if(n!==void 0){var h=aa,m=o(n);return aa=h,dl(n,r,m)}break}}var y=o;if(r==null)return y;var S=r[y];return S!==void 0?S:y}function px(n,r,o){var l="";if(Array.isArray(o))for(var u=0;u<o.length;u++)l+=dl(n,r,o[u])+";";else for(var c in o){var d=o[c];if(typeof d!="object"){var p=d;r!=null&&r[p]!==void 0?l+=c+"{"+r[p]+"}":Ty(p)&&(l+=ed(c)+":"+Ey(c,p)+";")}else if(Array.isArray(d)&&typeof d[0]=="string"&&(r==null||r[d[0]]===void 0))for(var h=0;h<d.length;h++)Ty(d[h])&&(l+=ed(c)+":"+Ey(c,d[h])+";");else{var m=dl(n,r,d);switch(c){case"animation":case"animationName":{l+=ed(c)+":"+m+";";break}default:l+=c+"{"+m+"}"}}}return l}var Ry=/label:\s*([^\s;{]+)\s*(;|$)/g,aa;function vl(n,r,o){if(n.length===1&&typeof n[0]=="object"&&n[0]!==null&&n[0].styles!==void 0)return n[0];var l=!0,u="";aa=void 0;var c=n[0];if(c==null||c.raw===void 0)l=!1,u+=dl(o,r,c);else{var d=c;u+=d[0]}for(var p=1;p<n.length;p++)if(u+=dl(o,r,n[p]),l){var h=c;u+=h[p]}Ry.lastIndex=0;for(var m="",y;(y=Ry.exec(u))!==null;)m+="-"+y[1];var S=ux(u)+m;return{name:S,styles:u,next:aa}}var hx=function(r){return r()},Qv=dd.useInsertionEffect?dd.useInsertionEffect:!1,Zv=Qv||hx,wy=Qv||w.useLayoutEffect,Fv=w.createContext(typeof HTMLElement<"u"?rx({key:"css"}):null);Fv.Provider;var Gd=function(r){return w.forwardRef(function(o,l){var u=w.useContext(Fv);return r(o,u,l)})},bl=w.createContext({}),Vd={}.hasOwnProperty,md="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",mx=function(r,o){var l={};for(var u in o)Vd.call(o,u)&&(l[u]=o[u]);return l[md]=r,l},gx=function(r){var o=r.cache,l=r.serialized,u=r.isStringTag;return qd(o,l,u),Zv(function(){return Pd(o,l,u)}),null},yx=Gd(function(n,r,o){var l=n.css;typeof l=="string"&&r.registered[l]!==void 0&&(l=r.registered[l]);var u=n[md],c=[l],d="";typeof n.className=="string"?d=Wv(r.registered,c,n.className):n.className!=null&&(d=n.className+" ");var p=vl(c,void 0,w.useContext(bl));d+=r.key+"-"+p.name;var h={};for(var m in n)Vd.call(n,m)&&m!=="css"&&m!==md&&(h[m]=n[m]);return h.className=d,o&&(h.ref=o),w.createElement(w.Fragment,null,w.createElement(gx,{cache:r,serialized:p,isStringTag:typeof u=="string"}),w.createElement(u,h))}),vx=yx,Ay=function(r,o){var l=arguments;if(o==null||!Vd.call(o,"css"))return w.createElement.apply(void 0,l);var u=l.length,c=new Array(u);c[0]=vx,c[1]=mx(r,o);for(var d=2;d<u;d++)c[d]=l[d];return w.createElement.apply(null,c)};(function(n){var r;r||(r=n.JSX||(n.JSX={}))})(Ay||(Ay={}));var bx=Gd(function(n,r){var o=n.styles,l=vl([o],void 0,w.useContext(bl)),u=w.useRef();return wy(function(){var c=r.key+"-global",d=new r.sheet.constructor({key:c,nonce:r.sheet.nonce,container:r.sheet.container,speedy:r.sheet.isSpeedy}),p=!1,h=document.querySelector('style[data-emotion="'+c+" "+l.name+'"]');return r.sheet.tags.length&&(d.before=r.sheet.tags[0]),h!==null&&(p=!0,h.setAttribute("data-emotion",c),d.hydrate([h])),u.current=[d,p],function(){d.flush()}},[r]),wy(function(){var c=u.current,d=c[0],p=c[1];if(p){c[1]=!1;return}if(l.next!==void 0&&Pd(r,l.next,!0),d.tags.length){var h=d.tags[d.tags.length-1].nextElementSibling;d.before=h,d.flush()}r.insert("",l,d,!1)},[r,l.name]),null});function Yd(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return vl(r)}function Sl(){var n=Yd.apply(void 0,arguments),r="animation-"+n.name;return{name:r,styles:"@keyframes "+r+"{"+n.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}var Sx=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,xx=Xv(function(n){return Sx.test(n)||n.charCodeAt(0)===111&&n.charCodeAt(1)===110&&n.charCodeAt(2)<91}),Cx=xx,Tx=function(r){return r!=="theme"},Oy=function(r){return typeof r=="string"&&r.charCodeAt(0)>96?Cx:Tx},My=function(r,o,l){var u;if(o){var c=o.shouldForwardProp;u=r.__emotion_forwardProp&&c?function(d){return r.__emotion_forwardProp(d)&&c(d)}:c}return typeof u!="function"&&l&&(u=r.__emotion_forwardProp),u},Ex=function(r){var o=r.cache,l=r.serialized,u=r.isStringTag;return qd(o,l,u),Zv(function(){return Pd(o,l,u)}),null},Rx=function n(r,o){var l=r.__emotion_real===r,u=l&&r.__emotion_base||r,c,d;o!==void 0&&(c=o.label,d=o.target);var p=My(r,o,l),h=p||Oy(u),m=!h("as");return function(){var y=arguments,S=l&&r.__emotion_styles!==void 0?r.__emotion_styles.slice(0):[];if(c!==void 0&&S.push("label:"+c+";"),y[0]==null||y[0].raw===void 0)S.push.apply(S,y);else{var E=y[0];S.push(E[0]);for(var R=y.length,x=1;x<R;x++)S.push(y[x],E[x])}var C=Gd(function(D,j,U){var M=m&&D.as||u,O="",A=[],$=D;if(D.theme==null){$={};for(var H in D)$[H]=D[H];$.theme=w.useContext(bl)}typeof D.className=="string"?O=Wv(j.registered,A,D.className):D.className!=null&&(O=D.className+" ");var P=vl(S.concat(A),j.registered,$);O+=j.key+"-"+P.name,d!==void 0&&(O+=" "+d);var K=m&&p===void 0?Oy(M):h,v={};for(var _ in D)m&&_==="as"||K(_)&&(v[_]=D[_]);return v.className=O,U&&(v.ref=U),w.createElement(w.Fragment,null,w.createElement(Ex,{cache:j,serialized:P,isStringTag:typeof M=="string"}),w.createElement(M,v))});return C.displayName=c!==void 0?c:"Styled("+(typeof u=="string"?u:u.displayName||u.name||"Component")+")",C.defaultProps=r.defaultProps,C.__emotion_real=C,C.__emotion_base=u,C.__emotion_styles=S,C.__emotion_forwardProp=p,Object.defineProperty(C,"toString",{value:function(){return"."+d}}),C.withComponent=function(D,j){var U=n(D,lu({},o,j,{shouldForwardProp:My(C,j,!0)}));return U.apply(void 0,S)},C}},wx=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"],gd=Rx.bind(null);wx.forEach(function(n){gd[n]=gd(n)});function Ax(n){return n==null||Object.keys(n).length===0}function Jv(n){const{styles:r,defaultTheme:o={}}=n,l=typeof r=="function"?u=>r(Ax(u)?o:u):r;return T.jsx(bx,{styles:l})}function t0(n,r){return gd(n,r)}function Ox(n,r){Array.isArray(n.__emotion_styles)&&(n.__emotion_styles=r(n.__emotion_styles))}const zy=[];function Dy(n){return zy[0]=n,vl(zy)}var nd={exports:{}},de={};/**
 * @license React
 * react-is.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var By;function Mx(){if(By)return de;By=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),c=Symbol.for("react.consumer"),d=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),h=Symbol.for("react.suspense"),m=Symbol.for("react.suspense_list"),y=Symbol.for("react.memo"),S=Symbol.for("react.lazy"),E=Symbol.for("react.view_transition"),R=Symbol.for("react.client.reference");function x(C){if(typeof C=="object"&&C!==null){var D=C.$$typeof;switch(D){case n:switch(C=C.type,C){case o:case u:case l:case h:case m:case E:return C;default:switch(C=C&&C.$$typeof,C){case d:case p:case S:case y:return C;case c:return C;default:return D}}case r:return D}}}return de.ContextConsumer=c,de.ContextProvider=d,de.Element=n,de.ForwardRef=p,de.Fragment=o,de.Lazy=S,de.Memo=y,de.Portal=r,de.Profiler=u,de.StrictMode=l,de.Suspense=h,de.SuspenseList=m,de.isContextConsumer=function(C){return x(C)===c},de.isContextProvider=function(C){return x(C)===d},de.isElement=function(C){return typeof C=="object"&&C!==null&&C.$$typeof===n},de.isForwardRef=function(C){return x(C)===p},de.isFragment=function(C){return x(C)===o},de.isLazy=function(C){return x(C)===S},de.isMemo=function(C){return x(C)===y},de.isPortal=function(C){return x(C)===r},de.isProfiler=function(C){return x(C)===u},de.isStrictMode=function(C){return x(C)===l},de.isSuspense=function(C){return x(C)===h},de.isSuspenseList=function(C){return x(C)===m},de.isValidElementType=function(C){return typeof C=="string"||typeof C=="function"||C===o||C===u||C===l||C===h||C===m||typeof C=="object"&&C!==null&&(C.$$typeof===S||C.$$typeof===y||C.$$typeof===d||C.$$typeof===c||C.$$typeof===p||C.$$typeof===R||C.getModuleId!==void 0)},de.typeOf=x,de}var Ny;function zx(){return Ny||(Ny=1,nd.exports=Mx()),nd.exports}var e0=zx();function ra(n){if(typeof n!="object"||n===null)return!1;const r=Object.getPrototypeOf(n);return(r===null||r===Object.prototype||Object.getPrototypeOf(r)===null)&&!(Symbol.toStringTag in n)&&!(Symbol.iterator in n)}function n0(n){if(w.isValidElement(n)||e0.isValidElementType(n)||!ra(n))return n;const r={};return Object.keys(n).forEach(o=>{r[o]=n0(n[o])}),r}function nn(n,r,o={clone:!0}){const l=o.clone?{...n}:n;return ra(n)&&ra(r)&&Object.keys(r).forEach(u=>{w.isValidElement(r[u])||e0.isValidElementType(r[u])?l[u]=r[u]:ra(r[u])&&Object.prototype.hasOwnProperty.call(n,u)&&ra(n[u])?l[u]=nn(n[u],r[u],o):o.clone?l[u]=ra(r[u])?n0(r[u]):r[u]:l[u]=r[u]}),l}const Dx=n=>{const r=Object.keys(n).map(o=>({key:o,val:n[o]}))||[];return r.sort((o,l)=>o.val-l.val),r.reduce((o,l)=>({...o,[l.key]:l.val}),{})};function Bx(n){const{values:r={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:o="px",step:l=5,...u}=n,c=Dx(r),d=Object.keys(c);function p(E){return`@media (min-width:${typeof r[E]=="number"?r[E]:E}${o})`}function h(E){return`@media (max-width:${(typeof r[E]=="number"?r[E]:E)-l/100}${o})`}function m(E,R){const x=d.indexOf(R);return`@media (min-width:${typeof r[E]=="number"?r[E]:E}${o}) and (max-width:${(x!==-1&&typeof r[d[x]]=="number"?r[d[x]]:R)-l/100}${o})`}function y(E){return d.indexOf(E)+1<d.length?m(E,d[d.indexOf(E)+1]):p(E)}function S(E){const R=d.indexOf(E);return R===0?p(d[1]):R===d.length-1?h(d[R]):m(E,d[d.indexOf(E)+1]).replace("@media","@media not all and")}return{keys:d,values:c,up:p,down:h,between:m,only:y,not:S,unit:o,...u}}function Nx(n,r){if(!n.containerQueries)return r;const o=Object.keys(r).filter(l=>l.startsWith("@container")).sort((l,u)=>{var d,p;const c=/min-width:\s*([0-9.]+)/;return+(((d=l.match(c))==null?void 0:d[1])||0)-+(((p=u.match(c))==null?void 0:p[1])||0)});return o.length?o.reduce((l,u)=>{const c=r[u];return delete l[u],l[u]=c,l},{...r}):r}function jx(n,r){return r==="@"||r.startsWith("@")&&(n.some(o=>r.startsWith(`@${o}`))||!!r.match(/^@\d/))}function kx(n,r){const o=r.match(/^@([^/]+)?\/?(.+)?$/);if(!o)return null;const[,l,u]=o,c=Number.isNaN(+l)?l||0:+l;return n.containerQueries(u).up(c)}function $x(n){const r=(c,d)=>c.replace("@media",d?`@container ${d}`:"@container");function o(c,d){c.up=(...p)=>r(n.breakpoints.up(...p),d),c.down=(...p)=>r(n.breakpoints.down(...p),d),c.between=(...p)=>r(n.breakpoints.between(...p),d),c.only=(...p)=>r(n.breakpoints.only(...p),d),c.not=(...p)=>{const h=r(n.breakpoints.not(...p),d);return h.includes("not all and")?h.replace("not all and ","").replace("min-width:","width<").replace("max-width:","width>").replace("and","or"):h}}const l={},u=c=>(o(l,c),l);return o(u),{...n,containerQueries:u}}const _x={borderRadius:4};function rl(n,r){return r?nn(n,r,{clone:!1}):n}const xu={xs:0,sm:600,md:900,lg:1200,xl:1536},jy={keys:["xs","sm","md","lg","xl"],up:n=>`@media (min-width:${xu[n]}px)`},Ux={containerQueries:n=>({up:r=>{let o=typeof r=="number"?r:xu[r]||r;return typeof o=="number"&&(o=`${o}px`),n?`@container ${n} (min-width:${o})`:`@container (min-width:${o})`}})};function Da(n,r,o){const l=n.theme||{};if(Array.isArray(r)){const c=l.breakpoints||jy;return r.reduce((d,p,h)=>(d[c.up(c.keys[h])]=o(r[h]),d),{})}if(typeof r=="object"){const c=l.breakpoints||jy;return Object.keys(r).reduce((d,p)=>{if(jx(c.keys,p)){const h=kx(l.containerQueries?l:Ux,p);h&&(d[h]=o(r[p],p))}else if(Object.keys(c.values||xu).includes(p)){const h=c.up(p);d[h]=o(r[p],p)}else{const h=p;d[h]=r[h]}return d},{})}return o(r)}function Lx(n={}){var o;return((o=n.keys)==null?void 0:o.reduce((l,u)=>{const c=n.up(u);return l[c]={},l},{}))||{}}function Hx(n,r){return n.reduce((o,l)=>{const u=o[l];return(!u||Object.keys(u).length===0)&&delete o[l],o},r)}function st(n){if(typeof n!="string")throw new Error(za(7));return n.charAt(0).toUpperCase()+n.slice(1)}function Cu(n,r,o=!0){if(!r||typeof r!="string")return null;if(n&&n.vars&&o){const l=`vars.${r}`.split(".").reduce((u,c)=>u&&u[c]?u[c]:null,n);if(l!=null)return l}return r.split(".").reduce((l,u)=>l&&l[u]!=null?l[u]:null,n)}function uu(n,r,o,l=o){let u;return typeof n=="function"?u=n(o):Array.isArray(n)?u=n[o]||l:u=Cu(n,o)||l,r&&(u=r(u,l,n)),u}function je(n){const{prop:r,cssProperty:o=n.prop,themeKey:l,transform:u}=n,c=d=>{if(d[r]==null)return null;const p=d[r],h=d.theme,m=Cu(h,l)||{};return Da(d,p,S=>{let E=uu(m,u,S);return S===E&&typeof S=="string"&&(E=uu(m,u,`${r}${S==="default"?"":st(S)}`,S)),o===!1?E:{[o]:E}})};return c.propTypes={},c.filterProps=[r],c}function qx(n){const r={};return o=>(r[o]===void 0&&(r[o]=n(o)),r[o])}const Px={m:"margin",p:"padding"},Gx={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},ky={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},Vx=qx(n=>{if(n.length>2)if(ky[n])n=ky[n];else return[n];const[r,o]=n.split(""),l=Px[r],u=Gx[o]||"";return Array.isArray(u)?u.map(c=>l+c):[l+u]}),Xd=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],Id=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"];[...Xd,...Id];function xl(n,r,o,l){const u=Cu(n,r,!0)??o;return typeof u=="number"||typeof u=="string"?c=>typeof c=="string"?c:typeof u=="string"?u.startsWith("var(")&&c===0?0:u.startsWith("var(")&&c===1?u:`calc(${c} * ${u})`:u*c:Array.isArray(u)?c=>{if(typeof c=="string")return c;const d=Math.abs(c),p=u[d];return c>=0?p:typeof p=="number"?-p:typeof p=="string"&&p.startsWith("var(")?`calc(-1 * ${p})`:`-${p}`}:typeof u=="function"?u:()=>{}}function Wd(n){return xl(n,"spacing",8)}function Cl(n,r){return typeof r=="string"||r==null?r:n(r)}function Yx(n,r){return o=>n.reduce((l,u)=>(l[u]=Cl(r,o),l),{})}function Xx(n,r,o,l){if(!r.includes(o))return null;const u=Vx(o),c=Yx(u,l),d=n[o];return Da(n,d,c)}function a0(n,r){const o=Wd(n.theme);return Object.keys(n).map(l=>Xx(n,r,l,o)).reduce(rl,{})}function Me(n){return a0(n,Xd)}Me.propTypes={};Me.filterProps=Xd;function ze(n){return a0(n,Id)}ze.propTypes={};ze.filterProps=Id;function r0(n=8,r=Wd({spacing:n})){if(n.mui)return n;const o=(...l)=>(l.length===0?[1]:l).map(c=>{const d=r(c);return typeof d=="number"?`${d}px`:d}).join(" ");return o.mui=!0,o}function Tu(...n){const r=n.reduce((l,u)=>(u.filterProps.forEach(c=>{l[c]=u}),l),{}),o=l=>Object.keys(l).reduce((u,c)=>r[c]?rl(u,r[c](l)):u,{});return o.propTypes={},o.filterProps=n.reduce((l,u)=>l.concat(u.filterProps),[]),o}function Hn(n){return typeof n!="number"?n:`${n}px solid`}function Vn(n,r){return je({prop:n,themeKey:"borders",transform:r})}const Ix=Vn("border",Hn),Wx=Vn("borderTop",Hn),Kx=Vn("borderRight",Hn),Qx=Vn("borderBottom",Hn),Zx=Vn("borderLeft",Hn),Fx=Vn("borderColor"),Jx=Vn("borderTopColor"),t2=Vn("borderRightColor"),e2=Vn("borderBottomColor"),n2=Vn("borderLeftColor"),a2=Vn("outline",Hn),r2=Vn("outlineColor"),Eu=n=>{if(n.borderRadius!==void 0&&n.borderRadius!==null){const r=xl(n.theme,"shape.borderRadius",4),o=l=>({borderRadius:Cl(r,l)});return Da(n,n.borderRadius,o)}return null};Eu.propTypes={};Eu.filterProps=["borderRadius"];Tu(Ix,Wx,Kx,Qx,Zx,Fx,Jx,t2,e2,n2,Eu,a2,r2);const Ru=n=>{if(n.gap!==void 0&&n.gap!==null){const r=xl(n.theme,"spacing",8),o=l=>({gap:Cl(r,l)});return Da(n,n.gap,o)}return null};Ru.propTypes={};Ru.filterProps=["gap"];const wu=n=>{if(n.columnGap!==void 0&&n.columnGap!==null){const r=xl(n.theme,"spacing",8),o=l=>({columnGap:Cl(r,l)});return Da(n,n.columnGap,o)}return null};wu.propTypes={};wu.filterProps=["columnGap"];const Au=n=>{if(n.rowGap!==void 0&&n.rowGap!==null){const r=xl(n.theme,"spacing",8),o=l=>({rowGap:Cl(r,l)});return Da(n,n.rowGap,o)}return null};Au.propTypes={};Au.filterProps=["rowGap"];const o2=je({prop:"gridColumn"}),i2=je({prop:"gridRow"}),l2=je({prop:"gridAutoFlow"}),s2=je({prop:"gridAutoColumns"}),u2=je({prop:"gridAutoRows"}),c2=je({prop:"gridTemplateColumns"}),f2=je({prop:"gridTemplateRows"}),d2=je({prop:"gridTemplateAreas"}),p2=je({prop:"gridArea"});Tu(Ru,wu,Au,o2,i2,l2,s2,u2,c2,f2,d2,p2);function Bo(n,r){return r==="grey"?r:n}const h2=je({prop:"color",themeKey:"palette",transform:Bo}),m2=je({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:Bo}),g2=je({prop:"backgroundColor",themeKey:"palette",transform:Bo});Tu(h2,m2,g2);function En(n){return n<=1&&n!==0?`${n*100}%`:n}const y2=je({prop:"width",transform:En}),Kd=n=>{if(n.maxWidth!==void 0&&n.maxWidth!==null){const r=o=>{var u,c,d,p,h;const l=((d=(c=(u=n.theme)==null?void 0:u.breakpoints)==null?void 0:c.values)==null?void 0:d[o])||xu[o];return l?((h=(p=n.theme)==null?void 0:p.breakpoints)==null?void 0:h.unit)!=="px"?{maxWidth:`${l}${n.theme.breakpoints.unit}`}:{maxWidth:l}:{maxWidth:En(o)}};return Da(n,n.maxWidth,r)}return null};Kd.filterProps=["maxWidth"];const v2=je({prop:"minWidth",transform:En}),b2=je({prop:"height",transform:En}),S2=je({prop:"maxHeight",transform:En}),x2=je({prop:"minHeight",transform:En});je({prop:"size",cssProperty:"width",transform:En});je({prop:"size",cssProperty:"height",transform:En});const C2=je({prop:"boxSizing"});Tu(y2,Kd,v2,b2,S2,x2,C2);const Tl={border:{themeKey:"borders",transform:Hn},borderTop:{themeKey:"borders",transform:Hn},borderRight:{themeKey:"borders",transform:Hn},borderBottom:{themeKey:"borders",transform:Hn},borderLeft:{themeKey:"borders",transform:Hn},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:Hn},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:Eu},color:{themeKey:"palette",transform:Bo},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:Bo},backgroundColor:{themeKey:"palette",transform:Bo},p:{style:ze},pt:{style:ze},pr:{style:ze},pb:{style:ze},pl:{style:ze},px:{style:ze},py:{style:ze},padding:{style:ze},paddingTop:{style:ze},paddingRight:{style:ze},paddingBottom:{style:ze},paddingLeft:{style:ze},paddingX:{style:ze},paddingY:{style:ze},paddingInline:{style:ze},paddingInlineStart:{style:ze},paddingInlineEnd:{style:ze},paddingBlock:{style:ze},paddingBlockStart:{style:ze},paddingBlockEnd:{style:ze},m:{style:Me},mt:{style:Me},mr:{style:Me},mb:{style:Me},ml:{style:Me},mx:{style:Me},my:{style:Me},margin:{style:Me},marginTop:{style:Me},marginRight:{style:Me},marginBottom:{style:Me},marginLeft:{style:Me},marginX:{style:Me},marginY:{style:Me},marginInline:{style:Me},marginInlineStart:{style:Me},marginInlineEnd:{style:Me},marginBlock:{style:Me},marginBlockStart:{style:Me},marginBlockEnd:{style:Me},displayPrint:{cssProperty:!1,transform:n=>({"@media print":{display:n}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:Ru},rowGap:{style:Au},columnGap:{style:wu},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:En},maxWidth:{style:Kd},minWidth:{transform:En},height:{transform:En},maxHeight:{transform:En},minHeight:{transform:En},boxSizing:{},font:{themeKey:"font"},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}};function T2(...n){const r=n.reduce((l,u)=>l.concat(Object.keys(u)),[]),o=new Set(r);return n.every(l=>o.size===Object.keys(l).length)}function E2(n,r){return typeof n=="function"?n(r):n}function R2(){function n(o,l,u,c){const d={[o]:l,theme:u},p=c[o];if(!p)return{[o]:l};const{cssProperty:h=o,themeKey:m,transform:y,style:S}=p;if(l==null)return null;if(m==="typography"&&l==="inherit")return{[o]:l};const E=Cu(u,m)||{};return S?S(d):Da(d,l,x=>{let C=uu(E,y,x);return x===C&&typeof x=="string"&&(C=uu(E,y,`${o}${x==="default"?"":st(x)}`,x)),h===!1?C:{[h]:C}})}function r(o){const{sx:l,theme:u={}}=o||{};if(!l)return null;const c=u.unstable_sxConfig??Tl;function d(p){let h=p;if(typeof p=="function")h=p(u);else if(typeof p!="object")return p;if(!h)return null;const m=Lx(u.breakpoints),y=Object.keys(m);let S=m;return Object.keys(h).forEach(E=>{const R=E2(h[E],u);if(R!=null)if(typeof R=="object")if(c[E])S=rl(S,n(E,R,u,c));else{const x=Da({theme:u},R,C=>({[E]:C}));T2(x,R)?S[E]=r({sx:R,theme:u}):S=rl(S,x)}else S=rl(S,n(E,R,u,c))}),Nx(u,Hx(y,S))}return Array.isArray(l)?l.map(d):d(l)}return r}const ir=R2();ir.filterProps=["sx"];function w2(n,r){var l;const o=this;if(o.vars){if(!((l=o.colorSchemes)!=null&&l[n])||typeof o.getColorSchemeSelector!="function")return{};let u=o.getColorSchemeSelector(n);return u==="&"?r:((u.includes("data-")||u.includes("."))&&(u=`*:where(${u.replace(/\s*&$/,"")}) &`),{[u]:r})}return o.palette.mode===n?r:{}}function El(n={},...r){const{breakpoints:o={},palette:l={},spacing:u,shape:c={},...d}=n,p=Bx(o),h=r0(u);let m=nn({breakpoints:p,direction:"ltr",components:{},palette:{mode:"light",...l},spacing:h,shape:{..._x,...c}},d);return m=$x(m),m.applyStyles=w2,m=r.reduce((y,S)=>nn(y,S),m),m.unstable_sxConfig={...Tl,...d==null?void 0:d.unstable_sxConfig},m.unstable_sx=function(S){return ir({sx:S,theme:this})},m}function A2(n){return Object.keys(n).length===0}function o0(n=null){const r=w.useContext(bl);return!r||A2(r)?n:r}const O2=El();function Rl(n=O2){return o0(n)}function M2({styles:n,themeId:r,defaultTheme:o={}}){const l=Rl(o),u=typeof n=="function"?n(r&&l[r]||l):n;return T.jsx(Jv,{styles:u})}const z2=n=>{var l;const r={systemProps:{},otherProps:{}},o=((l=n==null?void 0:n.theme)==null?void 0:l.unstable_sxConfig)??Tl;return Object.keys(n).forEach(u=>{o[u]?r.systemProps[u]=n[u]:r.otherProps[u]=n[u]}),r};function Qd(n){const{sx:r,...o}=n,{systemProps:l,otherProps:u}=z2(o);let c;return Array.isArray(r)?c=[l,...r]:typeof r=="function"?c=(...d)=>{const p=r(...d);return ra(p)?{...l,...p}:l}:c={...l,...r},{...u,sx:c}}const $y=n=>n,D2=()=>{let n=$y;return{configure(r){n=r},generate(r){return n(r)},reset(){n=$y}}},i0=D2();function l0(n){var r,o,l="";if(typeof n=="string"||typeof n=="number")l+=n;else if(typeof n=="object")if(Array.isArray(n)){var u=n.length;for(r=0;r<u;r++)n[r]&&(o=l0(n[r]))&&(l&&(l+=" "),l+=o)}else for(o in n)n[o]&&(l&&(l+=" "),l+=o);return l}function ht(){for(var n,r,o=0,l="",u=arguments.length;o<u;o++)(n=arguments[o])&&(r=l0(n))&&(l&&(l+=" "),l+=r);return l}function B2(n={}){const{themeId:r,defaultTheme:o,defaultClassName:l="MuiBox-root",generateClassName:u}=n,c=t0("div",{shouldForwardProp:p=>p!=="theme"&&p!=="sx"&&p!=="as"})(ir);return w.forwardRef(function(h,m){const y=Rl(o),{className:S,component:E="div",...R}=Qd(h);return T.jsx(c,{as:E,ref:m,className:ht(S,u?u(l):l),theme:r&&y[r]||y,...R})})}const N2={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function Dt(n,r,o="Mui"){const l=N2[r];return l?`${o}-${l}`:`${i0.generate(n)}-${r}`}function Mt(n,r,o="Mui"){const l={};return r.forEach(u=>{l[u]=Dt(n,u,o)}),l}function s0(n){const{variants:r,...o}=n,l={variants:r,style:Dy(o),isProcessed:!0};return l.style===o||r&&r.forEach(u=>{typeof u.style!="function"&&(u.style=Dy(u.style))}),l}const j2=El();function ad(n){return n!=="ownerState"&&n!=="theme"&&n!=="sx"&&n!=="as"}function k2(n){return n?(r,o)=>o[n]:null}function $2(n,r,o){n.theme=U2(n.theme)?o:n.theme[r]||n.theme}function au(n,r){const o=typeof r=="function"?r(n):r;if(Array.isArray(o))return o.flatMap(l=>au(n,l));if(Array.isArray(o==null?void 0:o.variants)){let l;if(o.isProcessed)l=o.style;else{const{variants:u,...c}=o;l=c}return u0(n,o.variants,[l])}return o!=null&&o.isProcessed?o.style:o}function u0(n,r,o=[]){var u;let l;t:for(let c=0;c<r.length;c+=1){const d=r[c];if(typeof d.props=="function"){if(l??(l={...n,...n.ownerState,ownerState:n.ownerState}),!d.props(l))continue}else for(const p in d.props)if(n[p]!==d.props[p]&&((u=n.ownerState)==null?void 0:u[p])!==d.props[p])continue t;typeof d.style=="function"?(l??(l={...n,...n.ownerState,ownerState:n.ownerState}),o.push(d.style(l))):o.push(d.style)}return o}function c0(n={}){const{themeId:r,defaultTheme:o=j2,rootShouldForwardProp:l=ad,slotShouldForwardProp:u=ad}=n;function c(p){$2(p,r,o)}return(p,h={})=>{Ox(p,A=>A.filter($=>$!==ir));const{name:m,slot:y,skipVariantsResolver:S,skipSx:E,overridesResolver:R=k2(H2(y)),...x}=h,C=S!==void 0?S:y&&y!=="Root"&&y!=="root"||!1,D=E||!1;let j=ad;y==="Root"||y==="root"?j=l:y?j=u:L2(p)&&(j=void 0);const U=t0(p,{shouldForwardProp:j,label:_2(),...x}),M=A=>{if(typeof A=="function"&&A.__emotion_real!==A)return function(H){return au(H,A)};if(ra(A)){const $=s0(A);return $.variants?function(P){return au(P,$)}:$.style}return A},O=(...A)=>{const $=[],H=A.map(M),P=[];if($.push(c),m&&R&&P.push(function(W){var B,V;const it=(V=(B=W.theme.components)==null?void 0:B[m])==null?void 0:V.styleOverrides;if(!it)return null;const F={};for(const ot in it)F[ot]=au(W,it[ot]);return R(W,F)}),m&&!C&&P.push(function(W){var F,B;const Z=W.theme,it=(B=(F=Z==null?void 0:Z.components)==null?void 0:F[m])==null?void 0:B.variants;return it?u0(W,it):null}),D||P.push(ir),Array.isArray(H[0])){const _=H.shift(),W=new Array($.length).fill(""),Z=new Array(P.length).fill("");let it;it=[...W,..._,...Z],it.raw=[...W,..._.raw,...Z],$.unshift(it)}const K=[...$,...H,...P],v=U(...K);return p.muiName&&(v.muiName=p.muiName),v};return U.withConfig&&(O.withConfig=U.withConfig),O}}function _2(n,r){return void 0}function U2(n){for(const r in n)return!1;return!0}function L2(n){return typeof n=="string"&&n.charCodeAt(0)>96}function H2(n){return n&&n.charAt(0).toLowerCase()+n.slice(1)}const f0=c0();function pl(n,r){const o={...r};for(const l in n)if(Object.prototype.hasOwnProperty.call(n,l)){const u=l;if(u==="components"||u==="slots")o[u]={...n[u],...o[u]};else if(u==="componentsProps"||u==="slotProps"){const c=n[u],d=r[u];if(!d)o[u]=c||{};else if(!c)o[u]=d;else{o[u]={...d};for(const p in c)if(Object.prototype.hasOwnProperty.call(c,p)){const h=p;o[u][h]=pl(c[h],d[h])}}}else o[u]===void 0&&(o[u]=n[u])}return o}function q2(n){const{theme:r,name:o,props:l}=n;return!r||!r.components||!r.components[o]||!r.components[o].defaultProps?l:pl(r.components[o].defaultProps,l)}function d0({props:n,name:r,defaultTheme:o,themeId:l}){let u=Rl(o);return l&&(u=u[l]||u),q2({theme:u,name:r,props:n})}const Wn=typeof window<"u"?w.useLayoutEffect:w.useEffect;function P2(n,r=Number.MIN_SAFE_INTEGER,o=Number.MAX_SAFE_INTEGER){return Math.max(r,Math.min(n,o))}function Zd(n,r=0,o=1){return P2(n,r,o)}function G2(n){n=n.slice(1);const r=new RegExp(`.{1,${n.length>=6?2:1}}`,"g");let o=n.match(r);return o&&o[0].length===1&&(o=o.map(l=>l+l)),o?`rgb${o.length===4?"a":""}(${o.map((l,u)=>u<3?parseInt(l,16):Math.round(parseInt(l,16)/255*1e3)/1e3).join(", ")})`:""}function lr(n){if(n.type)return n;if(n.charAt(0)==="#")return lr(G2(n));const r=n.indexOf("("),o=n.substring(0,r);if(!["rgb","rgba","hsl","hsla","color"].includes(o))throw new Error(za(9,n));let l=n.substring(r+1,n.length-1),u;if(o==="color"){if(l=l.split(" "),u=l.shift(),l.length===4&&l[3].charAt(0)==="/"&&(l[3]=l[3].slice(1)),!["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].includes(u))throw new Error(za(10,u))}else l=l.split(",");return l=l.map(c=>parseFloat(c)),{type:o,values:l,colorSpace:u}}const V2=n=>{const r=lr(n);return r.values.slice(0,3).map((o,l)=>r.type.includes("hsl")&&l!==0?`${o}%`:o).join(" ")},el=(n,r)=>{try{return V2(n)}catch{return n}};function Ou(n){const{type:r,colorSpace:o}=n;let{values:l}=n;return r.includes("rgb")?l=l.map((u,c)=>c<3?parseInt(u,10):u):r.includes("hsl")&&(l[1]=`${l[1]}%`,l[2]=`${l[2]}%`),r.includes("color")?l=`${o} ${l.join(" ")}`:l=`${l.join(", ")}`,`${r}(${l})`}function p0(n){n=lr(n);const{values:r}=n,o=r[0],l=r[1]/100,u=r[2]/100,c=l*Math.min(u,1-u),d=(m,y=(m+o/30)%12)=>u-c*Math.max(Math.min(y-3,9-y,1),-1);let p="rgb";const h=[Math.round(d(0)*255),Math.round(d(8)*255),Math.round(d(4)*255)];return n.type==="hsla"&&(p+="a",h.push(r[3])),Ou({type:p,values:h})}function yd(n){n=lr(n);let r=n.type==="hsl"||n.type==="hsla"?lr(p0(n)).values:n.values;return r=r.map(o=>(n.type!=="color"&&(o/=255),o<=.03928?o/12.92:((o+.055)/1.055)**2.4)),Number((.2126*r[0]+.7152*r[1]+.0722*r[2]).toFixed(3))}function Y2(n,r){const o=yd(n),l=yd(r);return(Math.max(o,l)+.05)/(Math.min(o,l)+.05)}function ye(n,r){return n=lr(n),r=Zd(r),(n.type==="rgb"||n.type==="hsl")&&(n.type+="a"),n.type==="color"?n.values[3]=`/${r}`:n.values[3]=r,Ou(n)}function Vs(n,r,o){try{return ye(n,r)}catch{return n}}function Fd(n,r){if(n=lr(n),r=Zd(r),n.type.includes("hsl"))n.values[2]*=1-r;else if(n.type.includes("rgb")||n.type.includes("color"))for(let o=0;o<3;o+=1)n.values[o]*=1-r;return Ou(n)}function me(n,r,o){try{return Fd(n,r)}catch{return n}}function Jd(n,r){if(n=lr(n),r=Zd(r),n.type.includes("hsl"))n.values[2]+=(100-n.values[2])*r;else if(n.type.includes("rgb"))for(let o=0;o<3;o+=1)n.values[o]+=(255-n.values[o])*r;else if(n.type.includes("color"))for(let o=0;o<3;o+=1)n.values[o]+=(1-n.values[o])*r;return Ou(n)}function ge(n,r,o){try{return Jd(n,r)}catch{return n}}function X2(n,r=.15){return yd(n)>.5?Fd(n,r):Jd(n,r)}function Ys(n,r,o){try{return X2(n,r)}catch{return n}}const h0=w.createContext(null);function tp(){return w.useContext(h0)}const I2=typeof Symbol=="function"&&Symbol.for,W2=I2?Symbol.for("mui.nested"):"__THEME_NESTED__";function K2(n,r){return typeof r=="function"?r(n):{...n,...r}}function Q2(n){const{children:r,theme:o}=n,l=tp(),u=w.useMemo(()=>{const c=l===null?{...o}:K2(l,o);return c!=null&&(c[W2]=l!==null),c},[o,l]);return T.jsx(h0.Provider,{value:u,children:r})}const m0=w.createContext();function Z2({value:n,...r}){return T.jsx(m0.Provider,{value:n??!0,...r})}const ep=()=>w.useContext(m0)??!1,g0=w.createContext(void 0);function F2({value:n,children:r}){return T.jsx(g0.Provider,{value:n,children:r})}function J2(n){const{theme:r,name:o,props:l}=n;if(!r||!r.components||!r.components[o])return l;const u=r.components[o];return u.defaultProps?pl(u.defaultProps,l):!u.styleOverrides&&!u.variants?pl(u,l):l}function tC({props:n,name:r}){const o=w.useContext(g0);return J2({props:n,name:r,theme:{components:o}})}const _y={};function Uy(n,r,o,l=!1){return w.useMemo(()=>{const u=n&&r[n]||r;if(typeof o=="function"){const c=o(u),d=n?{...r,[n]:c}:c;return l?()=>d:d}return n?{...r,[n]:o}:{...r,...o}},[n,r,o,l])}function y0(n){const{children:r,theme:o,themeId:l}=n,u=o0(_y),c=tp()||_y,d=Uy(l,u,o),p=Uy(l,c,o,!0),h=(l?d[l]:d).direction==="rtl";return T.jsx(Q2,{theme:p,children:T.jsx(bl.Provider,{value:d,children:T.jsx(Z2,{value:h,children:T.jsx(F2,{value:l?d[l].components:d.components,children:r})})})})}const Ly={theme:void 0};function eC(n){let r,o;return function(u){let c=r;return(c===void 0||u.theme!==o)&&(Ly.theme=u.theme,c=s0(n(Ly)),r=c,o=u.theme),c}}const np="mode",ap="color-scheme",nC="data-color-scheme";function aC(n){const{defaultMode:r="system",defaultLightColorScheme:o="light",defaultDarkColorScheme:l="dark",modeStorageKey:u=np,colorSchemeStorageKey:c=ap,attribute:d=nC,colorSchemeNode:p="document.documentElement",nonce:h}=n||{};let m="",y=d;if(d==="class"&&(y=".%s"),d==="data"&&(y="[data-%s]"),y.startsWith(".")){const E=y.substring(1);m+=`${p}.classList.remove('${E}'.replace('%s', light), '${E}'.replace('%s', dark));
      ${p}.classList.add('${E}'.replace('%s', colorScheme));`}const S=y.match(/\[([^\]]+)\]/);if(S){const[E,R]=S[1].split("=");R||(m+=`${p}.removeAttribute('${E}'.replace('%s', light));
      ${p}.removeAttribute('${E}'.replace('%s', dark));`),m+=`
      ${p}.setAttribute('${E}'.replace('%s', colorScheme), ${R?`${R}.replace('%s', colorScheme)`:'""'});`}else m+=`${p}.setAttribute('${y}', colorScheme);`;return T.jsx("script",{suppressHydrationWarning:!0,nonce:typeof window>"u"?h:"",dangerouslySetInnerHTML:{__html:`(function() {
try {
  let colorScheme = '';
  const mode = localStorage.getItem('${u}') || '${r}';
  const dark = localStorage.getItem('${c}-dark') || '${l}';
  const light = localStorage.getItem('${c}-light') || '${o}';
  if (mode === 'system') {
    // handle system mode
    const mql = window.matchMedia('(prefers-color-scheme: dark)');
    if (mql.matches) {
      colorScheme = dark
    } else {
      colorScheme = light
    }
  }
  if (mode === 'light') {
    colorScheme = light;
  }
  if (mode === 'dark') {
    colorScheme = dark;
  }
  if (colorScheme) {
    ${m}
  }
} catch(e){}})();`}},"mui-color-scheme-init")}function rC(){}const oC=({key:n,storageWindow:r})=>(!r&&typeof window<"u"&&(r=window),{get(o){if(typeof window>"u")return;if(!r)return o;let l;try{l=r.localStorage.getItem(n)}catch{}return l||o},set:o=>{if(r)try{r.localStorage.setItem(n,o)}catch{}},subscribe:o=>{if(!r)return rC;const l=u=>{const c=u.newValue;u.key===n&&o(c)};return r.addEventListener("storage",l),()=>{r.removeEventListener("storage",l)}}});function rd(){}function Hy(n){if(typeof window<"u"&&typeof window.matchMedia=="function"&&n==="system")return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}function v0(n,r){if(n.mode==="light"||n.mode==="system"&&n.systemMode==="light")return r("light");if(n.mode==="dark"||n.mode==="system"&&n.systemMode==="dark")return r("dark")}function iC(n){return v0(n,r=>{if(r==="light")return n.lightColorScheme;if(r==="dark")return n.darkColorScheme})}function lC(n){const{defaultMode:r="light",defaultLightColorScheme:o,defaultDarkColorScheme:l,supportedColorSchemes:u=[],modeStorageKey:c=np,colorSchemeStorageKey:d=ap,storageWindow:p=typeof window>"u"?void 0:window,storageManager:h=oC,noSsr:m=!1}=n,y=u.join(","),S=u.length>1,E=w.useMemo(()=>h==null?void 0:h({key:c,storageWindow:p}),[h,c,p]),R=w.useMemo(()=>h==null?void 0:h({key:`${d}-light`,storageWindow:p}),[h,d,p]),x=w.useMemo(()=>h==null?void 0:h({key:`${d}-dark`,storageWindow:p}),[h,d,p]),[C,D]=w.useState(()=>{const P=(E==null?void 0:E.get(r))||r,K=(R==null?void 0:R.get(o))||o,v=(x==null?void 0:x.get(l))||l;return{mode:P,systemMode:Hy(P),lightColorScheme:K,darkColorScheme:v}}),[j,U]=w.useState(m||!S);w.useEffect(()=>{U(!0)},[]);const M=iC(C),O=w.useCallback(P=>{D(K=>{if(P===K.mode)return K;const v=P??r;return E==null||E.set(v),{...K,mode:v,systemMode:Hy(v)}})},[E,r]),A=w.useCallback(P=>{P?typeof P=="string"?P&&!y.includes(P)?console.error(`\`${P}\` does not exist in \`theme.colorSchemes\`.`):D(K=>{const v={...K};return v0(K,_=>{_==="light"&&(R==null||R.set(P),v.lightColorScheme=P),_==="dark"&&(x==null||x.set(P),v.darkColorScheme=P)}),v}):D(K=>{const v={...K},_=P.light===null?o:P.light,W=P.dark===null?l:P.dark;return _&&(y.includes(_)?(v.lightColorScheme=_,R==null||R.set(_)):console.error(`\`${_}\` does not exist in \`theme.colorSchemes\`.`)),W&&(y.includes(W)?(v.darkColorScheme=W,x==null||x.set(W)):console.error(`\`${W}\` does not exist in \`theme.colorSchemes\`.`)),v}):D(K=>(R==null||R.set(o),x==null||x.set(l),{...K,lightColorScheme:o,darkColorScheme:l}))},[y,R,x,o,l]),$=w.useCallback(P=>{C.mode==="system"&&D(K=>{const v=P!=null&&P.matches?"dark":"light";return K.systemMode===v?K:{...K,systemMode:v}})},[C.mode]),H=w.useRef($);return H.current=$,w.useEffect(()=>{if(typeof window.matchMedia!="function"||!S)return;const P=(...v)=>H.current(...v),K=window.matchMedia("(prefers-color-scheme: dark)");return K.addListener(P),P(K),()=>{K.removeListener(P)}},[S]),w.useEffect(()=>{if(S){const P=(E==null?void 0:E.subscribe(_=>{(!_||["light","dark","system"].includes(_))&&O(_||r)}))||rd,K=(R==null?void 0:R.subscribe(_=>{(!_||y.match(_))&&A({light:_})}))||rd,v=(x==null?void 0:x.subscribe(_=>{(!_||y.match(_))&&A({dark:_})}))||rd;return()=>{P(),K(),v()}}},[A,O,y,r,p,S,E,R,x]),{...C,mode:j?C.mode:void 0,systemMode:j?C.systemMode:void 0,colorScheme:j?M:void 0,setMode:O,setColorScheme:A}}const sC="*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function uC(n){const{themeId:r,theme:o={},modeStorageKey:l=np,colorSchemeStorageKey:u=ap,disableTransitionOnChange:c=!1,defaultColorScheme:d,resolveTheme:p}=n,h={allColorSchemes:[],colorScheme:void 0,darkColorScheme:void 0,lightColorScheme:void 0,mode:void 0,setColorScheme:()=>{},setMode:()=>{},systemMode:void 0},m=w.createContext(void 0),y=()=>w.useContext(m)||h,S={},E={};function R(j){var Le,ne,Ee,ae;const{children:U,theme:M,modeStorageKey:O=l,colorSchemeStorageKey:A=u,disableTransitionOnChange:$=c,storageManager:H,storageWindow:P=typeof window>"u"?void 0:window,documentNode:K=typeof document>"u"?void 0:document,colorSchemeNode:v=typeof document>"u"?void 0:document.documentElement,disableNestedContext:_=!1,disableStyleSheetGeneration:W=!1,defaultMode:Z="system",forceThemeRerender:it=!1,noSsr:F}=j,B=w.useRef(!1),V=tp(),ot=w.useContext(m),Q=!!ot&&!_,z=w.useMemo(()=>M||(typeof o=="function"?o():o),[M]),X=z[r],et=X||z,{colorSchemes:nt=S,components:lt=E,cssVarPrefix:ct}=et,ft=Object.keys(nt).filter(se=>!!nt[se]).join(","),Tt=w.useMemo(()=>ft.split(","),[ft]),bt=typeof d=="string"?d:d.light,At=typeof d=="string"?d:d.dark,yt=nt[bt]&&nt[At]?Z:((ne=(Le=nt[et.defaultColorScheme])==null?void 0:Le.palette)==null?void 0:ne.mode)||((Ee=et.palette)==null?void 0:Ee.mode),{mode:wt,setMode:St,systemMode:$t,lightColorScheme:Et,darkColorScheme:Vt,colorScheme:Te,setColorScheme:Lt}=lC({supportedColorSchemes:Tt,defaultLightColorScheme:bt,defaultDarkColorScheme:At,modeStorageKey:O,colorSchemeStorageKey:A,defaultMode:yt,storageManager:H,storageWindow:P,noSsr:F});let Kt=wt,Qt=Te;Q&&(Kt=ot.mode,Qt=ot.colorScheme);let Yt=Qt||et.defaultColorScheme;et.vars&&!it&&(Yt=et.defaultColorScheme);const _t=w.useMemo(()=>{var we;const se=((we=et.generateThemeVars)==null?void 0:we.call(et))||et.vars,mt={...et,components:lt,colorSchemes:nt,cssVarPrefix:ct,vars:se};if(typeof mt.generateSpacing=="function"&&(mt.spacing=mt.generateSpacing()),Yt){const re=nt[Yt];re&&typeof re=="object"&&Object.keys(re).forEach(Zt=>{re[Zt]&&typeof re[Zt]=="object"?mt[Zt]={...mt[Zt],...re[Zt]}:mt[Zt]=re[Zt]})}return p?p(mt):mt},[et,Yt,lt,nt,ct]),dt=et.colorSchemeSelector;Wn(()=>{if(Qt&&v&&dt&&dt!=="media"){const se=dt;let mt=dt;if(se==="class"&&(mt=".%s"),se==="data"&&(mt="[data-%s]"),se!=null&&se.startsWith("data-")&&!se.includes("%s")&&(mt=`[${se}="%s"]`),mt.startsWith("."))v.classList.remove(...Tt.map(we=>mt.substring(1).replace("%s",we))),v.classList.add(mt.substring(1).replace("%s",Qt));else{const we=mt.replace("%s",Qt).match(/\[([^\]]+)\]/);if(we){const[re,Zt]=we[1].split("=");Zt||Tt.forEach(gt=>{v.removeAttribute(re.replace(Qt,gt))}),v.setAttribute(re,Zt?Zt.replace(/"|'/g,""):"")}else v.setAttribute(mt,Qt)}}},[Qt,dt,v,Tt]),w.useEffect(()=>{let se;if($&&B.current&&K){const mt=K.createElement("style");mt.appendChild(K.createTextNode(sC)),K.head.appendChild(mt),window.getComputedStyle(K.body),se=setTimeout(()=>{K.head.removeChild(mt)},1)}return()=>{clearTimeout(se)}},[Qt,$,K]),w.useEffect(()=>(B.current=!0,()=>{B.current=!1}),[]);const Ue=w.useMemo(()=>({allColorSchemes:Tt,colorScheme:Qt,darkColorScheme:Vt,lightColorScheme:Et,mode:Kt,setColorScheme:Lt,setMode:St,systemMode:$t}),[Tt,Qt,Vt,Et,Kt,Lt,St,$t,_t.colorSchemeSelector]);let le=!0;(W||et.cssVariables===!1||Q&&(V==null?void 0:V.cssVarPrefix)===ct)&&(le=!1);const Ze=T.jsxs(w.Fragment,{children:[T.jsx(y0,{themeId:X?r:void 0,theme:_t,children:U}),le&&T.jsx(Jv,{styles:((ae=_t.generateStyleSheets)==null?void 0:ae.call(_t))||[]})]});return Q?Ze:T.jsx(m.Provider,{value:Ue,children:Ze})}const x=typeof d=="string"?d:d.light,C=typeof d=="string"?d:d.dark;return{CssVarsProvider:R,useColorScheme:y,getInitColorSchemeScript:j=>aC({colorSchemeStorageKey:u,defaultLightColorScheme:x,defaultDarkColorScheme:C,modeStorageKey:l,...j})}}function cC(n=""){function r(...l){if(!l.length)return"";const u=l[0];return typeof u=="string"&&!u.match(/(#|\(|\)|(-?(\d*\.)?\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\d*\.)?\d+)$|(\d+ \d+ \d+)/)?`, var(--${n?`${n}-`:""}${u}${r(...l.slice(1))})`:`, ${u}`}return(l,...u)=>`var(--${n?`${n}-`:""}${l}${r(...u)})`}const qy=(n,r,o,l=[])=>{let u=n;r.forEach((c,d)=>{d===r.length-1?Array.isArray(u)?u[Number(c)]=o:u&&typeof u=="object"&&(u[c]=o):u&&typeof u=="object"&&(u[c]||(u[c]=l.includes(c)?[]:{}),u=u[c])})},fC=(n,r,o)=>{function l(u,c=[],d=[]){Object.entries(u).forEach(([p,h])=>{(!o||o&&!o([...c,p]))&&h!=null&&(typeof h=="object"&&Object.keys(h).length>0?l(h,[...c,p],Array.isArray(h)?[...d,p]:d):r([...c,p],h,d))})}l(n)},dC=(n,r)=>typeof r=="number"?["lineHeight","fontWeight","opacity","zIndex"].some(l=>n.includes(l))||n[n.length-1].toLowerCase().includes("opacity")?r:`${r}px`:r;function od(n,r){const{prefix:o,shouldSkipGeneratingVar:l}=r||{},u={},c={},d={};return fC(n,(p,h,m)=>{if((typeof h=="string"||typeof h=="number")&&(!l||!l(p,h))){const y=`--${o?`${o}-`:""}${p.join("-")}`,S=dC(p,h);Object.assign(u,{[y]:S}),qy(c,p,`var(${y})`,m),qy(d,p,`var(${y}, ${S})`,m)}},p=>p[0]==="vars"),{css:u,vars:c,varsWithDefaults:d}}function pC(n,r={}){const{getSelector:o=D,disableCssColorScheme:l,colorSchemeSelector:u}=r,{colorSchemes:c={},components:d,defaultColorScheme:p="light",...h}=n,{vars:m,css:y,varsWithDefaults:S}=od(h,r);let E=S;const R={},{[p]:x,...C}=c;if(Object.entries(C||{}).forEach(([M,O])=>{const{vars:A,css:$,varsWithDefaults:H}=od(O,r);E=nn(E,H),R[M]={css:$,vars:A}}),x){const{css:M,vars:O,varsWithDefaults:A}=od(x,r);E=nn(E,A),R[p]={css:M,vars:O}}function D(M,O){var $,H;let A=u;if(u==="class"&&(A=".%s"),u==="data"&&(A="[data-%s]"),u!=null&&u.startsWith("data-")&&!u.includes("%s")&&(A=`[${u}="%s"]`),M){if(A==="media")return n.defaultColorScheme===M?":root":{[`@media (prefers-color-scheme: ${((H=($=c[M])==null?void 0:$.palette)==null?void 0:H.mode)||M})`]:{":root":O}};if(A)return n.defaultColorScheme===M?`:root, ${A.replace("%s",String(M))}`:A.replace("%s",String(M))}return":root"}return{vars:E,generateThemeVars:()=>{let M={...m};return Object.entries(R).forEach(([,{vars:O}])=>{M=nn(M,O)}),M},generateStyleSheets:()=>{var P,K;const M=[],O=n.defaultColorScheme||"light";function A(v,_){Object.keys(_).length&&M.push(typeof v=="string"?{[v]:{..._}}:v)}A(o(void 0,{...y}),y);const{[O]:$,...H}=R;if($){const{css:v}=$,_=(K=(P=c[O])==null?void 0:P.palette)==null?void 0:K.mode,W=!l&&_?{colorScheme:_,...v}:{...v};A(o(O,{...W}),W)}return Object.entries(H).forEach(([v,{css:_}])=>{var it,F;const W=(F=(it=c[v])==null?void 0:it.palette)==null?void 0:F.mode,Z=!l&&W?{colorScheme:W,..._}:{..._};A(o(v,{...Z}),Z)}),M}}}function hC(n){return function(o){return n==="media"?`@media (prefers-color-scheme: ${o})`:n?n.startsWith("data-")&&!n.includes("%s")?`[${n}="${o}"] &`:n==="class"?`.${o} &`:n==="data"?`[data-${o}] &`:`${n.replace("%s",o)} &`:"&"}}function Bt(n,r,o=void 0){const l={};for(const u in n){const c=n[u];let d="",p=!0;for(let h=0;h<c.length;h+=1){const m=c[h];m&&(d+=(p===!0?"":" ")+r(m),p=!1,o&&o[m]&&(d+=" "+o[m]))}l[u]=d}return l}const mC=El(),gC=f0("div",{name:"MuiContainer",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[`maxWidth${st(String(o.maxWidth))}`],o.fixed&&r.fixed,o.disableGutters&&r.disableGutters]}}),yC=n=>d0({props:n,name:"MuiContainer",defaultTheme:mC}),vC=(n,r)=>{const o=h=>Dt(r,h),{classes:l,fixed:u,disableGutters:c,maxWidth:d}=n,p={root:["root",d&&`maxWidth${st(String(d))}`,u&&"fixed",c&&"disableGutters"]};return Bt(p,o,l)};function bC(n={}){const{createStyledComponent:r=gC,useThemeProps:o=yC,componentName:l="MuiContainer"}=n,u=r(({theme:d,ownerState:p})=>({width:"100%",marginLeft:"auto",boxSizing:"border-box",marginRight:"auto",...!p.disableGutters&&{paddingLeft:d.spacing(2),paddingRight:d.spacing(2),[d.breakpoints.up("sm")]:{paddingLeft:d.spacing(3),paddingRight:d.spacing(3)}}}),({theme:d,ownerState:p})=>p.fixed&&Object.keys(d.breakpoints.values).reduce((h,m)=>{const y=m,S=d.breakpoints.values[y];return S!==0&&(h[d.breakpoints.up(y)]={maxWidth:`${S}${d.breakpoints.unit}`}),h},{}),({theme:d,ownerState:p})=>({...p.maxWidth==="xs"&&{[d.breakpoints.up("xs")]:{maxWidth:Math.max(d.breakpoints.values.xs,444)}},...p.maxWidth&&p.maxWidth!=="xs"&&{[d.breakpoints.up(p.maxWidth)]:{maxWidth:`${d.breakpoints.values[p.maxWidth]}${d.breakpoints.unit}`}}}));return w.forwardRef(function(p,h){const m=o(p),{className:y,component:S="div",disableGutters:E=!1,fixed:R=!1,maxWidth:x="lg",classes:C,...D}=m,j={...m,component:S,disableGutters:E,fixed:R,maxWidth:x},U=vC(j,l);return T.jsx(u,{as:S,ownerState:j,className:ht(U.root,y),ref:h,...D})})}function ru(n,r){var o,l,u;return w.isValidElement(n)&&r.indexOf(n.type.muiName??((u=(l=(o=n.type)==null?void 0:o._payload)==null?void 0:l.value)==null?void 0:u.muiName))!==-1}const SC=(n,r)=>n.filter(o=>r.includes(o)),Po=(n,r,o)=>{const l=n.keys[0];Array.isArray(r)?r.forEach((u,c)=>{o((d,p)=>{c<=n.keys.length-1&&(c===0?Object.assign(d,p):d[n.up(n.keys[c])]=p)},u)}):r&&typeof r=="object"?(Object.keys(r).length>n.keys.length?n.keys:SC(n.keys,Object.keys(r))).forEach(c=>{if(n.keys.includes(c)){const d=r[c];d!==void 0&&o((p,h)=>{l===c?Object.assign(p,h):p[n.up(c)]=h},d)}}):(typeof r=="number"||typeof r=="string")&&o((u,c)=>{Object.assign(u,c)},r)};function cu(n){return`--Grid-${n}Spacing`}function Mu(n){return`--Grid-parent-${n}Spacing`}const Py="--Grid-columns",No="--Grid-parent-columns",xC=({theme:n,ownerState:r})=>{const o={};return Po(n.breakpoints,r.size,(l,u)=>{let c={};u==="grow"&&(c={flexBasis:0,flexGrow:1,maxWidth:"100%"}),u==="auto"&&(c={flexBasis:"auto",flexGrow:0,flexShrink:0,maxWidth:"none",width:"auto"}),typeof u=="number"&&(c={flexGrow:0,flexBasis:"auto",width:`calc(100% * ${u} / var(${No}) - (var(${No}) - ${u}) * (var(${Mu("column")}) / var(${No})))`}),l(o,c)}),o},CC=({theme:n,ownerState:r})=>{const o={};return Po(n.breakpoints,r.offset,(l,u)=>{let c={};u==="auto"&&(c={marginLeft:"auto"}),typeof u=="number"&&(c={marginLeft:u===0?"0px":`calc(100% * ${u} / var(${No}) + var(${Mu("column")}) * ${u} / var(${No}))`}),l(o,c)}),o},TC=({theme:n,ownerState:r})=>{if(!r.container)return{};const o={[Py]:12};return Po(n.breakpoints,r.columns,(l,u)=>{const c=u??12;l(o,{[Py]:c,"> *":{[No]:c}})}),o},EC=({theme:n,ownerState:r})=>{if(!r.container)return{};const o={};return Po(n.breakpoints,r.rowSpacing,(l,u)=>{var d;const c=typeof u=="string"?u:(d=n.spacing)==null?void 0:d.call(n,u);l(o,{[cu("row")]:c,"> *":{[Mu("row")]:c}})}),o},RC=({theme:n,ownerState:r})=>{if(!r.container)return{};const o={};return Po(n.breakpoints,r.columnSpacing,(l,u)=>{var d;const c=typeof u=="string"?u:(d=n.spacing)==null?void 0:d.call(n,u);l(o,{[cu("column")]:c,"> *":{[Mu("column")]:c}})}),o},wC=({theme:n,ownerState:r})=>{if(!r.container)return{};const o={};return Po(n.breakpoints,r.direction,(l,u)=>{l(o,{flexDirection:u})}),o},AC=({ownerState:n})=>({minWidth:0,boxSizing:"border-box",...n.container&&{display:"flex",flexWrap:"wrap",...n.wrap&&n.wrap!=="wrap"&&{flexWrap:n.wrap},gap:`var(${cu("row")}) var(${cu("column")})`}}),OC=n=>{const r=[];return Object.entries(n).forEach(([o,l])=>{l!==!1&&l!==void 0&&r.push(`grid-${o}-${String(l)}`)}),r},MC=(n,r="xs")=>{function o(l){return l===void 0?!1:typeof l=="string"&&!Number.isNaN(Number(l))||typeof l=="number"&&l>0}if(o(n))return[`spacing-${r}-${String(n)}`];if(typeof n=="object"&&!Array.isArray(n)){const l=[];return Object.entries(n).forEach(([u,c])=>{o(c)&&l.push(`spacing-${u}-${String(c)}`)}),l}return[]},zC=n=>n===void 0?[]:typeof n=="object"?Object.entries(n).map(([r,o])=>`direction-${r}-${o}`):[`direction-xs-${String(n)}`];function DC(n,r){n.item!==void 0&&delete n.item,n.zeroMinWidth!==void 0&&delete n.zeroMinWidth,r.keys.forEach(o=>{n[o]!==void 0&&delete n[o]})}const BC=El(),NC=f0("div",{name:"MuiGrid",slot:"Root"});function jC(n){return d0({props:n,name:"MuiGrid",defaultTheme:BC})}function kC(n={}){const{createStyledComponent:r=NC,useThemeProps:o=jC,useTheme:l=Rl,componentName:u="MuiGrid"}=n,c=(m,y)=>{const{container:S,direction:E,spacing:R,wrap:x,size:C}=m,D={root:["root",S&&"container",x!=="wrap"&&`wrap-xs-${String(x)}`,...zC(E),...OC(C),...S?MC(R,y.breakpoints.keys[0]):[]]};return Bt(D,j=>Dt(u,j),{})};function d(m,y,S=()=>!0){const E={};return m===null||(Array.isArray(m)?m.forEach((R,x)=>{R!==null&&S(R)&&y.keys[x]&&(E[y.keys[x]]=R)}):typeof m=="object"?Object.keys(m).forEach(R=>{const x=m[R];x!=null&&S(x)&&(E[R]=x)}):E[y.keys[0]]=m),E}const p=r(TC,RC,EC,xC,wC,AC,CC),h=w.forwardRef(function(y,S){const E=l(),R=o(y),x=Qd(R);DC(x,E.breakpoints);const{className:C,children:D,columns:j=12,container:U=!1,component:M="div",direction:O="row",wrap:A="wrap",size:$={},offset:H={},spacing:P=0,rowSpacing:K=P,columnSpacing:v=P,unstable_level:_=0,...W}=x,Z=d($,E.breakpoints,X=>X!==!1),it=d(H,E.breakpoints),F=y.columns??(_?void 0:j),B=y.spacing??(_?void 0:P),V=y.rowSpacing??y.spacing??(_?void 0:K),ot=y.columnSpacing??y.spacing??(_?void 0:v),Q={...x,level:_,columns:F,container:U,direction:O,wrap:A,spacing:B,rowSpacing:V,columnSpacing:ot,size:Z,offset:it},z=c(Q,E);return T.jsx(p,{ref:S,as:M,ownerState:Q,className:ht(z.root,C),...W,children:w.Children.map(D,X=>{var et;return w.isValidElement(X)&&ru(X,["Grid"])&&U&&X.props.container?w.cloneElement(X,{unstable_level:((et=X.props)==null?void 0:et.unstable_level)??_+1}):X})})});return h.muiName="Grid",h}const hl={black:"#000",white:"#fff"},$C={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"},Co={50:"#f3e5f5",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",700:"#7b1fa2"},To={300:"#e57373",400:"#ef5350",500:"#f44336",700:"#d32f2f",800:"#c62828"},Wi={300:"#ffb74d",400:"#ffa726",500:"#ff9800",700:"#f57c00",900:"#e65100"},Eo={50:"#e3f2fd",200:"#90caf9",400:"#42a5f5",700:"#1976d2",800:"#1565c0"},Ro={300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",700:"#0288d1",900:"#01579b"},wo={300:"#81c784",400:"#66bb6a",500:"#4caf50",700:"#388e3c",800:"#2e7d32",900:"#1b5e20"};function b0(){return{text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:hl.white,default:hl.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}}}const _C=b0();function S0(){return{text:{primary:hl.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:hl.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}}}const Gy=S0();function Vy(n,r,o,l){const u=l.light||l,c=l.dark||l*1.5;n[r]||(n.hasOwnProperty(o)?n[r]=n[o]:r==="light"?n.light=Jd(n.main,u):r==="dark"&&(n.dark=Fd(n.main,c)))}function UC(n="light"){return n==="dark"?{main:Eo[200],light:Eo[50],dark:Eo[400]}:{main:Eo[700],light:Eo[400],dark:Eo[800]}}function LC(n="light"){return n==="dark"?{main:Co[200],light:Co[50],dark:Co[400]}:{main:Co[500],light:Co[300],dark:Co[700]}}function HC(n="light"){return n==="dark"?{main:To[500],light:To[300],dark:To[700]}:{main:To[700],light:To[400],dark:To[800]}}function qC(n="light"){return n==="dark"?{main:Ro[400],light:Ro[300],dark:Ro[700]}:{main:Ro[700],light:Ro[500],dark:Ro[900]}}function PC(n="light"){return n==="dark"?{main:wo[400],light:wo[300],dark:wo[700]}:{main:wo[800],light:wo[500],dark:wo[900]}}function GC(n="light"){return n==="dark"?{main:Wi[400],light:Wi[300],dark:Wi[700]}:{main:"#ed6c02",light:Wi[500],dark:Wi[900]}}function rp(n){const{mode:r="light",contrastThreshold:o=3,tonalOffset:l=.2,...u}=n,c=n.primary||UC(r),d=n.secondary||LC(r),p=n.error||HC(r),h=n.info||qC(r),m=n.success||PC(r),y=n.warning||GC(r);function S(C){return Y2(C,Gy.text.primary)>=o?Gy.text.primary:_C.text.primary}const E=({color:C,name:D,mainShade:j=500,lightShade:U=300,darkShade:M=700})=>{if(C={...C},!C.main&&C[j]&&(C.main=C[j]),!C.hasOwnProperty("main"))throw new Error(za(11,D?` (${D})`:"",j));if(typeof C.main!="string")throw new Error(za(12,D?` (${D})`:"",JSON.stringify(C.main)));return Vy(C,"light",U,l),Vy(C,"dark",M,l),C.contrastText||(C.contrastText=S(C.main)),C};let R;return r==="light"?R=b0():r==="dark"&&(R=S0()),nn({common:{...hl},mode:r,primary:E({color:c,name:"primary"}),secondary:E({color:d,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:E({color:p,name:"error"}),warning:E({color:y,name:"warning"}),info:E({color:h,name:"info"}),success:E({color:m,name:"success"}),grey:$C,contrastThreshold:o,getContrastText:S,augmentColor:E,tonalOffset:l,...R},u)}function VC(n){const r={};return Object.entries(n).forEach(l=>{const[u,c]=l;typeof c=="object"&&(r[u]=`${c.fontStyle?`${c.fontStyle} `:""}${c.fontVariant?`${c.fontVariant} `:""}${c.fontWeight?`${c.fontWeight} `:""}${c.fontStretch?`${c.fontStretch} `:""}${c.fontSize||""}${c.lineHeight?`/${c.lineHeight} `:""}${c.fontFamily||""}`)}),r}function YC(n,r){return{toolbar:{minHeight:56,[n.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[n.up("sm")]:{minHeight:64}},...r}}function XC(n){return Math.round(n*1e5)/1e5}const Yy={textTransform:"uppercase"},Xy='"Roboto", "Helvetica", "Arial", sans-serif';function x0(n,r){const{fontFamily:o=Xy,fontSize:l=14,fontWeightLight:u=300,fontWeightRegular:c=400,fontWeightMedium:d=500,fontWeightBold:p=700,htmlFontSize:h=16,allVariants:m,pxToRem:y,...S}=typeof r=="function"?r(n):r,E=l/14,R=y||(D=>`${D/h*E}rem`),x=(D,j,U,M,O)=>({fontFamily:o,fontWeight:D,fontSize:R(j),lineHeight:U,...o===Xy?{letterSpacing:`${XC(M/j)}em`}:{},...O,...m}),C={h1:x(u,96,1.167,-1.5),h2:x(u,60,1.2,-.5),h3:x(c,48,1.167,0),h4:x(c,34,1.235,.25),h5:x(c,24,1.334,0),h6:x(d,20,1.6,.15),subtitle1:x(c,16,1.75,.15),subtitle2:x(d,14,1.57,.1),body1:x(c,16,1.5,.15),body2:x(c,14,1.43,.15),button:x(d,14,1.75,.4,Yy),caption:x(c,12,1.66,.4),overline:x(c,12,2.66,1,Yy),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return nn({htmlFontSize:h,pxToRem:R,fontFamily:o,fontSize:l,fontWeightLight:u,fontWeightRegular:c,fontWeightMedium:d,fontWeightBold:p,...C},S,{clone:!1})}const IC=.2,WC=.14,KC=.12;function Ce(...n){return[`${n[0]}px ${n[1]}px ${n[2]}px ${n[3]}px rgba(0,0,0,${IC})`,`${n[4]}px ${n[5]}px ${n[6]}px ${n[7]}px rgba(0,0,0,${WC})`,`${n[8]}px ${n[9]}px ${n[10]}px ${n[11]}px rgba(0,0,0,${KC})`].join(",")}const QC=["none",Ce(0,2,1,-1,0,1,1,0,0,1,3,0),Ce(0,3,1,-2,0,2,2,0,0,1,5,0),Ce(0,3,3,-2,0,3,4,0,0,1,8,0),Ce(0,2,4,-1,0,4,5,0,0,1,10,0),Ce(0,3,5,-1,0,5,8,0,0,1,14,0),Ce(0,3,5,-1,0,6,10,0,0,1,18,0),Ce(0,4,5,-2,0,7,10,1,0,2,16,1),Ce(0,5,5,-3,0,8,10,1,0,3,14,2),Ce(0,5,6,-3,0,9,12,1,0,3,16,2),Ce(0,6,6,-3,0,10,14,1,0,4,18,3),Ce(0,6,7,-4,0,11,15,1,0,4,20,3),Ce(0,7,8,-4,0,12,17,2,0,5,22,4),Ce(0,7,8,-4,0,13,19,2,0,5,24,4),Ce(0,7,9,-4,0,14,21,2,0,5,26,4),Ce(0,8,9,-5,0,15,22,2,0,6,28,5),Ce(0,8,10,-5,0,16,24,2,0,6,30,5),Ce(0,8,11,-5,0,17,26,2,0,6,32,5),Ce(0,9,11,-5,0,18,28,2,0,7,34,6),Ce(0,9,12,-6,0,19,29,2,0,7,36,6),Ce(0,10,13,-6,0,20,31,3,0,8,38,7),Ce(0,10,13,-6,0,21,33,3,0,8,40,7),Ce(0,10,14,-6,0,22,35,3,0,8,42,7),Ce(0,11,14,-7,0,23,36,3,0,9,44,8),Ce(0,11,15,-7,0,24,38,3,0,9,46,8)],ZC={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},FC={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function Iy(n){return`${Math.round(n)}ms`}function JC(n){if(!n)return 0;const r=n/36;return Math.min(Math.round((4+15*r**.25+r/5)*10),3e3)}function tT(n){const r={...ZC,...n.easing},o={...FC,...n.duration};return{getAutoHeightDuration:JC,create:(u=["all"],c={})=>{const{duration:d=o.standard,easing:p=r.easeInOut,delay:h=0,...m}=c;return(Array.isArray(u)?u:[u]).map(y=>`${y} ${typeof d=="string"?d:Iy(d)} ${p} ${typeof h=="string"?h:Iy(h)}`).join(",")},...n,easing:r,duration:o}}const eT={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500};function nT(n){return ra(n)||typeof n>"u"||typeof n=="string"||typeof n=="boolean"||typeof n=="number"||Array.isArray(n)}function C0(n={}){const r={...n};function o(l){const u=Object.entries(l);for(let c=0;c<u.length;c++){const[d,p]=u[c];!nT(p)||d.startsWith("unstable_")?delete l[d]:ra(p)&&(l[d]={...p},o(l[d]))}}return o(r),`import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';

const theme = ${JSON.stringify(r,null,2)};

theme.breakpoints = createBreakpoints(theme.breakpoints || {});
theme.transitions = createTransitions(theme.transitions || {});

export default theme;`}function vd(n={},...r){const{breakpoints:o,mixins:l={},spacing:u,palette:c={},transitions:d={},typography:p={},shape:h,...m}=n;if(n.vars&&n.generateThemeVars===void 0)throw new Error(za(20));const y=rp(c),S=El(n);let E=nn(S,{mixins:YC(S.breakpoints,l),palette:y,shadows:QC.slice(),typography:x0(y,p),transitions:tT(d),zIndex:{...eT}});return E=nn(E,m),E=r.reduce((R,x)=>nn(R,x),E),E.unstable_sxConfig={...Tl,...m==null?void 0:m.unstable_sxConfig},E.unstable_sx=function(x){return ir({sx:x,theme:this})},E.toRuntimeSource=C0,E}function bd(n){let r;return n<1?r=5.11916*n**2:r=4.5*Math.log(n+1)+2,Math.round(r*10)/1e3}const aT=[...Array(25)].map((n,r)=>{if(r===0)return"none";const o=bd(r);return`linear-gradient(rgba(255 255 255 / ${o}), rgba(255 255 255 / ${o}))`});function T0(n){return{inputPlaceholder:n==="dark"?.5:.42,inputUnderline:n==="dark"?.7:.42,switchTrackDisabled:n==="dark"?.2:.12,switchTrack:n==="dark"?.3:.38}}function E0(n){return n==="dark"?aT:[]}function rT(n){const{palette:r={mode:"light"},opacity:o,overlays:l,...u}=n,c=rp(r);return{palette:c,opacity:{...T0(c.mode),...o},overlays:l||E0(c.mode),...u}}function oT(n){var r;return!!n[0].match(/(cssVarPrefix|colorSchemeSelector|rootSelector|typography|mixins|breakpoints|direction|transitions)/)||!!n[0].match(/sxConfig$/)||n[0]==="palette"&&!!((r=n[1])!=null&&r.match(/(mode|contrastThreshold|tonalOffset)/))}const iT=n=>[...[...Array(25)].map((r,o)=>`--${n?`${n}-`:""}overlays-${o}`),`--${n?`${n}-`:""}palette-AppBar-darkBg`,`--${n?`${n}-`:""}palette-AppBar-darkColor`],lT=n=>(r,o)=>{const l=n.rootSelector||":root",u=n.colorSchemeSelector;let c=u;if(u==="class"&&(c=".%s"),u==="data"&&(c="[data-%s]"),u!=null&&u.startsWith("data-")&&!u.includes("%s")&&(c=`[${u}="%s"]`),n.defaultColorScheme===r){if(r==="dark"){const d={};return iT(n.cssVarPrefix).forEach(p=>{d[p]=o[p],delete o[p]}),c==="media"?{[l]:o,"@media (prefers-color-scheme: dark)":{[l]:d}}:c?{[c.replace("%s",r)]:d,[`${l}, ${c.replace("%s",r)}`]:o}:{[l]:{...o,...d}}}if(c&&c!=="media")return`${l}, ${c.replace("%s",String(r))}`}else if(r){if(c==="media")return{[`@media (prefers-color-scheme: ${String(r)})`]:{[l]:o}};if(c)return c.replace("%s",String(r))}return l};function sT(n,r){r.forEach(o=>{n[o]||(n[o]={})})}function J(n,r,o){!n[r]&&o&&(n[r]=o)}function nl(n){return typeof n!="string"||!n.startsWith("hsl")?n:p0(n)}function wa(n,r){`${r}Channel`in n||(n[`${r}Channel`]=el(nl(n[r])))}function uT(n){return typeof n=="number"?`${n}px`:typeof n=="string"||typeof n=="function"||Array.isArray(n)?n:"8px"}const ta=n=>{try{return n()}catch{}},cT=(n="mui")=>cC(n);function id(n,r,o,l){if(!r)return;r=r===!0?{}:r;const u=l==="dark"?"dark":"light";if(!o){n[l]=rT({...r,palette:{mode:u,...r==null?void 0:r.palette}});return}const{palette:c,...d}=vd({...o,palette:{mode:u,...r==null?void 0:r.palette}});return n[l]={...r,palette:c,opacity:{...T0(u),...r==null?void 0:r.opacity},overlays:(r==null?void 0:r.overlays)||E0(u)},d}function fT(n={},...r){const{colorSchemes:o={light:!0},defaultColorScheme:l,disableCssColorScheme:u=!1,cssVarPrefix:c="mui",shouldSkipGeneratingVar:d=oT,colorSchemeSelector:p=o.light&&o.dark?"media":void 0,rootSelector:h=":root",...m}=n,y=Object.keys(o)[0],S=l||(o.light&&y!=="light"?"light":y),E=cT(c),{[S]:R,light:x,dark:C,...D}=o,j={...D};let U=R;if((S==="dark"&&!("dark"in o)||S==="light"&&!("light"in o))&&(U=!0),!U)throw new Error(za(21,S));const M=id(j,U,m,S);x&&!j.light&&id(j,x,void 0,"light"),C&&!j.dark&&id(j,C,void 0,"dark");let O={defaultColorScheme:S,...M,cssVarPrefix:c,colorSchemeSelector:p,rootSelector:h,getCssVar:E,colorSchemes:j,font:{...VC(M.typography),...M.font},spacing:uT(m.spacing)};Object.keys(O.colorSchemes).forEach(K=>{const v=O.colorSchemes[K].palette,_=W=>{const Z=W.split("-"),it=Z[1],F=Z[2];return E(W,v[it][F])};if(v.mode==="light"&&(J(v.common,"background","#fff"),J(v.common,"onBackground","#000")),v.mode==="dark"&&(J(v.common,"background","#000"),J(v.common,"onBackground","#fff")),sT(v,["Alert","AppBar","Avatar","Button","Chip","FilledInput","LinearProgress","Skeleton","Slider","SnackbarContent","SpeedDialAction","StepConnector","StepContent","Switch","TableCell","Tooltip"]),v.mode==="light"){J(v.Alert,"errorColor",me(v.error.light,.6)),J(v.Alert,"infoColor",me(v.info.light,.6)),J(v.Alert,"successColor",me(v.success.light,.6)),J(v.Alert,"warningColor",me(v.warning.light,.6)),J(v.Alert,"errorFilledBg",_("palette-error-main")),J(v.Alert,"infoFilledBg",_("palette-info-main")),J(v.Alert,"successFilledBg",_("palette-success-main")),J(v.Alert,"warningFilledBg",_("palette-warning-main")),J(v.Alert,"errorFilledColor",ta(()=>v.getContrastText(v.error.main))),J(v.Alert,"infoFilledColor",ta(()=>v.getContrastText(v.info.main))),J(v.Alert,"successFilledColor",ta(()=>v.getContrastText(v.success.main))),J(v.Alert,"warningFilledColor",ta(()=>v.getContrastText(v.warning.main))),J(v.Alert,"errorStandardBg",ge(v.error.light,.9)),J(v.Alert,"infoStandardBg",ge(v.info.light,.9)),J(v.Alert,"successStandardBg",ge(v.success.light,.9)),J(v.Alert,"warningStandardBg",ge(v.warning.light,.9)),J(v.Alert,"errorIconColor",_("palette-error-main")),J(v.Alert,"infoIconColor",_("palette-info-main")),J(v.Alert,"successIconColor",_("palette-success-main")),J(v.Alert,"warningIconColor",_("palette-warning-main")),J(v.AppBar,"defaultBg",_("palette-grey-100")),J(v.Avatar,"defaultBg",_("palette-grey-400")),J(v.Button,"inheritContainedBg",_("palette-grey-300")),J(v.Button,"inheritContainedHoverBg",_("palette-grey-A100")),J(v.Chip,"defaultBorder",_("palette-grey-400")),J(v.Chip,"defaultAvatarColor",_("palette-grey-700")),J(v.Chip,"defaultIconColor",_("palette-grey-700")),J(v.FilledInput,"bg","rgba(0, 0, 0, 0.06)"),J(v.FilledInput,"hoverBg","rgba(0, 0, 0, 0.09)"),J(v.FilledInput,"disabledBg","rgba(0, 0, 0, 0.12)"),J(v.LinearProgress,"primaryBg",ge(v.primary.main,.62)),J(v.LinearProgress,"secondaryBg",ge(v.secondary.main,.62)),J(v.LinearProgress,"errorBg",ge(v.error.main,.62)),J(v.LinearProgress,"infoBg",ge(v.info.main,.62)),J(v.LinearProgress,"successBg",ge(v.success.main,.62)),J(v.LinearProgress,"warningBg",ge(v.warning.main,.62)),J(v.Skeleton,"bg",`rgba(${_("palette-text-primaryChannel")} / 0.11)`),J(v.Slider,"primaryTrack",ge(v.primary.main,.62)),J(v.Slider,"secondaryTrack",ge(v.secondary.main,.62)),J(v.Slider,"errorTrack",ge(v.error.main,.62)),J(v.Slider,"infoTrack",ge(v.info.main,.62)),J(v.Slider,"successTrack",ge(v.success.main,.62)),J(v.Slider,"warningTrack",ge(v.warning.main,.62));const W=Ys(v.background.default,.8);J(v.SnackbarContent,"bg",W),J(v.SnackbarContent,"color",ta(()=>v.getContrastText(W))),J(v.SpeedDialAction,"fabHoverBg",Ys(v.background.paper,.15)),J(v.StepConnector,"border",_("palette-grey-400")),J(v.StepContent,"border",_("palette-grey-400")),J(v.Switch,"defaultColor",_("palette-common-white")),J(v.Switch,"defaultDisabledColor",_("palette-grey-100")),J(v.Switch,"primaryDisabledColor",ge(v.primary.main,.62)),J(v.Switch,"secondaryDisabledColor",ge(v.secondary.main,.62)),J(v.Switch,"errorDisabledColor",ge(v.error.main,.62)),J(v.Switch,"infoDisabledColor",ge(v.info.main,.62)),J(v.Switch,"successDisabledColor",ge(v.success.main,.62)),J(v.Switch,"warningDisabledColor",ge(v.warning.main,.62)),J(v.TableCell,"border",ge(Vs(v.divider,1),.88)),J(v.Tooltip,"bg",Vs(v.grey[700],.92))}if(v.mode==="dark"){J(v.Alert,"errorColor",ge(v.error.light,.6)),J(v.Alert,"infoColor",ge(v.info.light,.6)),J(v.Alert,"successColor",ge(v.success.light,.6)),J(v.Alert,"warningColor",ge(v.warning.light,.6)),J(v.Alert,"errorFilledBg",_("palette-error-dark")),J(v.Alert,"infoFilledBg",_("palette-info-dark")),J(v.Alert,"successFilledBg",_("palette-success-dark")),J(v.Alert,"warningFilledBg",_("palette-warning-dark")),J(v.Alert,"errorFilledColor",ta(()=>v.getContrastText(v.error.dark))),J(v.Alert,"infoFilledColor",ta(()=>v.getContrastText(v.info.dark))),J(v.Alert,"successFilledColor",ta(()=>v.getContrastText(v.success.dark))),J(v.Alert,"warningFilledColor",ta(()=>v.getContrastText(v.warning.dark))),J(v.Alert,"errorStandardBg",me(v.error.light,.9)),J(v.Alert,"infoStandardBg",me(v.info.light,.9)),J(v.Alert,"successStandardBg",me(v.success.light,.9)),J(v.Alert,"warningStandardBg",me(v.warning.light,.9)),J(v.Alert,"errorIconColor",_("palette-error-main")),J(v.Alert,"infoIconColor",_("palette-info-main")),J(v.Alert,"successIconColor",_("palette-success-main")),J(v.Alert,"warningIconColor",_("palette-warning-main")),J(v.AppBar,"defaultBg",_("palette-grey-900")),J(v.AppBar,"darkBg",_("palette-background-paper")),J(v.AppBar,"darkColor",_("palette-text-primary")),J(v.Avatar,"defaultBg",_("palette-grey-600")),J(v.Button,"inheritContainedBg",_("palette-grey-800")),J(v.Button,"inheritContainedHoverBg",_("palette-grey-700")),J(v.Chip,"defaultBorder",_("palette-grey-700")),J(v.Chip,"defaultAvatarColor",_("palette-grey-300")),J(v.Chip,"defaultIconColor",_("palette-grey-300")),J(v.FilledInput,"bg","rgba(255, 255, 255, 0.09)"),J(v.FilledInput,"hoverBg","rgba(255, 255, 255, 0.13)"),J(v.FilledInput,"disabledBg","rgba(255, 255, 255, 0.12)"),J(v.LinearProgress,"primaryBg",me(v.primary.main,.5)),J(v.LinearProgress,"secondaryBg",me(v.secondary.main,.5)),J(v.LinearProgress,"errorBg",me(v.error.main,.5)),J(v.LinearProgress,"infoBg",me(v.info.main,.5)),J(v.LinearProgress,"successBg",me(v.success.main,.5)),J(v.LinearProgress,"warningBg",me(v.warning.main,.5)),J(v.Skeleton,"bg",`rgba(${_("palette-text-primaryChannel")} / 0.13)`),J(v.Slider,"primaryTrack",me(v.primary.main,.5)),J(v.Slider,"secondaryTrack",me(v.secondary.main,.5)),J(v.Slider,"errorTrack",me(v.error.main,.5)),J(v.Slider,"infoTrack",me(v.info.main,.5)),J(v.Slider,"successTrack",me(v.success.main,.5)),J(v.Slider,"warningTrack",me(v.warning.main,.5));const W=Ys(v.background.default,.98);J(v.SnackbarContent,"bg",W),J(v.SnackbarContent,"color",ta(()=>v.getContrastText(W))),J(v.SpeedDialAction,"fabHoverBg",Ys(v.background.paper,.15)),J(v.StepConnector,"border",_("palette-grey-600")),J(v.StepContent,"border",_("palette-grey-600")),J(v.Switch,"defaultColor",_("palette-grey-300")),J(v.Switch,"defaultDisabledColor",_("palette-grey-600")),J(v.Switch,"primaryDisabledColor",me(v.primary.main,.55)),J(v.Switch,"secondaryDisabledColor",me(v.secondary.main,.55)),J(v.Switch,"errorDisabledColor",me(v.error.main,.55)),J(v.Switch,"infoDisabledColor",me(v.info.main,.55)),J(v.Switch,"successDisabledColor",me(v.success.main,.55)),J(v.Switch,"warningDisabledColor",me(v.warning.main,.55)),J(v.TableCell,"border",me(Vs(v.divider,1),.68)),J(v.Tooltip,"bg",Vs(v.grey[700],.92))}wa(v.background,"default"),wa(v.background,"paper"),wa(v.common,"background"),wa(v.common,"onBackground"),wa(v,"divider"),Object.keys(v).forEach(W=>{const Z=v[W];W!=="tonalOffset"&&Z&&typeof Z=="object"&&(Z.main&&J(v[W],"mainChannel",el(nl(Z.main))),Z.light&&J(v[W],"lightChannel",el(nl(Z.light))),Z.dark&&J(v[W],"darkChannel",el(nl(Z.dark))),Z.contrastText&&J(v[W],"contrastTextChannel",el(nl(Z.contrastText))),W==="text"&&(wa(v[W],"primary"),wa(v[W],"secondary")),W==="action"&&(Z.active&&wa(v[W],"active"),Z.selected&&wa(v[W],"selected")))})}),O=r.reduce((K,v)=>nn(K,v),O);const A={prefix:c,disableCssColorScheme:u,shouldSkipGeneratingVar:d,getSelector:lT(O)},{vars:$,generateThemeVars:H,generateStyleSheets:P}=pC(O,A);return O.vars=$,Object.entries(O.colorSchemes[O.defaultColorScheme]).forEach(([K,v])=>{O[K]=v}),O.generateThemeVars=H,O.generateStyleSheets=P,O.generateSpacing=function(){return r0(m.spacing,Wd(this))},O.getColorSchemeSelector=hC(p),O.spacing=O.generateSpacing(),O.shouldSkipGeneratingVar=d,O.unstable_sxConfig={...Tl,...m==null?void 0:m.unstable_sxConfig},O.unstable_sx=function(v){return ir({sx:v,theme:this})},O.toRuntimeSource=C0,O}function Wy(n,r,o){n.colorSchemes&&o&&(n.colorSchemes[r]={...o!==!0&&o,palette:rp({...o===!0?{}:o.palette,mode:r})})}function zu(n={},...r){const{palette:o,cssVariables:l=!1,colorSchemes:u=o?void 0:{light:!0},defaultColorScheme:c=o==null?void 0:o.mode,...d}=n,p=c||"light",h=u==null?void 0:u[p],m={...u,...o?{[p]:{...typeof h!="boolean"&&h,palette:o}}:void 0};if(l===!1){if(!("colorSchemes"in n))return vd(n,...r);let y=o;"palette"in n||m[p]&&(m[p]!==!0?y=m[p].palette:p==="dark"&&(y={mode:"dark"}));const S=vd({...n,palette:y},...r);return S.defaultColorScheme=p,S.colorSchemes=m,S.palette.mode==="light"&&(S.colorSchemes.light={...m.light!==!0&&m.light,palette:S.palette},Wy(S,"dark",m.dark)),S.palette.mode==="dark"&&(S.colorSchemes.dark={...m.dark!==!0&&m.dark,palette:S.palette},Wy(S,"light",m.light)),S}return!o&&!("light"in m)&&p==="light"&&(m.light=!0),fT({...d,colorSchemes:m,defaultColorScheme:p,...typeof l!="boolean"&&l},...r)}const op=zu();function Go(){const n=Rl(op);return n[ia]||n}function R0(n){return n!=="ownerState"&&n!=="theme"&&n!=="sx"&&n!=="as"}const On=n=>R0(n)&&n!=="classes",ut=c0({themeId:ia,defaultTheme:op,rootShouldForwardProp:On});function dT({theme:n,...r}){const o=ia in n?n[ia]:void 0;return T.jsx(y0,{...r,themeId:o?ia:void 0,theme:o||n})}const Xs={colorSchemeStorageKey:"mui-color-scheme",defaultLightColorScheme:"light",defaultDarkColorScheme:"dark",modeStorageKey:"mui-mode"},{CssVarsProvider:pT}=uC({themeId:ia,theme:()=>zu({cssVariables:!0}),colorSchemeStorageKey:Xs.colorSchemeStorageKey,modeStorageKey:Xs.modeStorageKey,defaultColorScheme:{light:Xs.defaultLightColorScheme,dark:Xs.defaultDarkColorScheme},resolveTheme:n=>{const r={...n,typography:x0(n.palette,n.typography)};return r.unstable_sx=function(l){return ir({sx:l,theme:this})},r}}),hT=pT;function mT({theme:n,...r}){const o=w.useMemo(()=>{if(typeof n=="function")return n;const l=ia in n?n[ia]:n;return"colorSchemes"in l?null:"vars"in l?n:{...n,vars:null}},[n]);return o?T.jsx(dT,{theme:o,...r}):T.jsx(hT,{theme:n,...r})}function gT(n){return T.jsx(M2,{...n,defaultTheme:op,themeId:ia})}function ip(n){return function(o){return T.jsx(gT,{styles:typeof n=="function"?l=>n({theme:l,...o}):n})}}function yT(){return Qd}function Nt(n){return tC(n)}const Sd=typeof ip({})=="function",vT=(n,r)=>({WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",boxSizing:"border-box",WebkitTextSizeAdjust:"100%",...r&&!n.vars&&{colorScheme:n.palette.mode}}),bT=n=>({color:(n.vars||n).palette.text.primary,...n.typography.body1,backgroundColor:(n.vars||n).palette.background.default,"@media print":{backgroundColor:(n.vars||n).palette.common.white}}),w0=(n,r=!1)=>{var c,d;const o={};r&&n.colorSchemes&&typeof n.getColorSchemeSelector=="function"&&Object.entries(n.colorSchemes).forEach(([p,h])=>{var y,S;const m=n.getColorSchemeSelector(p);m.startsWith("@")?o[m]={":root":{colorScheme:(y=h.palette)==null?void 0:y.mode}}:o[m.replace(/\s*&/,"")]={colorScheme:(S=h.palette)==null?void 0:S.mode}});let l={html:vT(n,r),"*, *::before, *::after":{boxSizing:"inherit"},"strong, b":{fontWeight:n.typography.fontWeightBold},body:{margin:0,...bT(n),"&::backdrop":{backgroundColor:(n.vars||n).palette.background.default}},...o};const u=(d=(c=n.components)==null?void 0:c.MuiCssBaseline)==null?void 0:d.styleOverrides;return u&&(l=[l,u]),l},ou="mui-ecs",ST=n=>{const r=w0(n,!1),o=Array.isArray(r)?r[0]:r;return!n.vars&&o&&(o.html[`:root:has(${ou})`]={colorScheme:n.palette.mode}),n.colorSchemes&&Object.entries(n.colorSchemes).forEach(([l,u])=>{var d,p;const c=n.getColorSchemeSelector(l);c.startsWith("@")?o[c]={[`:root:not(:has(.${ou}))`]:{colorScheme:(d=u.palette)==null?void 0:d.mode}}:o[c.replace(/\s*&/,"")]={[`&:not(:has(.${ou}))`]:{colorScheme:(p=u.palette)==null?void 0:p.mode}}}),r},xT=ip(Sd?({theme:n,enableColorScheme:r})=>w0(n,r):({theme:n})=>ST(n));function CT(n){const r=Nt({props:n,name:"MuiCssBaseline"}),{children:o,enableColorScheme:l=!1}=r;return T.jsxs(w.Fragment,{children:[Sd&&T.jsx(xT,{enableColorScheme:l}),!Sd&&!l&&T.jsx("span",{className:ou,style:{display:"none"}}),o]})}const TT=zu({palette:{mode:"light",primary:{main:"#0079bf",light:"#4da6d9",dark:"#005582",contrastText:"#ffffff"},secondary:{main:"#026aa7",light:"#4da6d9",dark:"#004a7c",contrastText:"#ffffff"},background:{default:"#f4f5f7",paper:"#ffffff"},text:{primary:"#172b4d",secondary:"#5e6c84"},success:{main:"#61bd4f"},warning:{main:"#f2d600"},error:{main:"#eb5a46"},info:{main:"#00c2e0"}},typography:{fontFamily:["Roboto","-apple-system","BlinkMacSystemFont","Segoe UI","Helvetica Neue","Arial","sans-serif"].join(","),fontWeightLight:300,fontWeightRegular:400,fontWeightMedium:500,fontWeightBold:700,h1:{fontSize:"2rem",fontWeight:500,color:"#172b4d",lineHeight:1.2},h2:{fontSize:"1.5rem",fontWeight:500,color:"#172b4d",lineHeight:1.3},h3:{fontSize:"1.25rem",fontWeight:500,color:"#172b4d",lineHeight:1.4},h4:{fontSize:"1.125rem",fontWeight:500,color:"#172b4d",lineHeight:1.4},h5:{fontSize:"1rem",fontWeight:500,color:"#172b4d",lineHeight:1.5},h6:{fontSize:"0.875rem",fontWeight:500,color:"#172b4d",lineHeight:1.6},body1:{fontSize:"1rem",fontWeight:400,color:"#172b4d",lineHeight:1.5},body2:{fontSize:"0.875rem",fontWeight:400,color:"#5e6c84",lineHeight:1.43},subtitle1:{fontSize:"1rem",fontWeight:500,color:"#172b4d",lineHeight:1.75},subtitle2:{fontSize:"0.875rem",fontWeight:500,color:"#172b4d",lineHeight:1.57},caption:{fontSize:"0.75rem",fontWeight:400,color:"#5e6c84",lineHeight:1.66},overline:{fontSize:"0.75rem",fontWeight:500,color:"#5e6c84",lineHeight:2.66,textTransform:"uppercase",letterSpacing:"0.08333em"}},shape:{borderRadius:8},components:{MuiButton:{styleOverrides:{root:{textTransform:"none",borderRadius:8,fontWeight:500,fontFamily:'Roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif'},contained:{boxShadow:"none","&:hover":{boxShadow:"0 2px 4px rgba(0,0,0,0.1)"}}}},MuiCard:{styleOverrides:{root:{borderRadius:8,boxShadow:"0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)","&:hover":{boxShadow:"0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)"}}}},MuiAppBar:{styleOverrides:{root:{boxShadow:"0 1px 3px rgba(0,0,0,0.12)"}}}}});function Ky(...n){return n.reduce((r,o)=>o==null?r:function(...u){r.apply(this,u),o.apply(this,u)},()=>{})}const Gt=eC;function ET(n){return Dt("MuiSvgIcon",n)}Mt("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);const RT=n=>{const{color:r,fontSize:o,classes:l}=n,u={root:["root",r!=="inherit"&&`color${st(r)}`,`fontSize${st(o)}`]};return Bt(u,ET,l)},wT=ut("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.color!=="inherit"&&r[`color${st(o.color)}`],r[`fontSize${st(o.fontSize)}`]]}})(Gt(({theme:n})=>{var r,o,l,u,c,d,p,h,m,y,S,E,R,x;return{userSelect:"none",width:"1em",height:"1em",display:"inline-block",flexShrink:0,transition:(u=(r=n.transitions)==null?void 0:r.create)==null?void 0:u.call(r,"fill",{duration:(l=(o=(n.vars??n).transitions)==null?void 0:o.duration)==null?void 0:l.shorter}),variants:[{props:C=>!C.hasSvgAsChild,style:{fill:"currentColor"}},{props:{fontSize:"inherit"},style:{fontSize:"inherit"}},{props:{fontSize:"small"},style:{fontSize:((d=(c=n.typography)==null?void 0:c.pxToRem)==null?void 0:d.call(c,20))||"1.25rem"}},{props:{fontSize:"medium"},style:{fontSize:((h=(p=n.typography)==null?void 0:p.pxToRem)==null?void 0:h.call(p,24))||"1.5rem"}},{props:{fontSize:"large"},style:{fontSize:((y=(m=n.typography)==null?void 0:m.pxToRem)==null?void 0:y.call(m,35))||"2.1875rem"}},...Object.entries((n.vars??n).palette).filter(([,C])=>C&&C.main).map(([C])=>{var D,j;return{props:{color:C},style:{color:(j=(D=(n.vars??n).palette)==null?void 0:D[C])==null?void 0:j.main}}}),{props:{color:"action"},style:{color:(E=(S=(n.vars??n).palette)==null?void 0:S.action)==null?void 0:E.active}},{props:{color:"disabled"},style:{color:(x=(R=(n.vars??n).palette)==null?void 0:R.action)==null?void 0:x.disabled}},{props:{color:"inherit"},style:{color:void 0}}]}})),xd=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiSvgIcon"}),{children:u,className:c,color:d="inherit",component:p="svg",fontSize:h="medium",htmlColor:m,inheritViewBox:y=!1,titleAccess:S,viewBox:E="0 0 24 24",...R}=l,x=w.isValidElement(u)&&u.type==="svg",C={...l,color:d,component:p,fontSize:h,instanceFontSize:r.fontSize,inheritViewBox:y,viewBox:E,hasSvgAsChild:x},D={};y||(D.viewBox=E);const j=RT(C);return T.jsxs(wT,{as:p,className:ht(j.root,c),focusable:"false",color:m,"aria-hidden":S?void 0:!0,role:S?"img":void 0,ref:o,...D,...R,...x&&u.props,ownerState:C,children:[x?u.props.children:u,S?T.jsx("title",{children:S}):null]})});xd.muiName="SvgIcon";function Mn(n,r){function o(l,u){return T.jsx(xd,{"data-testid":void 0,ref:u,...l,children:n})}return o.muiName=xd.muiName,w.memo(w.forwardRef(o))}function A0(n,r=166){let o;function l(...u){const c=()=>{n.apply(this,u)};clearTimeout(o),o=setTimeout(c,r)}return l.clear=()=>{clearTimeout(o)},l}function wn(n){return n&&n.ownerDocument||document}function Ba(n){return wn(n).defaultView||window}function Qy(n,r){typeof n=="function"?n(r):n&&(n.current=r)}let Zy=0;function AT(n){const[r,o]=w.useState(n),l=n||r;return w.useEffect(()=>{r==null&&(Zy+=1,o(`mui-${Zy}`))},[r]),l}const OT={...dd},Fy=OT.useId;function Vo(n){if(Fy!==void 0){const r=Fy();return n??r}return AT(n)}function Cd(n){const{controlled:r,default:o,name:l,state:u="value"}=n,{current:c}=w.useRef(r!==void 0),[d,p]=w.useState(o),h=c?r:d,m=w.useCallback(y=>{c||p(y)},[]);return[h,m]}function Ma(n){const r=w.useRef(n);return Wn(()=>{r.current=n}),w.useRef((...o)=>(0,r.current)(...o)).current}function Pe(...n){const r=w.useRef(void 0),o=w.useCallback(l=>{const u=n.map(c=>{if(c==null)return null;if(typeof c=="function"){const d=c,p=d(l);return typeof p=="function"?p:()=>{d(null)}}return c.current=l,()=>{c.current=null}});return()=>{u.forEach(c=>c==null?void 0:c())}},n);return w.useMemo(()=>n.every(l=>l==null)?null:l=>{r.current&&(r.current(),r.current=void 0),l!=null&&(r.current=o(l))},n)}function MT(n,r){const o=n.charCodeAt(2);return n[0]==="o"&&n[1]==="n"&&o>=65&&o<=90&&typeof r=="function"}function zT(n,r){if(!n)return r;function o(d,p){const h={};return Object.keys(p).forEach(m=>{MT(m,p[m])&&typeof d[m]=="function"&&(h[m]=(...y)=>{d[m](...y),p[m](...y)})}),h}if(typeof n=="function"||typeof r=="function")return d=>{const p=typeof r=="function"?r(d):r,h=typeof n=="function"?n({...d,...p}):n,m=ht(d==null?void 0:d.className,p==null?void 0:p.className,h==null?void 0:h.className),y=o(h,p);return{...p,...h,...y,...!!m&&{className:m},...(p==null?void 0:p.style)&&(h==null?void 0:h.style)&&{style:{...p.style,...h.style}},...(p==null?void 0:p.sx)&&(h==null?void 0:h.sx)&&{sx:[...Array.isArray(p.sx)?p.sx:[p.sx],...Array.isArray(h.sx)?h.sx:[h.sx]]}}};const l=r,u=o(n,l),c=ht(l==null?void 0:l.className,n==null?void 0:n.className);return{...r,...n,...u,...!!c&&{className:c},...(l==null?void 0:l.style)&&(n==null?void 0:n.style)&&{style:{...l.style,...n.style}},...(l==null?void 0:l.sx)&&(n==null?void 0:n.sx)&&{sx:[...Array.isArray(l.sx)?l.sx:[l.sx],...Array.isArray(n.sx)?n.sx:[n.sx]]}}}function O0(n,r){if(n==null)return{};var o={};for(var l in n)if({}.hasOwnProperty.call(n,l)){if(r.indexOf(l)!==-1)continue;o[l]=n[l]}return o}function Td(n,r){return Td=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(o,l){return o.__proto__=l,o},Td(n,r)}function M0(n,r){n.prototype=Object.create(r.prototype),n.prototype.constructor=n,Td(n,r)}var z0=Lv();const Is=Uv(z0),Jy={disabled:!1},fu=oa.createContext(null);var DT=function(r){return r.scrollTop},al="unmounted",zr="exited",Dr="entering",Mo="entered",Ed="exiting",ca=function(n){M0(r,n);function r(l,u){var c;c=n.call(this,l,u)||this;var d=u,p=d&&!d.isMounting?l.enter:l.appear,h;return c.appearStatus=null,l.in?p?(h=zr,c.appearStatus=Dr):h=Mo:l.unmountOnExit||l.mountOnEnter?h=al:h=zr,c.state={status:h},c.nextCallback=null,c}r.getDerivedStateFromProps=function(u,c){var d=u.in;return d&&c.status===al?{status:zr}:null};var o=r.prototype;return o.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},o.componentDidUpdate=function(u){var c=null;if(u!==this.props){var d=this.state.status;this.props.in?d!==Dr&&d!==Mo&&(c=Dr):(d===Dr||d===Mo)&&(c=Ed)}this.updateStatus(!1,c)},o.componentWillUnmount=function(){this.cancelNextCallback()},o.getTimeouts=function(){var u=this.props.timeout,c,d,p;return c=d=p=u,u!=null&&typeof u!="number"&&(c=u.exit,d=u.enter,p=u.appear!==void 0?u.appear:d),{exit:c,enter:d,appear:p}},o.updateStatus=function(u,c){if(u===void 0&&(u=!1),c!==null)if(this.cancelNextCallback(),c===Dr){if(this.props.unmountOnExit||this.props.mountOnEnter){var d=this.props.nodeRef?this.props.nodeRef.current:Is.findDOMNode(this);d&&DT(d)}this.performEnter(u)}else this.performExit();else this.props.unmountOnExit&&this.state.status===zr&&this.setState({status:al})},o.performEnter=function(u){var c=this,d=this.props.enter,p=this.context?this.context.isMounting:u,h=this.props.nodeRef?[p]:[Is.findDOMNode(this),p],m=h[0],y=h[1],S=this.getTimeouts(),E=p?S.appear:S.enter;if(!u&&!d||Jy.disabled){this.safeSetState({status:Mo},function(){c.props.onEntered(m)});return}this.props.onEnter(m,y),this.safeSetState({status:Dr},function(){c.props.onEntering(m,y),c.onTransitionEnd(E,function(){c.safeSetState({status:Mo},function(){c.props.onEntered(m,y)})})})},o.performExit=function(){var u=this,c=this.props.exit,d=this.getTimeouts(),p=this.props.nodeRef?void 0:Is.findDOMNode(this);if(!c||Jy.disabled){this.safeSetState({status:zr},function(){u.props.onExited(p)});return}this.props.onExit(p),this.safeSetState({status:Ed},function(){u.props.onExiting(p),u.onTransitionEnd(d.exit,function(){u.safeSetState({status:zr},function(){u.props.onExited(p)})})})},o.cancelNextCallback=function(){this.nextCallback!==null&&(this.nextCallback.cancel(),this.nextCallback=null)},o.safeSetState=function(u,c){c=this.setNextCallback(c),this.setState(u,c)},o.setNextCallback=function(u){var c=this,d=!0;return this.nextCallback=function(p){d&&(d=!1,c.nextCallback=null,u(p))},this.nextCallback.cancel=function(){d=!1},this.nextCallback},o.onTransitionEnd=function(u,c){this.setNextCallback(c);var d=this.props.nodeRef?this.props.nodeRef.current:Is.findDOMNode(this),p=u==null&&!this.props.addEndListener;if(!d||p){setTimeout(this.nextCallback,0);return}if(this.props.addEndListener){var h=this.props.nodeRef?[this.nextCallback]:[d,this.nextCallback],m=h[0],y=h[1];this.props.addEndListener(m,y)}u!=null&&setTimeout(this.nextCallback,u)},o.render=function(){var u=this.state.status;if(u===al)return null;var c=this.props,d=c.children;c.in,c.mountOnEnter,c.unmountOnExit,c.appear,c.enter,c.exit,c.timeout,c.addEndListener,c.onEnter,c.onEntering,c.onEntered,c.onExit,c.onExiting,c.onExited,c.nodeRef;var p=O0(c,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]);return oa.createElement(fu.Provider,{value:null},typeof d=="function"?d(u,p):oa.cloneElement(oa.Children.only(d),p))},r}(oa.Component);ca.contextType=fu;ca.propTypes={};function Ao(){}ca.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:Ao,onEntering:Ao,onEntered:Ao,onExit:Ao,onExiting:Ao,onExited:Ao};ca.UNMOUNTED=al;ca.EXITED=zr;ca.ENTERING=Dr;ca.ENTERED=Mo;ca.EXITING=Ed;function BT(n){if(n===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n}function lp(n,r){var o=function(c){return r&&w.isValidElement(c)?r(c):c},l=Object.create(null);return n&&w.Children.map(n,function(u){return u}).forEach(function(u){l[u.key]=o(u)}),l}function NT(n,r){n=n||{},r=r||{};function o(y){return y in r?r[y]:n[y]}var l=Object.create(null),u=[];for(var c in n)c in r?u.length&&(l[c]=u,u=[]):u.push(c);var d,p={};for(var h in r){if(l[h])for(d=0;d<l[h].length;d++){var m=l[h][d];p[l[h][d]]=o(m)}p[h]=o(h)}for(d=0;d<u.length;d++)p[u[d]]=o(u[d]);return p}function Br(n,r,o){return o[r]!=null?o[r]:n.props[r]}function jT(n,r){return lp(n.children,function(o){return w.cloneElement(o,{onExited:r.bind(null,o),in:!0,appear:Br(o,"appear",n),enter:Br(o,"enter",n),exit:Br(o,"exit",n)})})}function kT(n,r,o){var l=lp(n.children),u=NT(r,l);return Object.keys(u).forEach(function(c){var d=u[c];if(w.isValidElement(d)){var p=c in r,h=c in l,m=r[c],y=w.isValidElement(m)&&!m.props.in;h&&(!p||y)?u[c]=w.cloneElement(d,{onExited:o.bind(null,d),in:!0,exit:Br(d,"exit",n),enter:Br(d,"enter",n)}):!h&&p&&!y?u[c]=w.cloneElement(d,{in:!1}):h&&p&&w.isValidElement(m)&&(u[c]=w.cloneElement(d,{onExited:o.bind(null,d),in:m.props.in,exit:Br(d,"exit",n),enter:Br(d,"enter",n)}))}}),u}var $T=Object.values||function(n){return Object.keys(n).map(function(r){return n[r]})},_T={component:"div",childFactory:function(r){return r}},sp=function(n){M0(r,n);function r(l,u){var c;c=n.call(this,l,u)||this;var d=c.handleExited.bind(BT(c));return c.state={contextValue:{isMounting:!0},handleExited:d,firstRender:!0},c}var o=r.prototype;return o.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},o.componentWillUnmount=function(){this.mounted=!1},r.getDerivedStateFromProps=function(u,c){var d=c.children,p=c.handleExited,h=c.firstRender;return{children:h?jT(u,p):kT(u,d,p),firstRender:!1}},o.handleExited=function(u,c){var d=lp(this.props.children);u.key in d||(u.props.onExited&&u.props.onExited(c),this.mounted&&this.setState(function(p){var h=lu({},p.children);return delete h[u.key],{children:h}}))},o.render=function(){var u=this.props,c=u.component,d=u.childFactory,p=O0(u,["component","childFactory"]),h=this.state.contextValue,m=$T(this.state.children).map(d);return delete p.appear,delete p.enter,delete p.exit,c===null?oa.createElement(fu.Provider,{value:h},m):oa.createElement(fu.Provider,{value:h},oa.createElement(c,p,m))},r}(oa.Component);sp.propTypes={};sp.defaultProps=_T;const tv={};function D0(n,r){const o=w.useRef(tv);return o.current===tv&&(o.current=n(r)),o}const UT=[];function LT(n){w.useEffect(n,UT)}class Du{constructor(){Vi(this,"currentId",null);Vi(this,"clear",()=>{this.currentId!==null&&(clearTimeout(this.currentId),this.currentId=null)});Vi(this,"disposeEffect",()=>this.clear)}static create(){return new Du}start(r,o){this.clear(),this.currentId=setTimeout(()=>{this.currentId=null,o()},r)}}function zo(){const n=D0(Du.create).current;return LT(n.disposeEffect),n}const B0=n=>n.scrollTop;function du(n,r){const{timeout:o,easing:l,style:u={}}=n;return{duration:u.transitionDuration??(typeof o=="number"?o:o[r.mode]||0),easing:u.transitionTimingFunction??(typeof l=="object"?l[r.mode]:l),delay:u.transitionDelay}}function HT(n){return Dt("MuiPaper",n)}Mt("MuiPaper",["root","rounded","outlined","elevation","elevation0","elevation1","elevation2","elevation3","elevation4","elevation5","elevation6","elevation7","elevation8","elevation9","elevation10","elevation11","elevation12","elevation13","elevation14","elevation15","elevation16","elevation17","elevation18","elevation19","elevation20","elevation21","elevation22","elevation23","elevation24"]);const qT=n=>{const{square:r,elevation:o,variant:l,classes:u}=n,c={root:["root",l,!r&&"rounded",l==="elevation"&&`elevation${o}`]};return Bt(c,HT,u)},PT=ut("div",{name:"MuiPaper",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[o.variant],!o.square&&r.rounded,o.variant==="elevation"&&r[`elevation${o.elevation}`]]}})(Gt(({theme:n})=>({backgroundColor:(n.vars||n).palette.background.paper,color:(n.vars||n).palette.text.primary,transition:n.transitions.create("box-shadow"),variants:[{props:({ownerState:r})=>!r.square,style:{borderRadius:n.shape.borderRadius}},{props:{variant:"outlined"},style:{border:`1px solid ${(n.vars||n).palette.divider}`}},{props:{variant:"elevation"},style:{boxShadow:"var(--Paper-shadow)",backgroundImage:"var(--Paper-overlay)"}}]}))),wl=w.forwardRef(function(r,o){var R;const l=Nt({props:r,name:"MuiPaper"}),u=Go(),{className:c,component:d="div",elevation:p=1,square:h=!1,variant:m="elevation",...y}=l,S={...l,component:d,elevation:p,square:h,variant:m},E=qT(S);return T.jsx(PT,{as:d,ownerState:S,className:ht(E.root,c),ref:o,...y,style:{...m==="elevation"&&{"--Paper-shadow":(u.vars||u).shadows[p],...u.vars&&{"--Paper-overlay":(R=u.vars.overlays)==null?void 0:R[p]},...!u.vars&&u.palette.mode==="dark"&&{"--Paper-overlay":`linear-gradient(${ye("#fff",bd(p))}, ${ye("#fff",bd(p))})`}},...y.style}})});function GT(n){return typeof n=="string"}function N0(n,r,o){return n===void 0||GT(n)?r:{...r,ownerState:{...r.ownerState,...o}}}function j0(n,r,o){return typeof n=="function"?n(r,o):n}function k0(n,r=[]){if(n===void 0)return{};const o={};return Object.keys(n).filter(l=>l.match(/^on[A-Z]/)&&typeof n[l]=="function"&&!r.includes(l)).forEach(l=>{o[l]=n[l]}),o}function ev(n){if(n===void 0)return{};const r={};return Object.keys(n).filter(o=>!(o.match(/^on[A-Z]/)&&typeof n[o]=="function")).forEach(o=>{r[o]=n[o]}),r}function $0(n){const{getSlotProps:r,additionalProps:o,externalSlotProps:l,externalForwardedProps:u,className:c}=n;if(!r){const R=ht(o==null?void 0:o.className,c,u==null?void 0:u.className,l==null?void 0:l.className),x={...o==null?void 0:o.style,...u==null?void 0:u.style,...l==null?void 0:l.style},C={...o,...u,...l};return R.length>0&&(C.className=R),Object.keys(x).length>0&&(C.style=x),{props:C,internalRef:void 0}}const d=k0({...u,...l}),p=ev(l),h=ev(u),m=r(d),y=ht(m==null?void 0:m.className,o==null?void 0:o.className,c,u==null?void 0:u.className,l==null?void 0:l.className),S={...m==null?void 0:m.style,...o==null?void 0:o.style,...u==null?void 0:u.style,...l==null?void 0:l.style},E={...m,...o,...h,...p};return y.length>0&&(E.className=y),Object.keys(S).length>0&&(E.style=S),{props:E,internalRef:m.ref}}function pe(n,r){const{className:o,elementType:l,ownerState:u,externalForwardedProps:c,internalForwardedProps:d,shouldForwardComponentProp:p=!1,...h}=r,{component:m,slots:y={[n]:void 0},slotProps:S={[n]:void 0},...E}=c,R=y[n]||l,x=j0(S[n],u),{props:{component:C,...D},internalRef:j}=$0({className:o,...h,externalForwardedProps:n==="root"?E:void 0,externalSlotProps:x}),U=Pe(j,x==null?void 0:x.ref,r.ref),M=n==="root"?C||m:C,O=N0(R,{...n==="root"&&!m&&!y[n]&&d,...n!=="root"&&!y[n]&&d,...D,...M&&!p&&{as:M},...M&&p&&{component:M},ref:U},u);return[R,O]}function pu(n){try{return n.matches(":focus-visible")}catch{}return!1}class hu{constructor(){Vi(this,"mountEffect",()=>{this.shouldMount&&!this.didMount&&this.ref.current!==null&&(this.didMount=!0,this.mounted.resolve())});this.ref={current:null},this.mounted=null,this.didMount=!1,this.shouldMount=!1,this.setShouldMount=null}static create(){return new hu}static use(){const r=D0(hu.create).current,[o,l]=w.useState(!1);return r.shouldMount=o,r.setShouldMount=l,w.useEffect(r.mountEffect,[o]),r}mount(){return this.mounted||(this.mounted=YT(),this.shouldMount=!0,this.setShouldMount(this.shouldMount)),this.mounted}start(...r){this.mount().then(()=>{var o;return(o=this.ref.current)==null?void 0:o.start(...r)})}stop(...r){this.mount().then(()=>{var o;return(o=this.ref.current)==null?void 0:o.stop(...r)})}pulsate(...r){this.mount().then(()=>{var o;return(o=this.ref.current)==null?void 0:o.pulsate(...r)})}}function VT(){return hu.use()}function YT(){let n,r;const o=new Promise((l,u)=>{n=l,r=u});return o.resolve=n,o.reject=r,o}function XT(n){const{className:r,classes:o,pulsate:l=!1,rippleX:u,rippleY:c,rippleSize:d,in:p,onExited:h,timeout:m}=n,[y,S]=w.useState(!1),E=ht(r,o.ripple,o.rippleVisible,l&&o.ripplePulsate),R={width:d,height:d,top:-(d/2)+c,left:-(d/2)+u},x=ht(o.child,y&&o.childLeaving,l&&o.childPulsate);return!p&&!y&&S(!0),w.useEffect(()=>{if(!p&&h!=null){const C=setTimeout(h,m);return()=>{clearTimeout(C)}}},[h,p,m]),T.jsx("span",{className:E,style:R,children:T.jsx("span",{className:x})})}const Ln=Mt("MuiTouchRipple",["root","ripple","rippleVisible","ripplePulsate","child","childLeaving","childPulsate"]),Rd=550,IT=80,WT=Sl`
  0% {
    transform: scale(0);
    opacity: 0.1;
  }

  100% {
    transform: scale(1);
    opacity: 0.3;
  }
`,KT=Sl`
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
`,QT=Sl`
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.92);
  }

  100% {
    transform: scale(1);
  }
`,ZT=ut("span",{name:"MuiTouchRipple",slot:"Root"})({overflow:"hidden",pointerEvents:"none",position:"absolute",zIndex:0,top:0,right:0,bottom:0,left:0,borderRadius:"inherit"}),FT=ut(XT,{name:"MuiTouchRipple",slot:"Ripple"})`
  opacity: 0;
  position: absolute;

  &.${Ln.rippleVisible} {
    opacity: 0.3;
    transform: scale(1);
    animation-name: ${WT};
    animation-duration: ${Rd}ms;
    animation-timing-function: ${({theme:n})=>n.transitions.easing.easeInOut};
  }

  &.${Ln.ripplePulsate} {
    animation-duration: ${({theme:n})=>n.transitions.duration.shorter}ms;
  }

  & .${Ln.child} {
    opacity: 1;
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: currentColor;
  }

  & .${Ln.childLeaving} {
    opacity: 0;
    animation-name: ${KT};
    animation-duration: ${Rd}ms;
    animation-timing-function: ${({theme:n})=>n.transitions.easing.easeInOut};
  }

  & .${Ln.childPulsate} {
    position: absolute;
    /* @noflip */
    left: 0px;
    top: 0;
    animation-name: ${QT};
    animation-duration: 2500ms;
    animation-timing-function: ${({theme:n})=>n.transitions.easing.easeInOut};
    animation-iteration-count: infinite;
    animation-delay: 200ms;
  }
`,JT=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiTouchRipple"}),{center:u=!1,classes:c={},className:d,...p}=l,[h,m]=w.useState([]),y=w.useRef(0),S=w.useRef(null);w.useEffect(()=>{S.current&&(S.current(),S.current=null)},[h]);const E=w.useRef(!1),R=zo(),x=w.useRef(null),C=w.useRef(null),D=w.useCallback(O=>{const{pulsate:A,rippleX:$,rippleY:H,rippleSize:P,cb:K}=O;m(v=>[...v,T.jsx(FT,{classes:{ripple:ht(c.ripple,Ln.ripple),rippleVisible:ht(c.rippleVisible,Ln.rippleVisible),ripplePulsate:ht(c.ripplePulsate,Ln.ripplePulsate),child:ht(c.child,Ln.child),childLeaving:ht(c.childLeaving,Ln.childLeaving),childPulsate:ht(c.childPulsate,Ln.childPulsate)},timeout:Rd,pulsate:A,rippleX:$,rippleY:H,rippleSize:P},y.current)]),y.current+=1,S.current=K},[c]),j=w.useCallback((O={},A={},$=()=>{})=>{const{pulsate:H=!1,center:P=u||A.pulsate,fakeElement:K=!1}=A;if((O==null?void 0:O.type)==="mousedown"&&E.current){E.current=!1;return}(O==null?void 0:O.type)==="touchstart"&&(E.current=!0);const v=K?null:C.current,_=v?v.getBoundingClientRect():{width:0,height:0,left:0,top:0};let W,Z,it;if(P||O===void 0||O.clientX===0&&O.clientY===0||!O.clientX&&!O.touches)W=Math.round(_.width/2),Z=Math.round(_.height/2);else{const{clientX:F,clientY:B}=O.touches&&O.touches.length>0?O.touches[0]:O;W=Math.round(F-_.left),Z=Math.round(B-_.top)}if(P)it=Math.sqrt((2*_.width**2+_.height**2)/3),it%2===0&&(it+=1);else{const F=Math.max(Math.abs((v?v.clientWidth:0)-W),W)*2+2,B=Math.max(Math.abs((v?v.clientHeight:0)-Z),Z)*2+2;it=Math.sqrt(F**2+B**2)}O!=null&&O.touches?x.current===null&&(x.current=()=>{D({pulsate:H,rippleX:W,rippleY:Z,rippleSize:it,cb:$})},R.start(IT,()=>{x.current&&(x.current(),x.current=null)})):D({pulsate:H,rippleX:W,rippleY:Z,rippleSize:it,cb:$})},[u,D,R]),U=w.useCallback(()=>{j({},{pulsate:!0})},[j]),M=w.useCallback((O,A)=>{if(R.clear(),(O==null?void 0:O.type)==="touchend"&&x.current){x.current(),x.current=null,R.start(0,()=>{M(O,A)});return}x.current=null,m($=>$.length>0?$.slice(1):$),S.current=A},[R]);return w.useImperativeHandle(o,()=>({pulsate:U,start:j,stop:M}),[U,j,M]),T.jsx(ZT,{className:ht(Ln.root,c.root,d),ref:C,...p,children:T.jsx(sp,{component:null,exit:!0,children:h})})});function tE(n){return Dt("MuiButtonBase",n)}const eE=Mt("MuiButtonBase",["root","disabled","focusVisible"]),nE=n=>{const{disabled:r,focusVisible:o,focusVisibleClassName:l,classes:u}=n,d=Bt({root:["root",r&&"disabled",o&&"focusVisible"]},tE,u);return o&&l&&(d.root+=` ${l}`),d},aE=ut("button",{name:"MuiButtonBase",slot:"Root"})({display:"inline-flex",alignItems:"center",justifyContent:"center",position:"relative",boxSizing:"border-box",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none",textDecoration:"none",color:"inherit","&::-moz-focus-inner":{borderStyle:"none"},[`&.${eE.disabled}`]:{pointerEvents:"none",cursor:"default"},"@media print":{colorAdjust:"exact"}}),ko=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiButtonBase"}),{action:u,centerRipple:c=!1,children:d,className:p,component:h="button",disabled:m=!1,disableRipple:y=!1,disableTouchRipple:S=!1,focusRipple:E=!1,focusVisibleClassName:R,LinkComponent:x="a",onBlur:C,onClick:D,onContextMenu:j,onDragLeave:U,onFocus:M,onFocusVisible:O,onKeyDown:A,onKeyUp:$,onMouseDown:H,onMouseLeave:P,onMouseUp:K,onTouchEnd:v,onTouchMove:_,onTouchStart:W,tabIndex:Z=0,TouchRippleProps:it,touchRippleRef:F,type:B,...V}=l,ot=w.useRef(null),Q=VT(),z=Pe(Q.ref,F),[X,et]=w.useState(!1);m&&X&&et(!1),w.useImperativeHandle(u,()=>({focusVisible:()=>{et(!0),ot.current.focus()}}),[]);const nt=Q.shouldMount&&!y&&!m;w.useEffect(()=>{X&&E&&!y&&Q.pulsate()},[y,E,X,Q]);const lt=Aa(Q,"start",H,S),ct=Aa(Q,"stop",j,S),ft=Aa(Q,"stop",U,S),Tt=Aa(Q,"stop",K,S),bt=Aa(Q,"stop",dt=>{X&&dt.preventDefault(),P&&P(dt)},S),At=Aa(Q,"start",W,S),yt=Aa(Q,"stop",v,S),wt=Aa(Q,"stop",_,S),St=Aa(Q,"stop",dt=>{pu(dt.target)||et(!1),C&&C(dt)},!1),$t=Ma(dt=>{ot.current||(ot.current=dt.currentTarget),pu(dt.target)&&(et(!0),O&&O(dt)),M&&M(dt)}),Et=()=>{const dt=ot.current;return h&&h!=="button"&&!(dt.tagName==="A"&&dt.href)},Vt=Ma(dt=>{E&&!dt.repeat&&X&&dt.key===" "&&Q.stop(dt,()=>{Q.start(dt)}),dt.target===dt.currentTarget&&Et()&&dt.key===" "&&dt.preventDefault(),A&&A(dt),dt.target===dt.currentTarget&&Et()&&dt.key==="Enter"&&!m&&(dt.preventDefault(),D&&D(dt))}),Te=Ma(dt=>{E&&dt.key===" "&&X&&!dt.defaultPrevented&&Q.stop(dt,()=>{Q.pulsate(dt)}),$&&$(dt),D&&dt.target===dt.currentTarget&&Et()&&dt.key===" "&&!dt.defaultPrevented&&D(dt)});let Lt=h;Lt==="button"&&(V.href||V.to)&&(Lt=x);const Kt={};Lt==="button"?(Kt.type=B===void 0?"button":B,Kt.disabled=m):(!V.href&&!V.to&&(Kt.role="button"),m&&(Kt["aria-disabled"]=m));const Qt=Pe(o,ot),Yt={...l,centerRipple:c,component:h,disabled:m,disableRipple:y,disableTouchRipple:S,focusRipple:E,tabIndex:Z,focusVisible:X},_t=nE(Yt);return T.jsxs(aE,{as:Lt,className:ht(_t.root,p),ownerState:Yt,onBlur:St,onClick:D,onContextMenu:ct,onFocus:$t,onKeyDown:Vt,onKeyUp:Te,onMouseDown:lt,onMouseLeave:bt,onMouseUp:Tt,onDragLeave:ft,onTouchEnd:yt,onTouchMove:wt,onTouchStart:At,ref:Qt,tabIndex:m?-1:Z,type:B,...Kt,...V,children:[d,nt?T.jsx(JT,{ref:z,center:c,...it}):null]})});function Aa(n,r,o,l=!1){return Ma(u=>(o&&o(u),l||n[r](u),!0))}function rE(n){return typeof n.main=="string"}function oE(n,r=[]){if(!rE(n))return!1;for(const o of r)if(!n.hasOwnProperty(o)||typeof n[o]!="string")return!1;return!0}function rn(n=[]){return([,r])=>r&&oE(r,n)}function iE(n){return Dt("MuiCircularProgress",n)}Mt("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);const ar=44,wd=Sl`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`,Ad=Sl`
  0% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -15px;
  }

  100% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: -126px;
  }
`,lE=typeof wd!="string"?Yd`
        animation: ${wd} 1.4s linear infinite;
      `:null,sE=typeof Ad!="string"?Yd`
        animation: ${Ad} 1.4s ease-in-out infinite;
      `:null,uE=n=>{const{classes:r,variant:o,color:l,disableShrink:u}=n,c={root:["root",o,`color${st(l)}`],svg:["svg"],circle:["circle",`circle${st(o)}`,u&&"circleDisableShrink"]};return Bt(c,iE,r)},cE=ut("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[o.variant],r[`color${st(o.color)}`]]}})(Gt(({theme:n})=>({display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:n.transitions.create("transform")}},{props:{variant:"indeterminate"},style:lE||{animation:`${wd} 1.4s linear infinite`}},...Object.entries(n.palette).filter(rn()).map(([r])=>({props:{color:r},style:{color:(n.vars||n).palette[r].main}}))]}))),fE=ut("svg",{name:"MuiCircularProgress",slot:"Svg"})({display:"block"}),dE=ut("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.circle,r[`circle${st(o.variant)}`],o.disableShrink&&r.circleDisableShrink]}})(Gt(({theme:n})=>({stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:n.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:({ownerState:r})=>r.variant==="indeterminate"&&!r.disableShrink,style:sE||{animation:`${Ad} 1.4s ease-in-out infinite`}}]}))),_0=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiCircularProgress"}),{className:u,color:c="primary",disableShrink:d=!1,size:p=40,style:h,thickness:m=3.6,value:y=0,variant:S="indeterminate",...E}=l,R={...l,color:c,disableShrink:d,size:p,thickness:m,value:y,variant:S},x=uE(R),C={},D={},j={};if(S==="determinate"){const U=2*Math.PI*((ar-m)/2);C.strokeDasharray=U.toFixed(3),j["aria-valuenow"]=Math.round(y),C.strokeDashoffset=`${((100-y)/100*U).toFixed(3)}px`,D.transform="rotate(-90deg)"}return T.jsx(cE,{className:ht(x.root,u),style:{width:p,height:p,...D,...h},ownerState:R,ref:o,role:"progressbar",...j,...E,children:T.jsx(fE,{className:x.svg,ownerState:R,viewBox:`${ar/2} ${ar/2} ${ar} ${ar}`,children:T.jsx(dE,{className:x.circle,style:C,ownerState:R,cx:ar,cy:ar,r:(ar-m)/2,fill:"none",strokeWidth:m})})})});function pE(n){return Dt("MuiIconButton",n)}const nv=Mt("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge","loading","loadingIndicator","loadingWrapper"]),hE=n=>{const{classes:r,disabled:o,color:l,edge:u,size:c,loading:d}=n,p={root:["root",d&&"loading",o&&"disabled",l!=="default"&&`color${st(l)}`,u&&`edge${st(u)}`,`size${st(c)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]};return Bt(p,pE,r)},mE=ut(ko,{name:"MuiIconButton",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.loading&&r.loading,o.color!=="default"&&r[`color${st(o.color)}`],o.edge&&r[`edge${st(o.edge)}`],r[`size${st(o.size)}`]]}})(Gt(({theme:n})=>({textAlign:"center",flex:"0 0 auto",fontSize:n.typography.pxToRem(24),padding:8,borderRadius:"50%",color:(n.vars||n).palette.action.active,transition:n.transitions.create("background-color",{duration:n.transitions.duration.shortest}),variants:[{props:r=>!r.disableRipple,style:{"--IconButton-hoverBg":n.vars?`rgba(${n.vars.palette.action.activeChannel} / ${n.vars.palette.action.hoverOpacity})`:ye(n.palette.action.active,n.palette.action.hoverOpacity),"&:hover":{backgroundColor:"var(--IconButton-hoverBg)","@media (hover: none)":{backgroundColor:"transparent"}}}},{props:{edge:"start"},style:{marginLeft:-12}},{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:{edge:"end"},style:{marginRight:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}}]})),Gt(({theme:n})=>({variants:[{props:{color:"inherit"},style:{color:"inherit"}},...Object.entries(n.palette).filter(rn()).map(([r])=>({props:{color:r},style:{color:(n.vars||n).palette[r].main}})),...Object.entries(n.palette).filter(rn()).map(([r])=>({props:{color:r},style:{"--IconButton-hoverBg":n.vars?`rgba(${(n.vars||n).palette[r].mainChannel} / ${n.vars.palette.action.hoverOpacity})`:ye((n.vars||n).palette[r].main,n.palette.action.hoverOpacity)}})),{props:{size:"small"},style:{padding:5,fontSize:n.typography.pxToRem(18)}},{props:{size:"large"},style:{padding:12,fontSize:n.typography.pxToRem(28)}}],[`&.${nv.disabled}`]:{backgroundColor:"transparent",color:(n.vars||n).palette.action.disabled},[`&.${nv.loading}`]:{color:"transparent"}}))),gE=ut("span",{name:"MuiIconButton",slot:"LoadingIndicator"})(({theme:n})=>({display:"none",position:"absolute",visibility:"visible",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:(n.vars||n).palette.action.disabled,variants:[{props:{loading:!0},style:{display:"flex"}}]})),ol=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiIconButton"}),{edge:u=!1,children:c,className:d,color:p="default",disabled:h=!1,disableFocusRipple:m=!1,size:y="medium",id:S,loading:E=null,loadingIndicator:R,...x}=l,C=Vo(S),D=R??T.jsx(_0,{"aria-labelledby":C,color:"inherit",size:16}),j={...l,edge:u,color:p,disabled:h,disableFocusRipple:m,loading:E,loadingIndicator:D,size:y},U=hE(j);return T.jsxs(mE,{id:E?C:S,className:ht(U.root,d),centerRipple:!0,focusRipple:!m,disabled:h||E,ref:o,...x,ownerState:j,children:[typeof E=="boolean"&&T.jsx("span",{className:U.loadingWrapper,style:{display:"contents"},children:T.jsx(gE,{className:U.loadingIndicator,ownerState:j,children:E&&D})}),c]})});function yE(n){return Dt("MuiTypography",n)}Mt("MuiTypography",["root","h1","h2","h3","h4","h5","h6","subtitle1","subtitle2","body1","body2","inherit","button","caption","overline","alignLeft","alignRight","alignCenter","alignJustify","noWrap","gutterBottom","paragraph"]);const vE={primary:!0,secondary:!0,error:!0,info:!0,success:!0,warning:!0,textPrimary:!0,textSecondary:!0,textDisabled:!0},bE=yT(),SE=n=>{const{align:r,gutterBottom:o,noWrap:l,paragraph:u,variant:c,classes:d}=n,p={root:["root",c,n.align!=="inherit"&&`align${st(r)}`,o&&"gutterBottom",l&&"noWrap",u&&"paragraph"]};return Bt(p,yE,d)},xE=ut("span",{name:"MuiTypography",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.variant&&r[o.variant],o.align!=="inherit"&&r[`align${st(o.align)}`],o.noWrap&&r.noWrap,o.gutterBottom&&r.gutterBottom,o.paragraph&&r.paragraph]}})(Gt(({theme:n})=>{var r;return{margin:0,variants:[{props:{variant:"inherit"},style:{font:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}},...Object.entries(n.typography).filter(([o,l])=>o!=="inherit"&&l&&typeof l=="object").map(([o,l])=>({props:{variant:o},style:l})),...Object.entries(n.palette).filter(rn()).map(([o])=>({props:{color:o},style:{color:(n.vars||n).palette[o].main}})),...Object.entries(((r=n.palette)==null?void 0:r.text)||{}).filter(([,o])=>typeof o=="string").map(([o])=>({props:{color:`text${st(o)}`},style:{color:(n.vars||n).palette.text[o]}})),{props:({ownerState:o})=>o.align!=="inherit",style:{textAlign:"var(--Typography-textAlign)"}},{props:({ownerState:o})=>o.noWrap,style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},{props:({ownerState:o})=>o.gutterBottom,style:{marginBottom:"0.35em"}},{props:({ownerState:o})=>o.paragraph,style:{marginBottom:16}}]}})),av={h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",subtitle1:"h6",subtitle2:"h6",body1:"p",body2:"p",inherit:"p"},jt=w.forwardRef(function(r,o){const{color:l,...u}=Nt({props:r,name:"MuiTypography"}),c=!vE[l],d=bE({...u,...c&&{color:l}}),{align:p="inherit",className:h,component:m,gutterBottom:y=!1,noWrap:S=!1,paragraph:E=!1,variant:R="body1",variantMapping:x=av,...C}=d,D={...d,align:p,color:l,className:h,component:m,gutterBottom:y,noWrap:S,paragraph:E,variant:R,variantMapping:x},j=m||(E?"p":x[R]||av[R])||"span",U=SE(D);return T.jsx(xE,{as:j,ref:o,className:ht(U.root,h),...C,ownerState:D,style:{...p!=="inherit"&&{"--Typography-textAlign":p},...C.style}})});function CE(n){return Dt("MuiAppBar",n)}Mt("MuiAppBar",["root","positionFixed","positionAbsolute","positionSticky","positionStatic","positionRelative","colorDefault","colorPrimary","colorSecondary","colorInherit","colorTransparent","colorError","colorInfo","colorSuccess","colorWarning"]);const TE=n=>{const{color:r,position:o,classes:l}=n,u={root:["root",`color${st(r)}`,`position${st(o)}`]};return Bt(u,CE,l)},rv=(n,r)=>n?`${n==null?void 0:n.replace(")","")}, ${r})`:r,EE=ut(wl,{name:"MuiAppBar",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[`position${st(o.position)}`],r[`color${st(o.color)}`]]}})(Gt(({theme:n})=>({display:"flex",flexDirection:"column",width:"100%",boxSizing:"border-box",flexShrink:0,variants:[{props:{position:"fixed"},style:{position:"fixed",zIndex:(n.vars||n).zIndex.appBar,top:0,left:"auto",right:0,"@media print":{position:"absolute"}}},{props:{position:"absolute"},style:{position:"absolute",zIndex:(n.vars||n).zIndex.appBar,top:0,left:"auto",right:0}},{props:{position:"sticky"},style:{position:"sticky",zIndex:(n.vars||n).zIndex.appBar,top:0,left:"auto",right:0}},{props:{position:"static"},style:{position:"static"}},{props:{position:"relative"},style:{position:"relative"}},{props:{color:"inherit"},style:{"--AppBar-color":"inherit"}},{props:{color:"default"},style:{"--AppBar-background":n.vars?n.vars.palette.AppBar.defaultBg:n.palette.grey[100],"--AppBar-color":n.vars?n.vars.palette.text.primary:n.palette.getContrastText(n.palette.grey[100]),...n.applyStyles("dark",{"--AppBar-background":n.vars?n.vars.palette.AppBar.defaultBg:n.palette.grey[900],"--AppBar-color":n.vars?n.vars.palette.text.primary:n.palette.getContrastText(n.palette.grey[900])})}},...Object.entries(n.palette).filter(rn(["contrastText"])).map(([r])=>({props:{color:r},style:{"--AppBar-background":(n.vars??n).palette[r].main,"--AppBar-color":(n.vars??n).palette[r].contrastText}})),{props:r=>r.enableColorOnDark===!0&&!["inherit","transparent"].includes(r.color),style:{backgroundColor:"var(--AppBar-background)",color:"var(--AppBar-color)"}},{props:r=>r.enableColorOnDark===!1&&!["inherit","transparent"].includes(r.color),style:{backgroundColor:"var(--AppBar-background)",color:"var(--AppBar-color)",...n.applyStyles("dark",{backgroundColor:n.vars?rv(n.vars.palette.AppBar.darkBg,"var(--AppBar-background)"):null,color:n.vars?rv(n.vars.palette.AppBar.darkColor,"var(--AppBar-color)"):null})}},{props:{color:"transparent"},style:{"--AppBar-background":"transparent","--AppBar-color":"inherit",backgroundColor:"var(--AppBar-background)",color:"var(--AppBar-color)",...n.applyStyles("dark",{backgroundImage:"none"})}}]}))),RE=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiAppBar"}),{className:u,color:c="primary",enableColorOnDark:d=!1,position:p="fixed",...h}=l,m={...l,color:c,position:p,enableColorOnDark:d},y=TE(m);return T.jsx(EE,{square:!0,component:"header",ownerState:m,elevation:4,className:ht(y.root,u,p==="fixed"&&"mui-fixed"),ref:o,...h})});var dn="top",Pn="bottom",Gn="right",pn="left",up="auto",Al=[dn,Pn,Gn,pn],$o="start",ml="end",wE="clippingParents",U0="viewport",Ki="popper",AE="reference",ov=Al.reduce(function(n,r){return n.concat([r+"-"+$o,r+"-"+ml])},[]),L0=[].concat(Al,[up]).reduce(function(n,r){return n.concat([r,r+"-"+$o,r+"-"+ml])},[]),OE="beforeRead",ME="read",zE="afterRead",DE="beforeMain",BE="main",NE="afterMain",jE="beforeWrite",kE="write",$E="afterWrite",_E=[OE,ME,zE,DE,BE,NE,jE,kE,$E];function ua(n){return n?(n.nodeName||"").toLowerCase():null}function An(n){if(n==null)return window;if(n.toString()!=="[object Window]"){var r=n.ownerDocument;return r&&r.defaultView||window}return n}function kr(n){var r=An(n).Element;return n instanceof r||n instanceof Element}function qn(n){var r=An(n).HTMLElement;return n instanceof r||n instanceof HTMLElement}function cp(n){if(typeof ShadowRoot>"u")return!1;var r=An(n).ShadowRoot;return n instanceof r||n instanceof ShadowRoot}function UE(n){var r=n.state;Object.keys(r.elements).forEach(function(o){var l=r.styles[o]||{},u=r.attributes[o]||{},c=r.elements[o];!qn(c)||!ua(c)||(Object.assign(c.style,l),Object.keys(u).forEach(function(d){var p=u[d];p===!1?c.removeAttribute(d):c.setAttribute(d,p===!0?"":p)}))})}function LE(n){var r=n.state,o={popper:{position:r.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(r.elements.popper.style,o.popper),r.styles=o,r.elements.arrow&&Object.assign(r.elements.arrow.style,o.arrow),function(){Object.keys(r.elements).forEach(function(l){var u=r.elements[l],c=r.attributes[l]||{},d=Object.keys(r.styles.hasOwnProperty(l)?r.styles[l]:o[l]),p=d.reduce(function(h,m){return h[m]="",h},{});!qn(u)||!ua(u)||(Object.assign(u.style,p),Object.keys(c).forEach(function(h){u.removeAttribute(h)}))})}}const HE={name:"applyStyles",enabled:!0,phase:"write",fn:UE,effect:LE,requires:["computeStyles"]};function sa(n){return n.split("-")[0]}var jr=Math.max,mu=Math.min,_o=Math.round;function Od(){var n=navigator.userAgentData;return n!=null&&n.brands&&Array.isArray(n.brands)?n.brands.map(function(r){return r.brand+"/"+r.version}).join(" "):navigator.userAgent}function H0(){return!/^((?!chrome|android).)*safari/i.test(Od())}function Uo(n,r,o){r===void 0&&(r=!1),o===void 0&&(o=!1);var l=n.getBoundingClientRect(),u=1,c=1;r&&qn(n)&&(u=n.offsetWidth>0&&_o(l.width)/n.offsetWidth||1,c=n.offsetHeight>0&&_o(l.height)/n.offsetHeight||1);var d=kr(n)?An(n):window,p=d.visualViewport,h=!H0()&&o,m=(l.left+(h&&p?p.offsetLeft:0))/u,y=(l.top+(h&&p?p.offsetTop:0))/c,S=l.width/u,E=l.height/c;return{width:S,height:E,top:y,right:m+S,bottom:y+E,left:m,x:m,y}}function fp(n){var r=Uo(n),o=n.offsetWidth,l=n.offsetHeight;return Math.abs(r.width-o)<=1&&(o=r.width),Math.abs(r.height-l)<=1&&(l=r.height),{x:n.offsetLeft,y:n.offsetTop,width:o,height:l}}function q0(n,r){var o=r.getRootNode&&r.getRootNode();if(n.contains(r))return!0;if(o&&cp(o)){var l=r;do{if(l&&n.isSameNode(l))return!0;l=l.parentNode||l.host}while(l)}return!1}function Na(n){return An(n).getComputedStyle(n)}function qE(n){return["table","td","th"].indexOf(ua(n))>=0}function sr(n){return((kr(n)?n.ownerDocument:n.document)||window.document).documentElement}function Bu(n){return ua(n)==="html"?n:n.assignedSlot||n.parentNode||(cp(n)?n.host:null)||sr(n)}function iv(n){return!qn(n)||Na(n).position==="fixed"?null:n.offsetParent}function PE(n){var r=/firefox/i.test(Od()),o=/Trident/i.test(Od());if(o&&qn(n)){var l=Na(n);if(l.position==="fixed")return null}var u=Bu(n);for(cp(u)&&(u=u.host);qn(u)&&["html","body"].indexOf(ua(u))<0;){var c=Na(u);if(c.transform!=="none"||c.perspective!=="none"||c.contain==="paint"||["transform","perspective"].indexOf(c.willChange)!==-1||r&&c.willChange==="filter"||r&&c.filter&&c.filter!=="none")return u;u=u.parentNode}return null}function Ol(n){for(var r=An(n),o=iv(n);o&&qE(o)&&Na(o).position==="static";)o=iv(o);return o&&(ua(o)==="html"||ua(o)==="body"&&Na(o).position==="static")?r:o||PE(n)||r}function dp(n){return["top","bottom"].indexOf(n)>=0?"x":"y"}function il(n,r,o){return jr(n,mu(r,o))}function GE(n,r,o){var l=il(n,r,o);return l>o?o:l}function P0(){return{top:0,right:0,bottom:0,left:0}}function G0(n){return Object.assign({},P0(),n)}function V0(n,r){return r.reduce(function(o,l){return o[l]=n,o},{})}var VE=function(r,o){return r=typeof r=="function"?r(Object.assign({},o.rects,{placement:o.placement})):r,G0(typeof r!="number"?r:V0(r,Al))};function YE(n){var r,o=n.state,l=n.name,u=n.options,c=o.elements.arrow,d=o.modifiersData.popperOffsets,p=sa(o.placement),h=dp(p),m=[pn,Gn].indexOf(p)>=0,y=m?"height":"width";if(!(!c||!d)){var S=VE(u.padding,o),E=fp(c),R=h==="y"?dn:pn,x=h==="y"?Pn:Gn,C=o.rects.reference[y]+o.rects.reference[h]-d[h]-o.rects.popper[y],D=d[h]-o.rects.reference[h],j=Ol(c),U=j?h==="y"?j.clientHeight||0:j.clientWidth||0:0,M=C/2-D/2,O=S[R],A=U-E[y]-S[x],$=U/2-E[y]/2+M,H=il(O,$,A),P=h;o.modifiersData[l]=(r={},r[P]=H,r.centerOffset=H-$,r)}}function XE(n){var r=n.state,o=n.options,l=o.element,u=l===void 0?"[data-popper-arrow]":l;u!=null&&(typeof u=="string"&&(u=r.elements.popper.querySelector(u),!u)||q0(r.elements.popper,u)&&(r.elements.arrow=u))}const IE={name:"arrow",enabled:!0,phase:"main",fn:YE,effect:XE,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Lo(n){return n.split("-")[1]}var WE={top:"auto",right:"auto",bottom:"auto",left:"auto"};function KE(n,r){var o=n.x,l=n.y,u=r.devicePixelRatio||1;return{x:_o(o*u)/u||0,y:_o(l*u)/u||0}}function lv(n){var r,o=n.popper,l=n.popperRect,u=n.placement,c=n.variation,d=n.offsets,p=n.position,h=n.gpuAcceleration,m=n.adaptive,y=n.roundOffsets,S=n.isFixed,E=d.x,R=E===void 0?0:E,x=d.y,C=x===void 0?0:x,D=typeof y=="function"?y({x:R,y:C}):{x:R,y:C};R=D.x,C=D.y;var j=d.hasOwnProperty("x"),U=d.hasOwnProperty("y"),M=pn,O=dn,A=window;if(m){var $=Ol(o),H="clientHeight",P="clientWidth";if($===An(o)&&($=sr(o),Na($).position!=="static"&&p==="absolute"&&(H="scrollHeight",P="scrollWidth")),$=$,u===dn||(u===pn||u===Gn)&&c===ml){O=Pn;var K=S&&$===A&&A.visualViewport?A.visualViewport.height:$[H];C-=K-l.height,C*=h?1:-1}if(u===pn||(u===dn||u===Pn)&&c===ml){M=Gn;var v=S&&$===A&&A.visualViewport?A.visualViewport.width:$[P];R-=v-l.width,R*=h?1:-1}}var _=Object.assign({position:p},m&&WE),W=y===!0?KE({x:R,y:C},An(o)):{x:R,y:C};if(R=W.x,C=W.y,h){var Z;return Object.assign({},_,(Z={},Z[O]=U?"0":"",Z[M]=j?"0":"",Z.transform=(A.devicePixelRatio||1)<=1?"translate("+R+"px, "+C+"px)":"translate3d("+R+"px, "+C+"px, 0)",Z))}return Object.assign({},_,(r={},r[O]=U?C+"px":"",r[M]=j?R+"px":"",r.transform="",r))}function QE(n){var r=n.state,o=n.options,l=o.gpuAcceleration,u=l===void 0?!0:l,c=o.adaptive,d=c===void 0?!0:c,p=o.roundOffsets,h=p===void 0?!0:p,m={placement:sa(r.placement),variation:Lo(r.placement),popper:r.elements.popper,popperRect:r.rects.popper,gpuAcceleration:u,isFixed:r.options.strategy==="fixed"};r.modifiersData.popperOffsets!=null&&(r.styles.popper=Object.assign({},r.styles.popper,lv(Object.assign({},m,{offsets:r.modifiersData.popperOffsets,position:r.options.strategy,adaptive:d,roundOffsets:h})))),r.modifiersData.arrow!=null&&(r.styles.arrow=Object.assign({},r.styles.arrow,lv(Object.assign({},m,{offsets:r.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:h})))),r.attributes.popper=Object.assign({},r.attributes.popper,{"data-popper-placement":r.placement})}const ZE={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:QE,data:{}};var Ws={passive:!0};function FE(n){var r=n.state,o=n.instance,l=n.options,u=l.scroll,c=u===void 0?!0:u,d=l.resize,p=d===void 0?!0:d,h=An(r.elements.popper),m=[].concat(r.scrollParents.reference,r.scrollParents.popper);return c&&m.forEach(function(y){y.addEventListener("scroll",o.update,Ws)}),p&&h.addEventListener("resize",o.update,Ws),function(){c&&m.forEach(function(y){y.removeEventListener("scroll",o.update,Ws)}),p&&h.removeEventListener("resize",o.update,Ws)}}const JE={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:FE,data:{}};var tR={left:"right",right:"left",bottom:"top",top:"bottom"};function iu(n){return n.replace(/left|right|bottom|top/g,function(r){return tR[r]})}var eR={start:"end",end:"start"};function sv(n){return n.replace(/start|end/g,function(r){return eR[r]})}function pp(n){var r=An(n),o=r.pageXOffset,l=r.pageYOffset;return{scrollLeft:o,scrollTop:l}}function hp(n){return Uo(sr(n)).left+pp(n).scrollLeft}function nR(n,r){var o=An(n),l=sr(n),u=o.visualViewport,c=l.clientWidth,d=l.clientHeight,p=0,h=0;if(u){c=u.width,d=u.height;var m=H0();(m||!m&&r==="fixed")&&(p=u.offsetLeft,h=u.offsetTop)}return{width:c,height:d,x:p+hp(n),y:h}}function aR(n){var r,o=sr(n),l=pp(n),u=(r=n.ownerDocument)==null?void 0:r.body,c=jr(o.scrollWidth,o.clientWidth,u?u.scrollWidth:0,u?u.clientWidth:0),d=jr(o.scrollHeight,o.clientHeight,u?u.scrollHeight:0,u?u.clientHeight:0),p=-l.scrollLeft+hp(n),h=-l.scrollTop;return Na(u||o).direction==="rtl"&&(p+=jr(o.clientWidth,u?u.clientWidth:0)-c),{width:c,height:d,x:p,y:h}}function mp(n){var r=Na(n),o=r.overflow,l=r.overflowX,u=r.overflowY;return/auto|scroll|overlay|hidden/.test(o+u+l)}function Y0(n){return["html","body","#document"].indexOf(ua(n))>=0?n.ownerDocument.body:qn(n)&&mp(n)?n:Y0(Bu(n))}function ll(n,r){var o;r===void 0&&(r=[]);var l=Y0(n),u=l===((o=n.ownerDocument)==null?void 0:o.body),c=An(l),d=u?[c].concat(c.visualViewport||[],mp(l)?l:[]):l,p=r.concat(d);return u?p:p.concat(ll(Bu(d)))}function Md(n){return Object.assign({},n,{left:n.x,top:n.y,right:n.x+n.width,bottom:n.y+n.height})}function rR(n,r){var o=Uo(n,!1,r==="fixed");return o.top=o.top+n.clientTop,o.left=o.left+n.clientLeft,o.bottom=o.top+n.clientHeight,o.right=o.left+n.clientWidth,o.width=n.clientWidth,o.height=n.clientHeight,o.x=o.left,o.y=o.top,o}function uv(n,r,o){return r===U0?Md(nR(n,o)):kr(r)?rR(r,o):Md(aR(sr(n)))}function oR(n){var r=ll(Bu(n)),o=["absolute","fixed"].indexOf(Na(n).position)>=0,l=o&&qn(n)?Ol(n):n;return kr(l)?r.filter(function(u){return kr(u)&&q0(u,l)&&ua(u)!=="body"}):[]}function iR(n,r,o,l){var u=r==="clippingParents"?oR(n):[].concat(r),c=[].concat(u,[o]),d=c[0],p=c.reduce(function(h,m){var y=uv(n,m,l);return h.top=jr(y.top,h.top),h.right=mu(y.right,h.right),h.bottom=mu(y.bottom,h.bottom),h.left=jr(y.left,h.left),h},uv(n,d,l));return p.width=p.right-p.left,p.height=p.bottom-p.top,p.x=p.left,p.y=p.top,p}function X0(n){var r=n.reference,o=n.element,l=n.placement,u=l?sa(l):null,c=l?Lo(l):null,d=r.x+r.width/2-o.width/2,p=r.y+r.height/2-o.height/2,h;switch(u){case dn:h={x:d,y:r.y-o.height};break;case Pn:h={x:d,y:r.y+r.height};break;case Gn:h={x:r.x+r.width,y:p};break;case pn:h={x:r.x-o.width,y:p};break;default:h={x:r.x,y:r.y}}var m=u?dp(u):null;if(m!=null){var y=m==="y"?"height":"width";switch(c){case $o:h[m]=h[m]-(r[y]/2-o[y]/2);break;case ml:h[m]=h[m]+(r[y]/2-o[y]/2);break}}return h}function gl(n,r){r===void 0&&(r={});var o=r,l=o.placement,u=l===void 0?n.placement:l,c=o.strategy,d=c===void 0?n.strategy:c,p=o.boundary,h=p===void 0?wE:p,m=o.rootBoundary,y=m===void 0?U0:m,S=o.elementContext,E=S===void 0?Ki:S,R=o.altBoundary,x=R===void 0?!1:R,C=o.padding,D=C===void 0?0:C,j=G0(typeof D!="number"?D:V0(D,Al)),U=E===Ki?AE:Ki,M=n.rects.popper,O=n.elements[x?U:E],A=iR(kr(O)?O:O.contextElement||sr(n.elements.popper),h,y,d),$=Uo(n.elements.reference),H=X0({reference:$,element:M,placement:u}),P=Md(Object.assign({},M,H)),K=E===Ki?P:$,v={top:A.top-K.top+j.top,bottom:K.bottom-A.bottom+j.bottom,left:A.left-K.left+j.left,right:K.right-A.right+j.right},_=n.modifiersData.offset;if(E===Ki&&_){var W=_[u];Object.keys(v).forEach(function(Z){var it=[Gn,Pn].indexOf(Z)>=0?1:-1,F=[dn,Pn].indexOf(Z)>=0?"y":"x";v[Z]+=W[F]*it})}return v}function lR(n,r){r===void 0&&(r={});var o=r,l=o.placement,u=o.boundary,c=o.rootBoundary,d=o.padding,p=o.flipVariations,h=o.allowedAutoPlacements,m=h===void 0?L0:h,y=Lo(l),S=y?p?ov:ov.filter(function(x){return Lo(x)===y}):Al,E=S.filter(function(x){return m.indexOf(x)>=0});E.length===0&&(E=S);var R=E.reduce(function(x,C){return x[C]=gl(n,{placement:C,boundary:u,rootBoundary:c,padding:d})[sa(C)],x},{});return Object.keys(R).sort(function(x,C){return R[x]-R[C]})}function sR(n){if(sa(n)===up)return[];var r=iu(n);return[sv(n),r,sv(r)]}function uR(n){var r=n.state,o=n.options,l=n.name;if(!r.modifiersData[l]._skip){for(var u=o.mainAxis,c=u===void 0?!0:u,d=o.altAxis,p=d===void 0?!0:d,h=o.fallbackPlacements,m=o.padding,y=o.boundary,S=o.rootBoundary,E=o.altBoundary,R=o.flipVariations,x=R===void 0?!0:R,C=o.allowedAutoPlacements,D=r.options.placement,j=sa(D),U=j===D,M=h||(U||!x?[iu(D)]:sR(D)),O=[D].concat(M).reduce(function(lt,ct){return lt.concat(sa(ct)===up?lR(r,{placement:ct,boundary:y,rootBoundary:S,padding:m,flipVariations:x,allowedAutoPlacements:C}):ct)},[]),A=r.rects.reference,$=r.rects.popper,H=new Map,P=!0,K=O[0],v=0;v<O.length;v++){var _=O[v],W=sa(_),Z=Lo(_)===$o,it=[dn,Pn].indexOf(W)>=0,F=it?"width":"height",B=gl(r,{placement:_,boundary:y,rootBoundary:S,altBoundary:E,padding:m}),V=it?Z?Gn:pn:Z?Pn:dn;A[F]>$[F]&&(V=iu(V));var ot=iu(V),Q=[];if(c&&Q.push(B[W]<=0),p&&Q.push(B[V]<=0,B[ot]<=0),Q.every(function(lt){return lt})){K=_,P=!1;break}H.set(_,Q)}if(P)for(var z=x?3:1,X=function(ct){var ft=O.find(function(Tt){var bt=H.get(Tt);if(bt)return bt.slice(0,ct).every(function(At){return At})});if(ft)return K=ft,"break"},et=z;et>0;et--){var nt=X(et);if(nt==="break")break}r.placement!==K&&(r.modifiersData[l]._skip=!0,r.placement=K,r.reset=!0)}}const cR={name:"flip",enabled:!0,phase:"main",fn:uR,requiresIfExists:["offset"],data:{_skip:!1}};function cv(n,r,o){return o===void 0&&(o={x:0,y:0}),{top:n.top-r.height-o.y,right:n.right-r.width+o.x,bottom:n.bottom-r.height+o.y,left:n.left-r.width-o.x}}function fv(n){return[dn,Gn,Pn,pn].some(function(r){return n[r]>=0})}function fR(n){var r=n.state,o=n.name,l=r.rects.reference,u=r.rects.popper,c=r.modifiersData.preventOverflow,d=gl(r,{elementContext:"reference"}),p=gl(r,{altBoundary:!0}),h=cv(d,l),m=cv(p,u,c),y=fv(h),S=fv(m);r.modifiersData[o]={referenceClippingOffsets:h,popperEscapeOffsets:m,isReferenceHidden:y,hasPopperEscaped:S},r.attributes.popper=Object.assign({},r.attributes.popper,{"data-popper-reference-hidden":y,"data-popper-escaped":S})}const dR={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:fR};function pR(n,r,o){var l=sa(n),u=[pn,dn].indexOf(l)>=0?-1:1,c=typeof o=="function"?o(Object.assign({},r,{placement:n})):o,d=c[0],p=c[1];return d=d||0,p=(p||0)*u,[pn,Gn].indexOf(l)>=0?{x:p,y:d}:{x:d,y:p}}function hR(n){var r=n.state,o=n.options,l=n.name,u=o.offset,c=u===void 0?[0,0]:u,d=L0.reduce(function(y,S){return y[S]=pR(S,r.rects,c),y},{}),p=d[r.placement],h=p.x,m=p.y;r.modifiersData.popperOffsets!=null&&(r.modifiersData.popperOffsets.x+=h,r.modifiersData.popperOffsets.y+=m),r.modifiersData[l]=d}const mR={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:hR};function gR(n){var r=n.state,o=n.name;r.modifiersData[o]=X0({reference:r.rects.reference,element:r.rects.popper,placement:r.placement})}const yR={name:"popperOffsets",enabled:!0,phase:"read",fn:gR,data:{}};function vR(n){return n==="x"?"y":"x"}function bR(n){var r=n.state,o=n.options,l=n.name,u=o.mainAxis,c=u===void 0?!0:u,d=o.altAxis,p=d===void 0?!1:d,h=o.boundary,m=o.rootBoundary,y=o.altBoundary,S=o.padding,E=o.tether,R=E===void 0?!0:E,x=o.tetherOffset,C=x===void 0?0:x,D=gl(r,{boundary:h,rootBoundary:m,padding:S,altBoundary:y}),j=sa(r.placement),U=Lo(r.placement),M=!U,O=dp(j),A=vR(O),$=r.modifiersData.popperOffsets,H=r.rects.reference,P=r.rects.popper,K=typeof C=="function"?C(Object.assign({},r.rects,{placement:r.placement})):C,v=typeof K=="number"?{mainAxis:K,altAxis:K}:Object.assign({mainAxis:0,altAxis:0},K),_=r.modifiersData.offset?r.modifiersData.offset[r.placement]:null,W={x:0,y:0};if($){if(c){var Z,it=O==="y"?dn:pn,F=O==="y"?Pn:Gn,B=O==="y"?"height":"width",V=$[O],ot=V+D[it],Q=V-D[F],z=R?-P[B]/2:0,X=U===$o?H[B]:P[B],et=U===$o?-P[B]:-H[B],nt=r.elements.arrow,lt=R&&nt?fp(nt):{width:0,height:0},ct=r.modifiersData["arrow#persistent"]?r.modifiersData["arrow#persistent"].padding:P0(),ft=ct[it],Tt=ct[F],bt=il(0,H[B],lt[B]),At=M?H[B]/2-z-bt-ft-v.mainAxis:X-bt-ft-v.mainAxis,yt=M?-H[B]/2+z+bt+Tt+v.mainAxis:et+bt+Tt+v.mainAxis,wt=r.elements.arrow&&Ol(r.elements.arrow),St=wt?O==="y"?wt.clientTop||0:wt.clientLeft||0:0,$t=(Z=_==null?void 0:_[O])!=null?Z:0,Et=V+At-$t-St,Vt=V+yt-$t,Te=il(R?mu(ot,Et):ot,V,R?jr(Q,Vt):Q);$[O]=Te,W[O]=Te-V}if(p){var Lt,Kt=O==="x"?dn:pn,Qt=O==="x"?Pn:Gn,Yt=$[A],_t=A==="y"?"height":"width",dt=Yt+D[Kt],Ue=Yt-D[Qt],le=[dn,pn].indexOf(j)!==-1,Ze=(Lt=_==null?void 0:_[A])!=null?Lt:0,Le=le?dt:Yt-H[_t]-P[_t]-Ze+v.altAxis,ne=le?Yt+H[_t]+P[_t]-Ze-v.altAxis:Ue,Ee=R&&le?GE(Le,Yt,ne):il(R?Le:dt,Yt,R?ne:Ue);$[A]=Ee,W[A]=Ee-Yt}r.modifiersData[l]=W}}const SR={name:"preventOverflow",enabled:!0,phase:"main",fn:bR,requiresIfExists:["offset"]};function xR(n){return{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop}}function CR(n){return n===An(n)||!qn(n)?pp(n):xR(n)}function TR(n){var r=n.getBoundingClientRect(),o=_o(r.width)/n.offsetWidth||1,l=_o(r.height)/n.offsetHeight||1;return o!==1||l!==1}function ER(n,r,o){o===void 0&&(o=!1);var l=qn(r),u=qn(r)&&TR(r),c=sr(r),d=Uo(n,u,o),p={scrollLeft:0,scrollTop:0},h={x:0,y:0};return(l||!l&&!o)&&((ua(r)!=="body"||mp(c))&&(p=CR(r)),qn(r)?(h=Uo(r,!0),h.x+=r.clientLeft,h.y+=r.clientTop):c&&(h.x=hp(c))),{x:d.left+p.scrollLeft-h.x,y:d.top+p.scrollTop-h.y,width:d.width,height:d.height}}function RR(n){var r=new Map,o=new Set,l=[];n.forEach(function(c){r.set(c.name,c)});function u(c){o.add(c.name);var d=[].concat(c.requires||[],c.requiresIfExists||[]);d.forEach(function(p){if(!o.has(p)){var h=r.get(p);h&&u(h)}}),l.push(c)}return n.forEach(function(c){o.has(c.name)||u(c)}),l}function wR(n){var r=RR(n);return _E.reduce(function(o,l){return o.concat(r.filter(function(u){return u.phase===l}))},[])}function AR(n){var r;return function(){return r||(r=new Promise(function(o){Promise.resolve().then(function(){r=void 0,o(n())})})),r}}function OR(n){var r=n.reduce(function(o,l){var u=o[l.name];return o[l.name]=u?Object.assign({},u,l,{options:Object.assign({},u.options,l.options),data:Object.assign({},u.data,l.data)}):l,o},{});return Object.keys(r).map(function(o){return r[o]})}var dv={placement:"bottom",modifiers:[],strategy:"absolute"};function pv(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return!r.some(function(l){return!(l&&typeof l.getBoundingClientRect=="function")})}function MR(n){n===void 0&&(n={});var r=n,o=r.defaultModifiers,l=o===void 0?[]:o,u=r.defaultOptions,c=u===void 0?dv:u;return function(p,h,m){m===void 0&&(m=c);var y={placement:"bottom",orderedModifiers:[],options:Object.assign({},dv,c),modifiersData:{},elements:{reference:p,popper:h},attributes:{},styles:{}},S=[],E=!1,R={state:y,setOptions:function(j){var U=typeof j=="function"?j(y.options):j;C(),y.options=Object.assign({},c,y.options,U),y.scrollParents={reference:kr(p)?ll(p):p.contextElement?ll(p.contextElement):[],popper:ll(h)};var M=wR(OR([].concat(l,y.options.modifiers)));return y.orderedModifiers=M.filter(function(O){return O.enabled}),x(),R.update()},forceUpdate:function(){if(!E){var j=y.elements,U=j.reference,M=j.popper;if(pv(U,M)){y.rects={reference:ER(U,Ol(M),y.options.strategy==="fixed"),popper:fp(M)},y.reset=!1,y.placement=y.options.placement,y.orderedModifiers.forEach(function(v){return y.modifiersData[v.name]=Object.assign({},v.data)});for(var O=0;O<y.orderedModifiers.length;O++){if(y.reset===!0){y.reset=!1,O=-1;continue}var A=y.orderedModifiers[O],$=A.fn,H=A.options,P=H===void 0?{}:H,K=A.name;typeof $=="function"&&(y=$({state:y,options:P,name:K,instance:R})||y)}}}},update:AR(function(){return new Promise(function(D){R.forceUpdate(),D(y)})}),destroy:function(){C(),E=!0}};if(!pv(p,h))return R;R.setOptions(m).then(function(D){!E&&m.onFirstUpdate&&m.onFirstUpdate(D)});function x(){y.orderedModifiers.forEach(function(D){var j=D.name,U=D.options,M=U===void 0?{}:U,O=D.effect;if(typeof O=="function"){var A=O({state:y,name:j,instance:R,options:M}),$=function(){};S.push(A||$)}})}function C(){S.forEach(function(D){return D()}),S=[]}return R}}var zR=[JE,yR,ZE,HE,mR,cR,SR,IE,dR],DR=MR({defaultModifiers:zR});function I0(n){var S;const{elementType:r,externalSlotProps:o,ownerState:l,skipResolvingSlotProps:u=!1,...c}=n,d=u?{}:j0(o,l),{props:p,internalRef:h}=$0({...c,externalSlotProps:d}),m=Pe(h,d==null?void 0:d.ref,(S=n.additionalProps)==null?void 0:S.ref);return N0(r,{...p,ref:m},l)}function Yo(n){var r;return parseInt(w.version,10)>=19?((r=n==null?void 0:n.props)==null?void 0:r.ref)||null:(n==null?void 0:n.ref)||null}function BR(n){return typeof n=="function"?n():n}const W0=w.forwardRef(function(r,o){const{children:l,container:u,disablePortal:c=!1}=r,[d,p]=w.useState(null),h=Pe(w.isValidElement(l)?Yo(l):null,o);if(Wn(()=>{c||p(BR(u)||document.body)},[u,c]),Wn(()=>{if(d&&!c)return Qy(o,d),()=>{Qy(o,null)}},[o,d,c]),c){if(w.isValidElement(l)){const m={ref:h};return w.cloneElement(l,m)}return l}return d&&z0.createPortal(l,d)});function NR(n){return Dt("MuiPopper",n)}Mt("MuiPopper",["root"]);function jR(n,r){if(r==="ltr")return n;switch(n){case"bottom-end":return"bottom-start";case"bottom-start":return"bottom-end";case"top-end":return"top-start";case"top-start":return"top-end";default:return n}}function zd(n){return typeof n=="function"?n():n}function kR(n){return n.nodeType!==void 0}const $R=n=>{const{classes:r}=n;return Bt({root:["root"]},NR,r)},_R={},UR=w.forwardRef(function(r,o){const{anchorEl:l,children:u,direction:c,disablePortal:d,modifiers:p,open:h,placement:m,popperOptions:y,popperRef:S,slotProps:E={},slots:R={},TransitionProps:x,ownerState:C,...D}=r,j=w.useRef(null),U=Pe(j,o),M=w.useRef(null),O=Pe(M,S),A=w.useRef(O);Wn(()=>{A.current=O},[O]),w.useImperativeHandle(S,()=>M.current,[]);const $=jR(m,c),[H,P]=w.useState($),[K,v]=w.useState(zd(l));w.useEffect(()=>{M.current&&M.current.forceUpdate()}),w.useEffect(()=>{l&&v(zd(l))},[l]),Wn(()=>{if(!K||!h)return;const F=ot=>{P(ot.placement)};let B=[{name:"preventOverflow",options:{altBoundary:d}},{name:"flip",options:{altBoundary:d}},{name:"onUpdate",enabled:!0,phase:"afterWrite",fn:({state:ot})=>{F(ot)}}];p!=null&&(B=B.concat(p)),y&&y.modifiers!=null&&(B=B.concat(y.modifiers));const V=DR(K,j.current,{placement:$,...y,modifiers:B});return A.current(V),()=>{V.destroy(),A.current(null)}},[K,d,p,h,y,$]);const _={placement:H};x!==null&&(_.TransitionProps=x);const W=$R(r),Z=R.root??"div",it=I0({elementType:Z,externalSlotProps:E.root,externalForwardedProps:D,additionalProps:{role:"tooltip",ref:U},ownerState:r,className:W.root});return T.jsx(Z,{...it,children:typeof u=="function"?u(_):u})}),LR=w.forwardRef(function(r,o){const{anchorEl:l,children:u,container:c,direction:d="ltr",disablePortal:p=!1,keepMounted:h=!1,modifiers:m,open:y,placement:S="bottom",popperOptions:E=_R,popperRef:R,style:x,transition:C=!1,slotProps:D={},slots:j={},...U}=r,[M,O]=w.useState(!0),A=()=>{O(!1)},$=()=>{O(!0)};if(!h&&!y&&(!C||M))return null;let H;if(c)H=c;else if(l){const v=zd(l);H=v&&kR(v)?wn(v).body:wn(null).body}const P=!y&&h&&(!C||M)?"none":void 0,K=C?{in:y,onEnter:A,onExited:$}:void 0;return T.jsx(W0,{disablePortal:p,container:H,children:T.jsx(UR,{anchorEl:l,direction:d,disablePortal:p,modifiers:m,ref:o,open:C?!M:y,placement:S,popperOptions:E,popperRef:R,slotProps:D,slots:j,...U,style:{position:"fixed",top:0,left:0,display:P,...x},TransitionProps:K,children:u})})}),HR=ut(LR,{name:"MuiPopper",slot:"Root"})({}),K0=w.forwardRef(function(r,o){const l=ep(),u=Nt({props:r,name:"MuiPopper"}),{anchorEl:c,component:d,components:p,componentsProps:h,container:m,disablePortal:y,keepMounted:S,modifiers:E,open:R,placement:x,popperOptions:C,popperRef:D,transition:j,slots:U,slotProps:M,...O}=u,A=(U==null?void 0:U.root)??(p==null?void 0:p.Root),$={anchorEl:c,container:m,disablePortal:y,keepMounted:S,modifiers:E,open:R,placement:x,popperOptions:C,popperRef:D,transition:j,...O};return T.jsx(HR,{as:d,direction:l?"rtl":"ltr",slots:{root:A},slotProps:M??h,...$,ref:o})}),qR=Mn(T.jsx("path",{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"}));function PR(n){return Dt("MuiChip",n)}const Pt=Mt("MuiChip",["root","sizeSmall","sizeMedium","colorDefault","colorError","colorInfo","colorPrimary","colorSecondary","colorSuccess","colorWarning","disabled","clickable","clickableColorPrimary","clickableColorSecondary","deletable","deletableColorPrimary","deletableColorSecondary","outlined","filled","outlinedPrimary","outlinedSecondary","filledPrimary","filledSecondary","avatar","avatarSmall","avatarMedium","avatarColorPrimary","avatarColorSecondary","icon","iconSmall","iconMedium","iconColorPrimary","iconColorSecondary","label","labelSmall","labelMedium","deleteIcon","deleteIconSmall","deleteIconMedium","deleteIconColorPrimary","deleteIconColorSecondary","deleteIconOutlinedColorPrimary","deleteIconOutlinedColorSecondary","deleteIconFilledColorPrimary","deleteIconFilledColorSecondary","focusVisible"]),GR=n=>{const{classes:r,disabled:o,size:l,color:u,iconColor:c,onDelete:d,clickable:p,variant:h}=n,m={root:["root",h,o&&"disabled",`size${st(l)}`,`color${st(u)}`,p&&"clickable",p&&`clickableColor${st(u)}`,d&&"deletable",d&&`deletableColor${st(u)}`,`${h}${st(u)}`],label:["label",`label${st(l)}`],avatar:["avatar",`avatar${st(l)}`,`avatarColor${st(u)}`],icon:["icon",`icon${st(l)}`,`iconColor${st(c)}`],deleteIcon:["deleteIcon",`deleteIcon${st(l)}`,`deleteIconColor${st(u)}`,`deleteIcon${st(h)}Color${st(u)}`]};return Bt(m,PR,r)},VR=ut("div",{name:"MuiChip",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n,{color:l,iconColor:u,clickable:c,onDelete:d,size:p,variant:h}=o;return[{[`& .${Pt.avatar}`]:r.avatar},{[`& .${Pt.avatar}`]:r[`avatar${st(p)}`]},{[`& .${Pt.avatar}`]:r[`avatarColor${st(l)}`]},{[`& .${Pt.icon}`]:r.icon},{[`& .${Pt.icon}`]:r[`icon${st(p)}`]},{[`& .${Pt.icon}`]:r[`iconColor${st(u)}`]},{[`& .${Pt.deleteIcon}`]:r.deleteIcon},{[`& .${Pt.deleteIcon}`]:r[`deleteIcon${st(p)}`]},{[`& .${Pt.deleteIcon}`]:r[`deleteIconColor${st(l)}`]},{[`& .${Pt.deleteIcon}`]:r[`deleteIcon${st(h)}Color${st(l)}`]},r.root,r[`size${st(p)}`],r[`color${st(l)}`],c&&r.clickable,c&&l!=="default"&&r[`clickableColor${st(l)})`],d&&r.deletable,d&&l!=="default"&&r[`deletableColor${st(l)}`],r[h],r[`${h}${st(l)}`]]}})(Gt(({theme:n})=>{const r=n.palette.mode==="light"?n.palette.grey[700]:n.palette.grey[300];return{maxWidth:"100%",fontFamily:n.typography.fontFamily,fontSize:n.typography.pxToRem(13),display:"inline-flex",alignItems:"center",justifyContent:"center",height:32,color:(n.vars||n).palette.text.primary,backgroundColor:(n.vars||n).palette.action.selected,borderRadius:32/2,whiteSpace:"nowrap",transition:n.transitions.create(["background-color","box-shadow"]),cursor:"unset",outline:0,textDecoration:"none",border:0,padding:0,verticalAlign:"middle",boxSizing:"border-box",[`&.${Pt.disabled}`]:{opacity:(n.vars||n).palette.action.disabledOpacity,pointerEvents:"none"},[`& .${Pt.avatar}`]:{marginLeft:5,marginRight:-6,width:24,height:24,color:n.vars?n.vars.palette.Chip.defaultAvatarColor:r,fontSize:n.typography.pxToRem(12)},[`& .${Pt.avatarColorPrimary}`]:{color:(n.vars||n).palette.primary.contrastText,backgroundColor:(n.vars||n).palette.primary.dark},[`& .${Pt.avatarColorSecondary}`]:{color:(n.vars||n).palette.secondary.contrastText,backgroundColor:(n.vars||n).palette.secondary.dark},[`& .${Pt.avatarSmall}`]:{marginLeft:4,marginRight:-4,width:18,height:18,fontSize:n.typography.pxToRem(10)},[`& .${Pt.icon}`]:{marginLeft:5,marginRight:-6},[`& .${Pt.deleteIcon}`]:{WebkitTapHighlightColor:"transparent",color:n.vars?`rgba(${n.vars.palette.text.primaryChannel} / 0.26)`:ye(n.palette.text.primary,.26),fontSize:22,cursor:"pointer",margin:"0 5px 0 -6px","&:hover":{color:n.vars?`rgba(${n.vars.palette.text.primaryChannel} / 0.4)`:ye(n.palette.text.primary,.4)}},variants:[{props:{size:"small"},style:{height:24,[`& .${Pt.icon}`]:{fontSize:18,marginLeft:4,marginRight:-4},[`& .${Pt.deleteIcon}`]:{fontSize:16,marginRight:4,marginLeft:-4}}},...Object.entries(n.palette).filter(rn(["contrastText"])).map(([o])=>({props:{color:o},style:{backgroundColor:(n.vars||n).palette[o].main,color:(n.vars||n).palette[o].contrastText,[`& .${Pt.deleteIcon}`]:{color:n.vars?`rgba(${n.vars.palette[o].contrastTextChannel} / 0.7)`:ye(n.palette[o].contrastText,.7),"&:hover, &:active":{color:(n.vars||n).palette[o].contrastText}}}})),{props:o=>o.iconColor===o.color,style:{[`& .${Pt.icon}`]:{color:n.vars?n.vars.palette.Chip.defaultIconColor:r}}},{props:o=>o.iconColor===o.color&&o.color!=="default",style:{[`& .${Pt.icon}`]:{color:"inherit"}}},{props:{onDelete:!0},style:{[`&.${Pt.focusVisible}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.action.selectedChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.focusOpacity}))`:ye(n.palette.action.selected,n.palette.action.selectedOpacity+n.palette.action.focusOpacity)}}},...Object.entries(n.palette).filter(rn(["dark"])).map(([o])=>({props:{color:o,onDelete:!0},style:{[`&.${Pt.focusVisible}`]:{background:(n.vars||n).palette[o].dark}}})),{props:{clickable:!0},style:{userSelect:"none",WebkitTapHighlightColor:"transparent",cursor:"pointer","&:hover":{backgroundColor:n.vars?`rgba(${n.vars.palette.action.selectedChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.hoverOpacity}))`:ye(n.palette.action.selected,n.palette.action.selectedOpacity+n.palette.action.hoverOpacity)},[`&.${Pt.focusVisible}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.action.selectedChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.focusOpacity}))`:ye(n.palette.action.selected,n.palette.action.selectedOpacity+n.palette.action.focusOpacity)},"&:active":{boxShadow:(n.vars||n).shadows[1]}}},...Object.entries(n.palette).filter(rn(["dark"])).map(([o])=>({props:{color:o,clickable:!0},style:{[`&:hover, &.${Pt.focusVisible}`]:{backgroundColor:(n.vars||n).palette[o].dark}}})),{props:{variant:"outlined"},style:{backgroundColor:"transparent",border:n.vars?`1px solid ${n.vars.palette.Chip.defaultBorder}`:`1px solid ${n.palette.mode==="light"?n.palette.grey[400]:n.palette.grey[700]}`,[`&.${Pt.clickable}:hover`]:{backgroundColor:(n.vars||n).palette.action.hover},[`&.${Pt.focusVisible}`]:{backgroundColor:(n.vars||n).palette.action.focus},[`& .${Pt.avatar}`]:{marginLeft:4},[`& .${Pt.avatarSmall}`]:{marginLeft:2},[`& .${Pt.icon}`]:{marginLeft:4},[`& .${Pt.iconSmall}`]:{marginLeft:2},[`& .${Pt.deleteIcon}`]:{marginRight:5},[`& .${Pt.deleteIconSmall}`]:{marginRight:3}}},...Object.entries(n.palette).filter(rn()).map(([o])=>({props:{variant:"outlined",color:o},style:{color:(n.vars||n).palette[o].main,border:`1px solid ${n.vars?`rgba(${n.vars.palette[o].mainChannel} / 0.7)`:ye(n.palette[o].main,.7)}`,[`&.${Pt.clickable}:hover`]:{backgroundColor:n.vars?`rgba(${n.vars.palette[o].mainChannel} / ${n.vars.palette.action.hoverOpacity})`:ye(n.palette[o].main,n.palette.action.hoverOpacity)},[`&.${Pt.focusVisible}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette[o].mainChannel} / ${n.vars.palette.action.focusOpacity})`:ye(n.palette[o].main,n.palette.action.focusOpacity)},[`& .${Pt.deleteIcon}`]:{color:n.vars?`rgba(${n.vars.palette[o].mainChannel} / 0.7)`:ye(n.palette[o].main,.7),"&:hover, &:active":{color:(n.vars||n).palette[o].main}}}}))]}})),YR=ut("span",{name:"MuiChip",slot:"Label",overridesResolver:(n,r)=>{const{ownerState:o}=n,{size:l}=o;return[r.label,r[`label${st(l)}`]]}})({overflow:"hidden",textOverflow:"ellipsis",paddingLeft:12,paddingRight:12,whiteSpace:"nowrap",variants:[{props:{variant:"outlined"},style:{paddingLeft:11,paddingRight:11}},{props:{size:"small"},style:{paddingLeft:8,paddingRight:8}},{props:{size:"small",variant:"outlined"},style:{paddingLeft:7,paddingRight:7}}]});function hv(n){return n.key==="Backspace"||n.key==="Delete"}const Nr=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiChip"}),{avatar:u,className:c,clickable:d,color:p="default",component:h,deleteIcon:m,disabled:y=!1,icon:S,label:E,onClick:R,onDelete:x,onKeyDown:C,onKeyUp:D,size:j="medium",variant:U="filled",tabIndex:M,skipFocusWhenDisabled:O=!1,...A}=l,$=w.useRef(null),H=Pe($,o),P=Q=>{Q.stopPropagation(),x&&x(Q)},K=Q=>{Q.currentTarget===Q.target&&hv(Q)&&Q.preventDefault(),C&&C(Q)},v=Q=>{Q.currentTarget===Q.target&&x&&hv(Q)&&x(Q),D&&D(Q)},_=d!==!1&&R?!0:d,W=_||x?ko:h||"div",Z={...l,component:W,disabled:y,size:j,color:p,iconColor:w.isValidElement(S)&&S.props.color||p,onDelete:!!x,clickable:_,variant:U},it=GR(Z),F=W===ko?{component:h||"div",focusVisibleClassName:it.focusVisible,...x&&{disableRipple:!0}}:{};let B=null;x&&(B=m&&w.isValidElement(m)?w.cloneElement(m,{className:ht(m.props.className,it.deleteIcon),onClick:P}):T.jsx(qR,{className:it.deleteIcon,onClick:P}));let V=null;u&&w.isValidElement(u)&&(V=w.cloneElement(u,{className:ht(it.avatar,u.props.className)}));let ot=null;return S&&w.isValidElement(S)&&(ot=w.cloneElement(S,{className:ht(it.icon,S.props.className)})),T.jsxs(VR,{as:W,className:ht(it.root,c),disabled:_&&y?!0:void 0,onClick:R,onKeyDown:K,onKeyUp:v,ref:H,tabIndex:O&&y?-1:M,ownerState:Z,...F,...A,children:[V||ot,T.jsx(YR,{className:it.label,ownerState:Z,children:E}),B]})});function Ks(n){return parseInt(n,10)||0}const XR={shadow:{visibility:"hidden",position:"absolute",overflow:"hidden",height:0,top:0,left:0,transform:"translateZ(0)"}};function IR(n){for(const r in n)return!1;return!0}function mv(n){return IR(n)||n.outerHeightStyle===0&&!n.overflowing}const WR=w.forwardRef(function(r,o){const{onChange:l,maxRows:u,minRows:c=1,style:d,value:p,...h}=r,{current:m}=w.useRef(p!=null),y=w.useRef(null),S=Pe(o,y),E=w.useRef(null),R=w.useRef(null),x=w.useCallback(()=>{const M=y.current,O=R.current;if(!M||!O)return;const $=Ba(M).getComputedStyle(M);if($.width==="0px")return{outerHeightStyle:0,overflowing:!1};O.style.width=$.width,O.value=M.value||r.placeholder||"x",O.value.slice(-1)===`
`&&(O.value+=" ");const H=$.boxSizing,P=Ks($.paddingBottom)+Ks($.paddingTop),K=Ks($.borderBottomWidth)+Ks($.borderTopWidth),v=O.scrollHeight;O.value="x";const _=O.scrollHeight;let W=v;c&&(W=Math.max(Number(c)*_,W)),u&&(W=Math.min(Number(u)*_,W)),W=Math.max(W,_);const Z=W+(H==="border-box"?P+K:0),it=Math.abs(W-v)<=1;return{outerHeightStyle:Z,overflowing:it}},[u,c,r.placeholder]),C=Ma(()=>{const M=y.current,O=x();if(!M||!O||mv(O))return!1;const A=O.outerHeightStyle;return E.current!=null&&E.current!==A}),D=w.useCallback(()=>{const M=y.current,O=x();if(!M||!O||mv(O))return;const A=O.outerHeightStyle;E.current!==A&&(E.current=A,M.style.height=`${A}px`),M.style.overflow=O.overflowing?"hidden":""},[x]),j=w.useRef(-1);Wn(()=>{const M=A0(D),O=y==null?void 0:y.current;if(!O)return;const A=Ba(O);A.addEventListener("resize",M);let $;return typeof ResizeObserver<"u"&&($=new ResizeObserver(()=>{C()&&($.unobserve(O),cancelAnimationFrame(j.current),D(),j.current=requestAnimationFrame(()=>{$.observe(O)}))}),$.observe(O)),()=>{M.clear(),cancelAnimationFrame(j.current),A.removeEventListener("resize",M),$&&$.disconnect()}},[x,D,C]),Wn(()=>{D()});const U=M=>{m||D();const O=M.target,A=O.value.length,$=O.value.endsWith(`
`),H=O.selectionStart===A;$&&H&&O.setSelectionRange(A,A),l&&l(M)};return T.jsxs(w.Fragment,{children:[T.jsx("textarea",{value:p,onChange:U,ref:S,rows:c,style:d,...h}),T.jsx("textarea",{"aria-hidden":!0,className:r.className,readOnly:!0,ref:R,tabIndex:-1,style:{...XR.shadow,...d,paddingTop:0,paddingBottom:0}})]})});function Dd(n){return typeof n=="string"}function Xo({props:n,states:r,muiFormControl:o}){return r.reduce((l,u)=>(l[u]=n[u],o&&typeof n[u]>"u"&&(l[u]=o[u]),l),{})}const gp=w.createContext(void 0);function Io(){return w.useContext(gp)}function gv(n){return n!=null&&!(Array.isArray(n)&&n.length===0)}function gu(n,r=!1){return n&&(gv(n.value)&&n.value!==""||r&&gv(n.defaultValue)&&n.defaultValue!=="")}function KR(n){return n.startAdornment}function QR(n){return Dt("MuiInputBase",n)}const Ho=Mt("MuiInputBase",["root","formControl","focused","disabled","adornedStart","adornedEnd","error","sizeSmall","multiline","colorSecondary","fullWidth","hiddenLabel","readOnly","input","inputSizeSmall","inputMultiline","inputTypeSearch","inputAdornedStart","inputAdornedEnd","inputHiddenLabel"]);var yv;const Nu=(n,r)=>{const{ownerState:o}=n;return[r.root,o.formControl&&r.formControl,o.startAdornment&&r.adornedStart,o.endAdornment&&r.adornedEnd,o.error&&r.error,o.size==="small"&&r.sizeSmall,o.multiline&&r.multiline,o.color&&r[`color${st(o.color)}`],o.fullWidth&&r.fullWidth,o.hiddenLabel&&r.hiddenLabel]},ju=(n,r)=>{const{ownerState:o}=n;return[r.input,o.size==="small"&&r.inputSizeSmall,o.multiline&&r.inputMultiline,o.type==="search"&&r.inputTypeSearch,o.startAdornment&&r.inputAdornedStart,o.endAdornment&&r.inputAdornedEnd,o.hiddenLabel&&r.inputHiddenLabel]},ZR=n=>{const{classes:r,color:o,disabled:l,error:u,endAdornment:c,focused:d,formControl:p,fullWidth:h,hiddenLabel:m,multiline:y,readOnly:S,size:E,startAdornment:R,type:x}=n,C={root:["root",`color${st(o)}`,l&&"disabled",u&&"error",h&&"fullWidth",d&&"focused",p&&"formControl",E&&E!=="medium"&&`size${st(E)}`,y&&"multiline",R&&"adornedStart",c&&"adornedEnd",m&&"hiddenLabel",S&&"readOnly"],input:["input",l&&"disabled",x==="search"&&"inputTypeSearch",y&&"inputMultiline",E==="small"&&"inputSizeSmall",m&&"inputHiddenLabel",R&&"inputAdornedStart",c&&"inputAdornedEnd",S&&"readOnly"]};return Bt(C,QR,r)},ku=ut("div",{name:"MuiInputBase",slot:"Root",overridesResolver:Nu})(Gt(({theme:n})=>({...n.typography.body1,color:(n.vars||n).palette.text.primary,lineHeight:"1.4375em",boxSizing:"border-box",position:"relative",cursor:"text",display:"inline-flex",alignItems:"center",[`&.${Ho.disabled}`]:{color:(n.vars||n).palette.text.disabled,cursor:"default"},variants:[{props:({ownerState:r})=>r.multiline,style:{padding:"4px 0 5px"}},{props:({ownerState:r,size:o})=>r.multiline&&o==="small",style:{paddingTop:1}},{props:({ownerState:r})=>r.fullWidth,style:{width:"100%"}}]}))),$u=ut("input",{name:"MuiInputBase",slot:"Input",overridesResolver:ju})(Gt(({theme:n})=>{const r=n.palette.mode==="light",o={color:"currentColor",...n.vars?{opacity:n.vars.opacity.inputPlaceholder}:{opacity:r?.42:.5},transition:n.transitions.create("opacity",{duration:n.transitions.duration.shorter})},l={opacity:"0 !important"},u=n.vars?{opacity:n.vars.opacity.inputPlaceholder}:{opacity:r?.42:.5};return{font:"inherit",letterSpacing:"inherit",color:"currentColor",padding:"4px 0 5px",border:0,boxSizing:"content-box",background:"none",height:"1.4375em",margin:0,WebkitTapHighlightColor:"transparent",display:"block",minWidth:0,width:"100%","&::-webkit-input-placeholder":o,"&::-moz-placeholder":o,"&::-ms-input-placeholder":o,"&:focus":{outline:0},"&:invalid":{boxShadow:"none"},"&::-webkit-search-decoration":{WebkitAppearance:"none"},[`label[data-shrink=false] + .${Ho.formControl} &`]:{"&::-webkit-input-placeholder":l,"&::-moz-placeholder":l,"&::-ms-input-placeholder":l,"&:focus::-webkit-input-placeholder":u,"&:focus::-moz-placeholder":u,"&:focus::-ms-input-placeholder":u},[`&.${Ho.disabled}`]:{opacity:1,WebkitTextFillColor:(n.vars||n).palette.text.disabled},variants:[{props:({ownerState:c})=>!c.disableInjectingGlobalStyles,style:{animationName:"mui-auto-fill-cancel",animationDuration:"10ms","&:-webkit-autofill":{animationDuration:"5000s",animationName:"mui-auto-fill"}}},{props:{size:"small"},style:{paddingTop:1}},{props:({ownerState:c})=>c.multiline,style:{height:"auto",resize:"none",padding:0,paddingTop:0}},{props:{type:"search"},style:{MozAppearance:"textfield"}}]}})),vv=ip({"@keyframes mui-auto-fill":{from:{display:"block"}},"@keyframes mui-auto-fill-cancel":{from:{display:"block"}}}),yp=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiInputBase"}),{"aria-describedby":u,autoComplete:c,autoFocus:d,className:p,color:h,components:m={},componentsProps:y={},defaultValue:S,disabled:E,disableInjectingGlobalStyles:R,endAdornment:x,error:C,fullWidth:D=!1,id:j,inputComponent:U="input",inputProps:M={},inputRef:O,margin:A,maxRows:$,minRows:H,multiline:P=!1,name:K,onBlur:v,onChange:_,onClick:W,onFocus:Z,onKeyDown:it,onKeyUp:F,placeholder:B,readOnly:V,renderSuffix:ot,rows:Q,size:z,slotProps:X={},slots:et={},startAdornment:nt,type:lt="text",value:ct,...ft}=l,Tt=M.value!=null?M.value:ct,{current:bt}=w.useRef(Tt!=null),At=w.useRef(),yt=w.useCallback(mt=>{},[]),wt=Pe(At,O,M.ref,yt),[St,$t]=w.useState(!1),Et=Io(),Vt=Xo({props:l,muiFormControl:Et,states:["color","disabled","error","hiddenLabel","size","required","filled"]});Vt.focused=Et?Et.focused:St,w.useEffect(()=>{!Et&&E&&St&&($t(!1),v&&v())},[Et,E,St,v]);const Te=Et&&Et.onFilled,Lt=Et&&Et.onEmpty,Kt=w.useCallback(mt=>{gu(mt)?Te&&Te():Lt&&Lt()},[Te,Lt]);Wn(()=>{bt&&Kt({value:Tt})},[Tt,Kt,bt]);const Qt=mt=>{Z&&Z(mt),M.onFocus&&M.onFocus(mt),Et&&Et.onFocus?Et.onFocus(mt):$t(!0)},Yt=mt=>{v&&v(mt),M.onBlur&&M.onBlur(mt),Et&&Et.onBlur?Et.onBlur(mt):$t(!1)},_t=(mt,...we)=>{if(!bt){const re=mt.target||At.current;if(re==null)throw new Error(za(1));Kt({value:re.value})}M.onChange&&M.onChange(mt,...we),_&&_(mt,...we)};w.useEffect(()=>{Kt(At.current)},[]);const dt=mt=>{At.current&&mt.currentTarget===mt.target&&At.current.focus(),W&&W(mt)};let Ue=U,le=M;P&&Ue==="input"&&(Q?le={type:void 0,minRows:Q,maxRows:Q,...le}:le={type:void 0,maxRows:$,minRows:H,...le},Ue=WR);const Ze=mt=>{Kt(mt.animationName==="mui-auto-fill-cancel"?At.current:{value:"x"})};w.useEffect(()=>{Et&&Et.setAdornedStart(!!nt)},[Et,nt]);const Le={...l,color:Vt.color||"primary",disabled:Vt.disabled,endAdornment:x,error:Vt.error,focused:Vt.focused,formControl:Et,fullWidth:D,hiddenLabel:Vt.hiddenLabel,multiline:P,size:Vt.size,startAdornment:nt,type:lt},ne=ZR(Le),Ee=et.root||m.Root||ku,ae=X.root||y.root||{},se=et.input||m.Input||$u;return le={...le,...X.input??y.input},T.jsxs(w.Fragment,{children:[!R&&typeof vv=="function"&&(yv||(yv=T.jsx(vv,{}))),T.jsxs(Ee,{...ae,ref:o,onClick:dt,...ft,...!Dd(Ee)&&{ownerState:{...Le,...ae.ownerState}},className:ht(ne.root,ae.className,p,V&&"MuiInputBase-readOnly"),children:[nt,T.jsx(gp.Provider,{value:null,children:T.jsx(se,{"aria-invalid":Vt.error,"aria-describedby":u,autoComplete:c,autoFocus:d,defaultValue:S,disabled:Vt.disabled,id:j,onAnimationStart:Ze,name:K,placeholder:B,readOnly:V,required:Vt.required,rows:Q,value:Tt,onKeyDown:it,onKeyUp:F,type:lt,...le,...!Dd(se)&&{as:Ue,ownerState:{...Le,...le.ownerState}},ref:wt,className:ht(ne.input,le.className,V&&"MuiInputBase-readOnly"),onBlur:Yt,onChange:_t,onFocus:Qt})}),x,ot?ot({...Vt,startAdornment:nt}):null]})]})});function FR(n){return Dt("MuiInput",n)}const Qi={...Ho,...Mt("MuiInput",["root","underline","input"])};function JR(n){return Dt("MuiOutlinedInput",n)}const ea={...Ho,...Mt("MuiOutlinedInput",["root","notchedOutline","input"])};function tw(n){return Dt("MuiFilledInput",n)}const Or={...Ho,...Mt("MuiFilledInput",["root","underline","input","adornedStart","adornedEnd","sizeSmall","multiline","hiddenLabel"])},ew=Mn(T.jsx("path",{d:"M7 10l5 5 5-5z"})),nw=Mn(T.jsx("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}));function aw(n){return Dt("MuiAvatar",n)}Mt("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);const rw=n=>{const{classes:r,variant:o,colorDefault:l}=n;return Bt({root:["root",o,l&&"colorDefault"],img:["img"],fallback:["fallback"]},aw,r)},ow=ut("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[o.variant],o.colorDefault&&r.colorDefault]}})(Gt(({theme:n})=>({position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:n.typography.fontFamily,fontSize:n.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(n.vars||n).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(n.vars||n).palette.background.default,...n.vars?{backgroundColor:n.vars.palette.Avatar.defaultBg}:{backgroundColor:n.palette.grey[400],...n.applyStyles("dark",{backgroundColor:n.palette.grey[600]})}}}]}))),iw=ut("img",{name:"MuiAvatar",slot:"Img"})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),lw=ut(nw,{name:"MuiAvatar",slot:"Fallback"})({width:"75%",height:"75%"});function sw({crossOrigin:n,referrerPolicy:r,src:o,srcSet:l}){const[u,c]=w.useState(!1);return w.useEffect(()=>{if(!o&&!l)return;c(!1);let d=!0;const p=new Image;return p.onload=()=>{d&&c("loaded")},p.onerror=()=>{d&&c("error")},p.crossOrigin=n,p.referrerPolicy=r,p.src=o,l&&(p.srcset=l),()=>{d=!1}},[n,r,o,l]),u}const uw=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiAvatar"}),{alt:u,children:c,className:d,component:p="div",slots:h={},slotProps:m={},imgProps:y,sizes:S,src:E,srcSet:R,variant:x="circular",...C}=l;let D=null;const j={...l,component:p,variant:x},U=sw({...y,...typeof m.img=="function"?m.img(j):m.img,src:E,srcSet:R}),M=E||R,O=M&&U!=="error";j.colorDefault=!O,delete j.ownerState;const A=rw(j),[$,H]=pe("root",{ref:o,className:ht(A.root,d),elementType:ow,externalForwardedProps:{slots:h,slotProps:m,component:p,...C},ownerState:j}),[P,K]=pe("img",{className:A.img,elementType:iw,externalForwardedProps:{slots:h,slotProps:{img:{...y,...m.img}}},additionalProps:{alt:u,src:E,srcSet:R,sizes:S},ownerState:j}),[v,_]=pe("fallback",{className:A.fallback,elementType:lw,externalForwardedProps:{slots:h,slotProps:m},shouldForwardComponentProp:!0,ownerState:j});return O?D=T.jsx(P,{...K}):c||c===0?D=c:M&&u?D=u[0]:D=T.jsx(v,{..._}),T.jsx($,{...H,children:D})}),cw={entering:{opacity:1},entered:{opacity:1}},Bd=w.forwardRef(function(r,o){const l=Go(),u={enter:l.transitions.duration.enteringScreen,exit:l.transitions.duration.leavingScreen},{addEndListener:c,appear:d=!0,children:p,easing:h,in:m,onEnter:y,onEntered:S,onEntering:E,onExit:R,onExited:x,onExiting:C,style:D,timeout:j=u,TransitionComponent:U=ca,...M}=r,O=w.useRef(null),A=Pe(O,Yo(p),o),$=it=>F=>{if(it){const B=O.current;F===void 0?it(B):it(B,F)}},H=$(E),P=$((it,F)=>{B0(it);const B=du({style:D,timeout:j,easing:h},{mode:"enter"});it.style.webkitTransition=l.transitions.create("opacity",B),it.style.transition=l.transitions.create("opacity",B),y&&y(it,F)}),K=$(S),v=$(C),_=$(it=>{const F=du({style:D,timeout:j,easing:h},{mode:"exit"});it.style.webkitTransition=l.transitions.create("opacity",F),it.style.transition=l.transitions.create("opacity",F),R&&R(it)}),W=$(x),Z=it=>{c&&c(O.current,it)};return T.jsx(U,{appear:d,in:m,nodeRef:O,onEnter:P,onEntered:K,onEntering:H,onExit:_,onExited:W,onExiting:v,addEndListener:Z,timeout:j,...M,children:(it,{ownerState:F,...B})=>w.cloneElement(p,{style:{opacity:0,visibility:it==="exited"&&!m?"hidden":void 0,...cw[it],...D,...p.props.style},ref:A,...B})})});function fw(n){return Dt("MuiBackdrop",n)}Mt("MuiBackdrop",["root","invisible"]);const dw=n=>{const{classes:r,invisible:o}=n;return Bt({root:["root",o&&"invisible"]},fw,r)},pw=ut("div",{name:"MuiBackdrop",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.invisible&&r.invisible]}})({position:"fixed",display:"flex",alignItems:"center",justifyContent:"center",right:0,bottom:0,top:0,left:0,backgroundColor:"rgba(0, 0, 0, 0.5)",WebkitTapHighlightColor:"transparent",variants:[{props:{invisible:!0},style:{backgroundColor:"transparent"}}]}),Q0=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiBackdrop"}),{children:u,className:c,component:d="div",invisible:p=!1,open:h,components:m={},componentsProps:y={},slotProps:S={},slots:E={},TransitionComponent:R,transitionDuration:x,...C}=l,D={...l,component:d,invisible:p},j=dw(D),U={transition:R,root:m.Root,...E},M={...y,...S},O={slots:U,slotProps:M},[A,$]=pe("root",{elementType:pw,externalForwardedProps:O,className:ht(j.root,c),ownerState:D}),[H,P]=pe("transition",{elementType:Bd,externalForwardedProps:O,ownerState:D});return T.jsx(H,{in:h,timeout:x,...C,...P,children:T.jsx(A,{"aria-hidden":!0,...$,classes:j,ref:o,children:u})})}),hw=Mt("MuiBox",["root"]),mw=zu(),Se=B2({themeId:ia,defaultTheme:mw,defaultClassName:hw.root,generateClassName:i0.generate});function gw(n){return Dt("MuiButton",n)}const Mr=Mt("MuiButton",["root","text","textInherit","textPrimary","textSecondary","textSuccess","textError","textInfo","textWarning","outlined","outlinedInherit","outlinedPrimary","outlinedSecondary","outlinedSuccess","outlinedError","outlinedInfo","outlinedWarning","contained","containedInherit","containedPrimary","containedSecondary","containedSuccess","containedError","containedInfo","containedWarning","disableElevation","focusVisible","disabled","colorInherit","colorPrimary","colorSecondary","colorSuccess","colorError","colorInfo","colorWarning","textSizeSmall","textSizeMedium","textSizeLarge","outlinedSizeSmall","outlinedSizeMedium","outlinedSizeLarge","containedSizeSmall","containedSizeMedium","containedSizeLarge","sizeMedium","sizeSmall","sizeLarge","fullWidth","startIcon","endIcon","icon","iconSizeSmall","iconSizeMedium","iconSizeLarge","loading","loadingWrapper","loadingIconPlaceholder","loadingIndicator","loadingPositionCenter","loadingPositionStart","loadingPositionEnd"]),yw=w.createContext({}),vw=w.createContext(void 0),bw=n=>{const{color:r,disableElevation:o,fullWidth:l,size:u,variant:c,loading:d,loadingPosition:p,classes:h}=n,m={root:["root",d&&"loading",c,`${c}${st(r)}`,`size${st(u)}`,`${c}Size${st(u)}`,`color${st(r)}`,o&&"disableElevation",l&&"fullWidth",d&&`loadingPosition${st(p)}`],startIcon:["icon","startIcon",`iconSize${st(u)}`],endIcon:["icon","endIcon",`iconSize${st(u)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]},y=Bt(m,gw,h);return{...h,...y}},Z0=[{props:{size:"small"},style:{"& > *:nth-of-type(1)":{fontSize:18}}},{props:{size:"medium"},style:{"& > *:nth-of-type(1)":{fontSize:20}}},{props:{size:"large"},style:{"& > *:nth-of-type(1)":{fontSize:22}}}],Sw=ut(ko,{shouldForwardProp:n=>On(n)||n==="classes",name:"MuiButton",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[o.variant],r[`${o.variant}${st(o.color)}`],r[`size${st(o.size)}`],r[`${o.variant}Size${st(o.size)}`],o.color==="inherit"&&r.colorInherit,o.disableElevation&&r.disableElevation,o.fullWidth&&r.fullWidth,o.loading&&r.loading]}})(Gt(({theme:n})=>{const r=n.palette.mode==="light"?n.palette.grey[300]:n.palette.grey[800],o=n.palette.mode==="light"?n.palette.grey.A100:n.palette.grey[700];return{...n.typography.button,minWidth:64,padding:"6px 16px",border:0,borderRadius:(n.vars||n).shape.borderRadius,transition:n.transitions.create(["background-color","box-shadow","border-color","color"],{duration:n.transitions.duration.short}),"&:hover":{textDecoration:"none"},[`&.${Mr.disabled}`]:{color:(n.vars||n).palette.action.disabled},variants:[{props:{variant:"contained"},style:{color:"var(--variant-containedColor)",backgroundColor:"var(--variant-containedBg)",boxShadow:(n.vars||n).shadows[2],"&:hover":{boxShadow:(n.vars||n).shadows[4],"@media (hover: none)":{boxShadow:(n.vars||n).shadows[2]}},"&:active":{boxShadow:(n.vars||n).shadows[8]},[`&.${Mr.focusVisible}`]:{boxShadow:(n.vars||n).shadows[6]},[`&.${Mr.disabled}`]:{color:(n.vars||n).palette.action.disabled,boxShadow:(n.vars||n).shadows[0],backgroundColor:(n.vars||n).palette.action.disabledBackground}}},{props:{variant:"outlined"},style:{padding:"5px 15px",border:"1px solid currentColor",borderColor:"var(--variant-outlinedBorder, currentColor)",backgroundColor:"var(--variant-outlinedBg)",color:"var(--variant-outlinedColor)",[`&.${Mr.disabled}`]:{border:`1px solid ${(n.vars||n).palette.action.disabledBackground}`}}},{props:{variant:"text"},style:{padding:"6px 8px",color:"var(--variant-textColor)",backgroundColor:"var(--variant-textBg)"}},...Object.entries(n.palette).filter(rn()).map(([l])=>({props:{color:l},style:{"--variant-textColor":(n.vars||n).palette[l].main,"--variant-outlinedColor":(n.vars||n).palette[l].main,"--variant-outlinedBorder":n.vars?`rgba(${n.vars.palette[l].mainChannel} / 0.5)`:ye(n.palette[l].main,.5),"--variant-containedColor":(n.vars||n).palette[l].contrastText,"--variant-containedBg":(n.vars||n).palette[l].main,"@media (hover: hover)":{"&:hover":{"--variant-containedBg":(n.vars||n).palette[l].dark,"--variant-textBg":n.vars?`rgba(${n.vars.palette[l].mainChannel} / ${n.vars.palette.action.hoverOpacity})`:ye(n.palette[l].main,n.palette.action.hoverOpacity),"--variant-outlinedBorder":(n.vars||n).palette[l].main,"--variant-outlinedBg":n.vars?`rgba(${n.vars.palette[l].mainChannel} / ${n.vars.palette.action.hoverOpacity})`:ye(n.palette[l].main,n.palette.action.hoverOpacity)}}}})),{props:{color:"inherit"},style:{color:"inherit",borderColor:"currentColor","--variant-containedBg":n.vars?n.vars.palette.Button.inheritContainedBg:r,"@media (hover: hover)":{"&:hover":{"--variant-containedBg":n.vars?n.vars.palette.Button.inheritContainedHoverBg:o,"--variant-textBg":n.vars?`rgba(${n.vars.palette.text.primaryChannel} / ${n.vars.palette.action.hoverOpacity})`:ye(n.palette.text.primary,n.palette.action.hoverOpacity),"--variant-outlinedBg":n.vars?`rgba(${n.vars.palette.text.primaryChannel} / ${n.vars.palette.action.hoverOpacity})`:ye(n.palette.text.primary,n.palette.action.hoverOpacity)}}}},{props:{size:"small",variant:"text"},style:{padding:"4px 5px",fontSize:n.typography.pxToRem(13)}},{props:{size:"large",variant:"text"},style:{padding:"8px 11px",fontSize:n.typography.pxToRem(15)}},{props:{size:"small",variant:"outlined"},style:{padding:"3px 9px",fontSize:n.typography.pxToRem(13)}},{props:{size:"large",variant:"outlined"},style:{padding:"7px 21px",fontSize:n.typography.pxToRem(15)}},{props:{size:"small",variant:"contained"},style:{padding:"4px 10px",fontSize:n.typography.pxToRem(13)}},{props:{size:"large",variant:"contained"},style:{padding:"8px 22px",fontSize:n.typography.pxToRem(15)}},{props:{disableElevation:!0},style:{boxShadow:"none","&:hover":{boxShadow:"none"},[`&.${Mr.focusVisible}`]:{boxShadow:"none"},"&:active":{boxShadow:"none"},[`&.${Mr.disabled}`]:{boxShadow:"none"}}},{props:{fullWidth:!0},style:{width:"100%"}},{props:{loadingPosition:"center"},style:{transition:n.transitions.create(["background-color","box-shadow","border-color"],{duration:n.transitions.duration.short}),[`&.${Mr.loading}`]:{color:"transparent"}}}]}})),xw=ut("span",{name:"MuiButton",slot:"StartIcon",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.startIcon,o.loading&&r.startIconLoadingStart,r[`iconSize${st(o.size)}`]]}})(({theme:n})=>({display:"inherit",marginRight:8,marginLeft:-4,variants:[{props:{size:"small"},style:{marginLeft:-2}},{props:{loadingPosition:"start",loading:!0},style:{transition:n.transitions.create(["opacity"],{duration:n.transitions.duration.short}),opacity:0}},{props:{loadingPosition:"start",loading:!0,fullWidth:!0},style:{marginRight:-8}},...Z0]})),Cw=ut("span",{name:"MuiButton",slot:"EndIcon",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.endIcon,o.loading&&r.endIconLoadingEnd,r[`iconSize${st(o.size)}`]]}})(({theme:n})=>({display:"inherit",marginRight:-4,marginLeft:8,variants:[{props:{size:"small"},style:{marginRight:-2}},{props:{loadingPosition:"end",loading:!0},style:{transition:n.transitions.create(["opacity"],{duration:n.transitions.duration.short}),opacity:0}},{props:{loadingPosition:"end",loading:!0,fullWidth:!0},style:{marginLeft:-8}},...Z0]})),Tw=ut("span",{name:"MuiButton",slot:"LoadingIndicator"})(({theme:n})=>({display:"none",position:"absolute",visibility:"visible",variants:[{props:{loading:!0},style:{display:"flex"}},{props:{loadingPosition:"start"},style:{left:14}},{props:{loadingPosition:"start",size:"small"},style:{left:10}},{props:{variant:"text",loadingPosition:"start"},style:{left:6}},{props:{loadingPosition:"center"},style:{left:"50%",transform:"translate(-50%)",color:(n.vars||n).palette.action.disabled}},{props:{loadingPosition:"end"},style:{right:14}},{props:{loadingPosition:"end",size:"small"},style:{right:10}},{props:{variant:"text",loadingPosition:"end"},style:{right:6}},{props:{loadingPosition:"start",fullWidth:!0},style:{position:"relative",left:-10}},{props:{loadingPosition:"end",fullWidth:!0},style:{position:"relative",right:-10}}]})),bv=ut("span",{name:"MuiButton",slot:"LoadingIconPlaceholder"})({display:"inline-block",width:"1em",height:"1em"}),or=w.forwardRef(function(r,o){const l=w.useContext(yw),u=w.useContext(vw),c=pl(l,r),d=Nt({props:c,name:"MuiButton"}),{children:p,color:h="primary",component:m="button",className:y,disabled:S=!1,disableElevation:E=!1,disableFocusRipple:R=!1,endIcon:x,focusVisibleClassName:C,fullWidth:D=!1,id:j,loading:U=null,loadingIndicator:M,loadingPosition:O="center",size:A="medium",startIcon:$,type:H,variant:P="text",...K}=d,v=Vo(j),_=M??T.jsx(_0,{"aria-labelledby":v,color:"inherit",size:16}),W={...d,color:h,component:m,disabled:S,disableElevation:E,disableFocusRipple:R,fullWidth:D,loading:U,loadingIndicator:_,loadingPosition:O,size:A,type:H,variant:P},Z=bw(W),it=($||U&&O==="start")&&T.jsx(xw,{className:Z.startIcon,ownerState:W,children:$||T.jsx(bv,{className:Z.loadingIconPlaceholder,ownerState:W})}),F=(x||U&&O==="end")&&T.jsx(Cw,{className:Z.endIcon,ownerState:W,children:x||T.jsx(bv,{className:Z.loadingIconPlaceholder,ownerState:W})}),B=u||"",V=typeof U=="boolean"?T.jsx("span",{className:Z.loadingWrapper,style:{display:"contents"},children:U&&T.jsx(Tw,{className:Z.loadingIndicator,ownerState:W,children:_})}):null;return T.jsxs(Sw,{ownerState:W,className:ht(l.className,Z.root,y,B),component:m,disabled:S||U,focusRipple:!R,focusVisibleClassName:ht(Z.focusVisible,C),ref:o,type:H,id:U?v:j,...K,classes:Z,children:[it,O!=="end"&&V,p,O==="end"&&V,F]})});function Ew(n){return Dt("MuiCard",n)}Mt("MuiCard",["root"]);const Rw=n=>{const{classes:r}=n;return Bt({root:["root"]},Ew,r)},ww=ut(wl,{name:"MuiCard",slot:"Root"})({overflow:"hidden"}),Oa=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiCard"}),{className:u,raised:c=!1,...d}=l,p={...l,raised:c},h=Rw(p);return T.jsx(ww,{className:ht(h.root,u),elevation:c?8:void 0,ref:o,ownerState:p,...d})});function Aw(n){return Dt("MuiCardContent",n)}Mt("MuiCardContent",["root"]);const Ow=n=>{const{classes:r}=n;return Bt({root:["root"]},Aw,r)},Mw=ut("div",{name:"MuiCardContent",slot:"Root"})({padding:16,"&:last-child":{paddingBottom:24}}),rr=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiCardContent"}),{className:u,component:c="div",...d}=l,p={...l,component:c},h=Ow(p);return T.jsx(Mw,{as:c,className:ht(h.root,u),ownerState:p,ref:o,...d})}),zw=bC({createStyledComponent:ut("div",{name:"MuiContainer",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[`maxWidth${st(String(o.maxWidth))}`],o.fixed&&r.fixed,o.disableGutters&&r.disableGutters]}}),useThemeProps:n=>Nt({props:n,name:"MuiContainer"})});function F0(n=window){const r=n.document.documentElement.clientWidth;return n.innerWidth-r}function Dw(n){const r=wn(n);return r.body===n?Ba(n).innerWidth>r.documentElement.clientWidth:n.scrollHeight>n.clientHeight}function sl(n,r){r?n.setAttribute("aria-hidden","true"):n.removeAttribute("aria-hidden")}function Sv(n){return parseInt(Ba(n).getComputedStyle(n).paddingRight,10)||0}function Bw(n){const o=["TEMPLATE","SCRIPT","STYLE","LINK","MAP","META","NOSCRIPT","PICTURE","COL","COLGROUP","PARAM","SLOT","SOURCE","TRACK"].includes(n.tagName),l=n.tagName==="INPUT"&&n.getAttribute("type")==="hidden";return o||l}function xv(n,r,o,l,u){const c=[r,o,...l];[].forEach.call(n.children,d=>{const p=!c.includes(d),h=!Bw(d);p&&h&&sl(d,u)})}function ld(n,r){let o=-1;return n.some((l,u)=>r(l)?(o=u,!0):!1),o}function Nw(n,r){const o=[],l=n.container;if(!r.disableScrollLock){if(Dw(l)){const d=F0(Ba(l));o.push({value:l.style.paddingRight,property:"padding-right",el:l}),l.style.paddingRight=`${Sv(l)+d}px`;const p=wn(l).querySelectorAll(".mui-fixed");[].forEach.call(p,h=>{o.push({value:h.style.paddingRight,property:"padding-right",el:h}),h.style.paddingRight=`${Sv(h)+d}px`})}let c;if(l.parentNode instanceof DocumentFragment)c=wn(l).body;else{const d=l.parentElement,p=Ba(l);c=(d==null?void 0:d.nodeName)==="HTML"&&p.getComputedStyle(d).overflowY==="scroll"?d:l}o.push({value:c.style.overflow,property:"overflow",el:c},{value:c.style.overflowX,property:"overflow-x",el:c},{value:c.style.overflowY,property:"overflow-y",el:c}),c.style.overflow="hidden"}return()=>{o.forEach(({value:c,el:d,property:p})=>{c?d.style.setProperty(p,c):d.style.removeProperty(p)})}}function jw(n){const r=[];return[].forEach.call(n.children,o=>{o.getAttribute("aria-hidden")==="true"&&r.push(o)}),r}class kw{constructor(){this.modals=[],this.containers=[]}add(r,o){let l=this.modals.indexOf(r);if(l!==-1)return l;l=this.modals.length,this.modals.push(r),r.modalRef&&sl(r.modalRef,!1);const u=jw(o);xv(o,r.mount,r.modalRef,u,!0);const c=ld(this.containers,d=>d.container===o);return c!==-1?(this.containers[c].modals.push(r),l):(this.containers.push({modals:[r],container:o,restore:null,hiddenSiblings:u}),l)}mount(r,o){const l=ld(this.containers,c=>c.modals.includes(r)),u=this.containers[l];u.restore||(u.restore=Nw(u,o))}remove(r,o=!0){const l=this.modals.indexOf(r);if(l===-1)return l;const u=ld(this.containers,d=>d.modals.includes(r)),c=this.containers[u];if(c.modals.splice(c.modals.indexOf(r),1),this.modals.splice(l,1),c.modals.length===0)c.restore&&c.restore(),r.modalRef&&sl(r.modalRef,o),xv(c.container,r.mount,r.modalRef,c.hiddenSiblings,!1),this.containers.splice(u,1);else{const d=c.modals[c.modals.length-1];d.modalRef&&sl(d.modalRef,!1)}return l}isTopModal(r){return this.modals.length>0&&this.modals[this.modals.length-1]===r}}const $w=["input","select","textarea","a[href]","button","[tabindex]","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])'].join(",");function _w(n){const r=parseInt(n.getAttribute("tabindex")||"",10);return Number.isNaN(r)?n.contentEditable==="true"||(n.nodeName==="AUDIO"||n.nodeName==="VIDEO"||n.nodeName==="DETAILS")&&n.getAttribute("tabindex")===null?0:n.tabIndex:r}function Uw(n){if(n.tagName!=="INPUT"||n.type!=="radio"||!n.name)return!1;const r=l=>n.ownerDocument.querySelector(`input[type="radio"]${l}`);let o=r(`[name="${n.name}"]:checked`);return o||(o=r(`[name="${n.name}"]`)),o!==n}function Lw(n){return!(n.disabled||n.tagName==="INPUT"&&n.type==="hidden"||Uw(n))}function Hw(n){const r=[],o=[];return Array.from(n.querySelectorAll($w)).forEach((l,u)=>{const c=_w(l);c===-1||!Lw(l)||(c===0?r.push(l):o.push({documentOrder:u,tabIndex:c,node:l}))}),o.sort((l,u)=>l.tabIndex===u.tabIndex?l.documentOrder-u.documentOrder:l.tabIndex-u.tabIndex).map(l=>l.node).concat(r)}function qw(){return!0}function Pw(n){const{children:r,disableAutoFocus:o=!1,disableEnforceFocus:l=!1,disableRestoreFocus:u=!1,getTabbable:c=Hw,isEnabled:d=qw,open:p}=n,h=w.useRef(!1),m=w.useRef(null),y=w.useRef(null),S=w.useRef(null),E=w.useRef(null),R=w.useRef(!1),x=w.useRef(null),C=Pe(Yo(r),x),D=w.useRef(null);w.useEffect(()=>{!p||!x.current||(R.current=!o)},[o,p]),w.useEffect(()=>{if(!p||!x.current)return;const M=wn(x.current);return x.current.contains(M.activeElement)||(x.current.hasAttribute("tabIndex")||x.current.setAttribute("tabIndex","-1"),R.current&&x.current.focus()),()=>{u||(S.current&&S.current.focus&&(h.current=!0,S.current.focus()),S.current=null)}},[p]),w.useEffect(()=>{if(!p||!x.current)return;const M=wn(x.current),O=H=>{D.current=H,!(l||!d()||H.key!=="Tab")&&M.activeElement===x.current&&H.shiftKey&&(h.current=!0,y.current&&y.current.focus())},A=()=>{var K,v;const H=x.current;if(H===null)return;if(!M.hasFocus()||!d()||h.current){h.current=!1;return}if(H.contains(M.activeElement)||l&&M.activeElement!==m.current&&M.activeElement!==y.current)return;if(M.activeElement!==E.current)E.current=null;else if(E.current!==null)return;if(!R.current)return;let P=[];if((M.activeElement===m.current||M.activeElement===y.current)&&(P=c(x.current)),P.length>0){const _=!!((K=D.current)!=null&&K.shiftKey&&((v=D.current)==null?void 0:v.key)==="Tab"),W=P[0],Z=P[P.length-1];typeof W!="string"&&typeof Z!="string"&&(_?Z.focus():W.focus())}else H.focus()};M.addEventListener("focusin",A),M.addEventListener("keydown",O,!0);const $=setInterval(()=>{M.activeElement&&M.activeElement.tagName==="BODY"&&A()},50);return()=>{clearInterval($),M.removeEventListener("focusin",A),M.removeEventListener("keydown",O,!0)}},[o,l,u,d,p,c]);const j=M=>{S.current===null&&(S.current=M.relatedTarget),R.current=!0,E.current=M.target;const O=r.props.onFocus;O&&O(M)},U=M=>{S.current===null&&(S.current=M.relatedTarget),R.current=!0};return T.jsxs(w.Fragment,{children:[T.jsx("div",{tabIndex:p?0:-1,onFocus:U,ref:m,"data-testid":"sentinelStart"}),w.cloneElement(r,{ref:C,onFocus:j}),T.jsx("div",{tabIndex:p?0:-1,onFocus:U,ref:y,"data-testid":"sentinelEnd"})]})}function Gw(n){return typeof n=="function"?n():n}function Vw(n){return n?n.props.hasOwnProperty("in"):!1}const Cv=()=>{},Qs=new kw;function Yw(n){const{container:r,disableEscapeKeyDown:o=!1,disableScrollLock:l=!1,closeAfterTransition:u=!1,onTransitionEnter:c,onTransitionExited:d,children:p,onClose:h,open:m,rootRef:y}=n,S=w.useRef({}),E=w.useRef(null),R=w.useRef(null),x=Pe(R,y),[C,D]=w.useState(!m),j=Vw(p);let U=!0;(n["aria-hidden"]==="false"||n["aria-hidden"]===!1)&&(U=!1);const M=()=>wn(E.current),O=()=>(S.current.modalRef=R.current,S.current.mount=E.current,S.current),A=()=>{Qs.mount(O(),{disableScrollLock:l}),R.current&&(R.current.scrollTop=0)},$=Ma(()=>{const F=Gw(r)||M().body;Qs.add(O(),F),R.current&&A()}),H=()=>Qs.isTopModal(O()),P=Ma(F=>{E.current=F,F&&(m&&H()?A():R.current&&sl(R.current,U))}),K=w.useCallback(()=>{Qs.remove(O(),U)},[U]);w.useEffect(()=>()=>{K()},[K]),w.useEffect(()=>{m?$():(!j||!u)&&K()},[m,K,j,u,$]);const v=F=>B=>{var V;(V=F.onKeyDown)==null||V.call(F,B),!(B.key!=="Escape"||B.which===229||!H())&&(o||(B.stopPropagation(),h&&h(B,"escapeKeyDown")))},_=F=>B=>{var V;(V=F.onClick)==null||V.call(F,B),B.target===B.currentTarget&&h&&h(B,"backdropClick")};return{getRootProps:(F={})=>{const B=k0(n);delete B.onTransitionEnter,delete B.onTransitionExited;const V={...B,...F};return{role:"presentation",...V,onKeyDown:v(V),ref:x}},getBackdropProps:(F={})=>{const B=F;return{"aria-hidden":!0,...B,onClick:_(B),open:m}},getTransitionProps:()=>{const F=()=>{D(!1),c&&c()},B=()=>{D(!0),d&&d(),u&&K()};return{onEnter:Ky(F,(p==null?void 0:p.props.onEnter)??Cv),onExited:Ky(B,(p==null?void 0:p.props.onExited)??Cv)}},rootRef:x,portalRef:P,isTopModal:H,exited:C,hasTransition:j}}function Xw(n){return Dt("MuiModal",n)}Mt("MuiModal",["root","hidden","backdrop"]);const Iw=n=>{const{open:r,exited:o,classes:l}=n;return Bt({root:["root",!r&&o&&"hidden"],backdrop:["backdrop"]},Xw,l)},Ww=ut("div",{name:"MuiModal",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,!o.open&&o.exited&&r.hidden]}})(Gt(({theme:n})=>({position:"fixed",zIndex:(n.vars||n).zIndex.modal,right:0,bottom:0,top:0,left:0,variants:[{props:({ownerState:r})=>!r.open&&r.exited,style:{visibility:"hidden"}}]}))),Kw=ut(Q0,{name:"MuiModal",slot:"Backdrop"})({zIndex:-1}),J0=w.forwardRef(function(r,o){const l=Nt({name:"MuiModal",props:r}),{BackdropComponent:u=Kw,BackdropProps:c,classes:d,className:p,closeAfterTransition:h=!1,children:m,container:y,component:S,components:E={},componentsProps:R={},disableAutoFocus:x=!1,disableEnforceFocus:C=!1,disableEscapeKeyDown:D=!1,disablePortal:j=!1,disableRestoreFocus:U=!1,disableScrollLock:M=!1,hideBackdrop:O=!1,keepMounted:A=!1,onClose:$,onTransitionEnter:H,onTransitionExited:P,open:K,slotProps:v={},slots:_={},theme:W,...Z}=l,it={...l,closeAfterTransition:h,disableAutoFocus:x,disableEnforceFocus:C,disableEscapeKeyDown:D,disablePortal:j,disableRestoreFocus:U,disableScrollLock:M,hideBackdrop:O,keepMounted:A},{getRootProps:F,getBackdropProps:B,getTransitionProps:V,portalRef:ot,isTopModal:Q,exited:z,hasTransition:X}=Yw({...it,rootRef:o}),et={...it,exited:z},nt=Iw(et),lt={};if(m.props.tabIndex===void 0&&(lt.tabIndex="-1"),X){const{onEnter:yt,onExited:wt}=V();lt.onEnter=yt,lt.onExited=wt}const ct={slots:{root:E.Root,backdrop:E.Backdrop,..._},slotProps:{...R,...v}},[ft,Tt]=pe("root",{ref:o,elementType:Ww,externalForwardedProps:{...ct,...Z,component:S},getSlotProps:F,ownerState:et,className:ht(p,nt==null?void 0:nt.root,!et.open&&et.exited&&(nt==null?void 0:nt.hidden))}),[bt,At]=pe("backdrop",{ref:c==null?void 0:c.ref,elementType:u,externalForwardedProps:ct,shouldForwardComponentProp:!0,additionalProps:c,getSlotProps:yt=>B({...yt,onClick:wt=>{yt!=null&&yt.onClick&&yt.onClick(wt)}}),className:ht(c==null?void 0:c.className,nt==null?void 0:nt.backdrop),ownerState:et});return!A&&!K&&(!X||z)?null:T.jsx(W0,{ref:ot,container:y,disablePortal:j,children:T.jsxs(ft,{...Tt,children:[!O&&u?T.jsx(bt,{...At}):null,T.jsx(Pw,{disableEnforceFocus:C,disableAutoFocus:x,disableRestoreFocus:U,isEnabled:Q,open:K,children:w.cloneElement(m,lt)})]})})});function Qw(n){return Dt("MuiDialog",n)}const sd=Mt("MuiDialog",["root","scrollPaper","scrollBody","container","paper","paperScrollPaper","paperScrollBody","paperWidthFalse","paperWidthXs","paperWidthSm","paperWidthMd","paperWidthLg","paperWidthXl","paperFullWidth","paperFullScreen"]),tb=w.createContext({}),Zw=ut(Q0,{name:"MuiDialog",slot:"Backdrop",overrides:(n,r)=>r.backdrop})({zIndex:-1}),Fw=n=>{const{classes:r,scroll:o,maxWidth:l,fullWidth:u,fullScreen:c}=n,d={root:["root"],container:["container",`scroll${st(o)}`],paper:["paper",`paperScroll${st(o)}`,`paperWidth${st(String(l))}`,u&&"paperFullWidth",c&&"paperFullScreen"]};return Bt(d,Qw,r)},Jw=ut(J0,{name:"MuiDialog",slot:"Root"})({"@media print":{position:"absolute !important"}}),t4=ut("div",{name:"MuiDialog",slot:"Container",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.container,r[`scroll${st(o.scroll)}`]]}})({height:"100%","@media print":{height:"auto"},outline:0,variants:[{props:{scroll:"paper"},style:{display:"flex",justifyContent:"center",alignItems:"center"}},{props:{scroll:"body"},style:{overflowY:"auto",overflowX:"hidden",textAlign:"center","&::after":{content:'""',display:"inline-block",verticalAlign:"middle",height:"100%",width:"0"}}}]}),e4=ut(wl,{name:"MuiDialog",slot:"Paper",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.paper,r[`scrollPaper${st(o.scroll)}`],r[`paperWidth${st(String(o.maxWidth))}`],o.fullWidth&&r.paperFullWidth,o.fullScreen&&r.paperFullScreen]}})(Gt(({theme:n})=>({margin:32,position:"relative",overflowY:"auto","@media print":{overflowY:"visible",boxShadow:"none"},variants:[{props:{scroll:"paper"},style:{display:"flex",flexDirection:"column",maxHeight:"calc(100% - 64px)"}},{props:{scroll:"body"},style:{display:"inline-block",verticalAlign:"middle",textAlign:"initial"}},{props:({ownerState:r})=>!r.maxWidth,style:{maxWidth:"calc(100% - 64px)"}},{props:{maxWidth:"xs"},style:{maxWidth:n.breakpoints.unit==="px"?Math.max(n.breakpoints.values.xs,444):`max(${n.breakpoints.values.xs}${n.breakpoints.unit}, 444px)`,[`&.${sd.paperScrollBody}`]:{[n.breakpoints.down(Math.max(n.breakpoints.values.xs,444)+32*2)]:{maxWidth:"calc(100% - 64px)"}}}},...Object.keys(n.breakpoints.values).filter(r=>r!=="xs").map(r=>({props:{maxWidth:r},style:{maxWidth:`${n.breakpoints.values[r]}${n.breakpoints.unit}`,[`&.${sd.paperScrollBody}`]:{[n.breakpoints.down(n.breakpoints.values[r]+32*2)]:{maxWidth:"calc(100% - 64px)"}}}})),{props:({ownerState:r})=>r.fullWidth,style:{width:"calc(100% - 64px)"}},{props:({ownerState:r})=>r.fullScreen,style:{margin:0,width:"100%",maxWidth:"100%",height:"100%",maxHeight:"none",borderRadius:0,[`&.${sd.paperScrollBody}`]:{margin:0,maxWidth:"100%"}}}]}))),n4=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiDialog"}),u=Go(),c={enter:u.transitions.duration.enteringScreen,exit:u.transitions.duration.leavingScreen},{"aria-describedby":d,"aria-labelledby":p,"aria-modal":h=!0,BackdropComponent:m,BackdropProps:y,children:S,className:E,disableEscapeKeyDown:R=!1,fullScreen:x=!1,fullWidth:C=!1,maxWidth:D="sm",onClick:j,onClose:U,open:M,PaperComponent:O=wl,PaperProps:A={},scroll:$="paper",slots:H={},slotProps:P={},TransitionComponent:K=Bd,transitionDuration:v=c,TransitionProps:_,...W}=l,Z={...l,disableEscapeKeyDown:R,fullScreen:x,fullWidth:C,maxWidth:D,scroll:$},it=Fw(Z),F=w.useRef(),B=$t=>{F.current=$t.target===$t.currentTarget},V=$t=>{j&&j($t),F.current&&(F.current=null,U&&U($t,"backdropClick"))},ot=Vo(p),Q=w.useMemo(()=>({titleId:ot}),[ot]),z={transition:K,...H},X={transition:_,paper:A,backdrop:y,...P},et={slots:z,slotProps:X},[nt,lt]=pe("root",{elementType:Jw,shouldForwardComponentProp:!0,externalForwardedProps:et,ownerState:Z,className:ht(it.root,E),ref:o}),[ct,ft]=pe("backdrop",{elementType:Zw,shouldForwardComponentProp:!0,externalForwardedProps:et,ownerState:Z}),[Tt,bt]=pe("paper",{elementType:e4,shouldForwardComponentProp:!0,externalForwardedProps:et,ownerState:Z,className:ht(it.paper,A.className)}),[At,yt]=pe("container",{elementType:t4,externalForwardedProps:et,ownerState:Z,className:it.container}),[wt,St]=pe("transition",{elementType:Bd,externalForwardedProps:et,ownerState:Z,additionalProps:{appear:!0,in:M,timeout:v,role:"presentation"}});return T.jsx(nt,{closeAfterTransition:!0,slots:{backdrop:ct},slotProps:{backdrop:{transitionDuration:v,as:m,...ft}},disableEscapeKeyDown:R,onClose:U,open:M,onClick:V,...lt,...W,children:T.jsx(wt,{...St,children:T.jsx(At,{onMouseDown:B,...yt,children:T.jsx(Tt,{as:O,elevation:24,role:"dialog","aria-describedby":d,"aria-labelledby":ot,"aria-modal":h,...bt,children:T.jsx(tb.Provider,{value:Q,children:S})})})})})});function a4(n){return Dt("MuiDialogActions",n)}Mt("MuiDialogActions",["root","spacing"]);const r4=n=>{const{classes:r,disableSpacing:o}=n;return Bt({root:["root",!o&&"spacing"]},a4,r)},o4=ut("div",{name:"MuiDialogActions",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,!o.disableSpacing&&r.spacing]}})({display:"flex",alignItems:"center",padding:8,justifyContent:"flex-end",flex:"0 0 auto",variants:[{props:({ownerState:n})=>!n.disableSpacing,style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),i4=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiDialogActions"}),{className:u,disableSpacing:c=!1,...d}=l,p={...l,disableSpacing:c},h=r4(p);return T.jsx(o4,{className:ht(h.root,u),ownerState:p,ref:o,...d})});function l4(n){return Dt("MuiDialogContent",n)}Mt("MuiDialogContent",["root","dividers"]);function s4(n){return Dt("MuiDialogTitle",n)}const u4=Mt("MuiDialogTitle",["root"]),c4=n=>{const{classes:r,dividers:o}=n;return Bt({root:["root",o&&"dividers"]},l4,r)},f4=ut("div",{name:"MuiDialogContent",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.dividers&&r.dividers]}})(Gt(({theme:n})=>({flex:"1 1 auto",WebkitOverflowScrolling:"touch",overflowY:"auto",padding:"20px 24px",variants:[{props:({ownerState:r})=>r.dividers,style:{padding:"16px 24px",borderTop:`1px solid ${(n.vars||n).palette.divider}`,borderBottom:`1px solid ${(n.vars||n).palette.divider}`}},{props:({ownerState:r})=>!r.dividers,style:{[`.${u4.root} + &`]:{paddingTop:0}}}]}))),d4=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiDialogContent"}),{className:u,dividers:c=!1,...d}=l,p={...l,dividers:c},h=c4(p);return T.jsx(f4,{className:ht(h.root,u),ownerState:p,ref:o,...d})}),p4=n=>{const{classes:r}=n;return Bt({root:["root"]},s4,r)},h4=ut(jt,{name:"MuiDialogTitle",slot:"Root"})({padding:"16px 24px",flex:"0 0 auto"}),m4=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiDialogTitle"}),{className:u,id:c,...d}=l,p=l,h=p4(p),{titleId:m=c}=w.useContext(tb);return T.jsx(h4,{component:"h2",className:ht(h.root,u),ownerState:p,ref:o,variant:"h6",id:c??m,...d})});function g4(n){return Dt("MuiDivider",n)}const Tv=Mt("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"]),y4=n=>{const{absolute:r,children:o,classes:l,flexItem:u,light:c,orientation:d,textAlign:p,variant:h}=n;return Bt({root:["root",r&&"absolute",h,c&&"light",d==="vertical"&&"vertical",u&&"flexItem",o&&"withChildren",o&&d==="vertical"&&"withChildrenVertical",p==="right"&&d!=="vertical"&&"textAlignRight",p==="left"&&d!=="vertical"&&"textAlignLeft"],wrapper:["wrapper",d==="vertical"&&"wrapperVertical"]},g4,l)},v4=ut("div",{name:"MuiDivider",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.absolute&&r.absolute,r[o.variant],o.light&&r.light,o.orientation==="vertical"&&r.vertical,o.flexItem&&r.flexItem,o.children&&r.withChildren,o.children&&o.orientation==="vertical"&&r.withChildrenVertical,o.textAlign==="right"&&o.orientation!=="vertical"&&r.textAlignRight,o.textAlign==="left"&&o.orientation!=="vertical"&&r.textAlignLeft]}})(Gt(({theme:n})=>({margin:0,flexShrink:0,borderWidth:0,borderStyle:"solid",borderColor:(n.vars||n).palette.divider,borderBottomWidth:"thin",variants:[{props:{absolute:!0},style:{position:"absolute",bottom:0,left:0,width:"100%"}},{props:{light:!0},style:{borderColor:n.vars?`rgba(${n.vars.palette.dividerChannel} / 0.08)`:ye(n.palette.divider,.08)}},{props:{variant:"inset"},style:{marginLeft:72}},{props:{variant:"middle",orientation:"horizontal"},style:{marginLeft:n.spacing(2),marginRight:n.spacing(2)}},{props:{variant:"middle",orientation:"vertical"},style:{marginTop:n.spacing(1),marginBottom:n.spacing(1)}},{props:{orientation:"vertical"},style:{height:"100%",borderBottomWidth:0,borderRightWidth:"thin"}},{props:{flexItem:!0},style:{alignSelf:"stretch",height:"auto"}},{props:({ownerState:r})=>!!r.children,style:{display:"flex",textAlign:"center",border:0,borderTopStyle:"solid",borderLeftStyle:"solid","&::before, &::after":{content:'""',alignSelf:"center"}}},{props:({ownerState:r})=>r.children&&r.orientation!=="vertical",style:{"&::before, &::after":{width:"100%",borderTop:`thin solid ${(n.vars||n).palette.divider}`,borderTopStyle:"inherit"}}},{props:({ownerState:r})=>r.orientation==="vertical"&&r.children,style:{flexDirection:"column","&::before, &::after":{height:"100%",borderLeft:`thin solid ${(n.vars||n).palette.divider}`,borderLeftStyle:"inherit"}}},{props:({ownerState:r})=>r.textAlign==="right"&&r.orientation!=="vertical",style:{"&::before":{width:"90%"},"&::after":{width:"10%"}}},{props:({ownerState:r})=>r.textAlign==="left"&&r.orientation!=="vertical",style:{"&::before":{width:"10%"},"&::after":{width:"90%"}}}]}))),b4=ut("span",{name:"MuiDivider",slot:"Wrapper",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.wrapper,o.orientation==="vertical"&&r.wrapperVertical]}})(Gt(({theme:n})=>({display:"inline-block",paddingLeft:`calc(${n.spacing(1)} * 1.2)`,paddingRight:`calc(${n.spacing(1)} * 1.2)`,whiteSpace:"nowrap",variants:[{props:{orientation:"vertical"},style:{paddingTop:`calc(${n.spacing(1)} * 1.2)`,paddingBottom:`calc(${n.spacing(1)} * 1.2)`}}]}))),Nd=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiDivider"}),{absolute:u=!1,children:c,className:d,orientation:p="horizontal",component:h=c||p==="vertical"?"div":"hr",flexItem:m=!1,light:y=!1,role:S=h!=="hr"?"separator":void 0,textAlign:E="center",variant:R="fullWidth",...x}=l,C={...l,absolute:u,component:h,flexItem:m,light:y,orientation:p,role:S,textAlign:E,variant:R},D=y4(C);return T.jsx(v4,{as:h,className:ht(D.root,d),role:S,ref:o,ownerState:C,"aria-orientation":S==="separator"&&(h!=="hr"||p==="vertical")?p:void 0,...x,children:c?T.jsx(b4,{className:D.wrapper,ownerState:C,children:c}):null})});Nd&&(Nd.muiSkipListHighlight=!0);function S4(n){return Dt("MuiFab",n)}const Ev=Mt("MuiFab",["root","primary","secondary","extended","circular","focusVisible","disabled","colorInherit","sizeSmall","sizeMedium","sizeLarge","info","error","warning","success"]),x4=n=>{const{color:r,variant:o,classes:l,size:u}=n,c={root:["root",o,`size${st(u)}`,r==="inherit"?"colorInherit":r]},d=Bt(c,S4,l);return{...l,...d}},C4=ut(ko,{name:"MuiFab",slot:"Root",shouldForwardProp:n=>On(n)||n==="classes",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[o.variant],r[`size${st(o.size)}`],o.color==="inherit"&&r.colorInherit,r[st(o.size)],r[o.color]]}})(Gt(({theme:n})=>{var r,o;return{...n.typography.button,minHeight:36,transition:n.transitions.create(["background-color","box-shadow","border-color"],{duration:n.transitions.duration.short}),borderRadius:"50%",padding:0,minWidth:0,width:56,height:56,zIndex:(n.vars||n).zIndex.fab,boxShadow:(n.vars||n).shadows[6],"&:active":{boxShadow:(n.vars||n).shadows[12]},color:n.vars?n.vars.palette.grey[900]:(o=(r=n.palette).getContrastText)==null?void 0:o.call(r,n.palette.grey[300]),backgroundColor:(n.vars||n).palette.grey[300],"&:hover":{backgroundColor:(n.vars||n).palette.grey.A100,"@media (hover: none)":{backgroundColor:(n.vars||n).palette.grey[300]},textDecoration:"none"},[`&.${Ev.focusVisible}`]:{boxShadow:(n.vars||n).shadows[6]},variants:[{props:{size:"small"},style:{width:40,height:40}},{props:{size:"medium"},style:{width:48,height:48}},{props:{variant:"extended"},style:{borderRadius:48/2,padding:"0 16px",width:"auto",minHeight:"auto",minWidth:48,height:48}},{props:{variant:"extended",size:"small"},style:{width:"auto",padding:"0 8px",borderRadius:34/2,minWidth:34,height:34}},{props:{variant:"extended",size:"medium"},style:{width:"auto",padding:"0 16px",borderRadius:40/2,minWidth:40,height:40}},{props:{color:"inherit"},style:{color:"inherit"}}]}}),Gt(({theme:n})=>({variants:[...Object.entries(n.palette).filter(rn(["dark","contrastText"])).map(([r])=>({props:{color:r},style:{color:(n.vars||n).palette[r].contrastText,backgroundColor:(n.vars||n).palette[r].main,"&:hover":{backgroundColor:(n.vars||n).palette[r].dark,"@media (hover: none)":{backgroundColor:(n.vars||n).palette[r].main}}}}))]})),Gt(({theme:n})=>({[`&.${Ev.disabled}`]:{color:(n.vars||n).palette.action.disabled,boxShadow:(n.vars||n).shadows[0],backgroundColor:(n.vars||n).palette.action.disabledBackground}}))),T4=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiFab"}),{children:u,className:c,color:d="default",component:p="button",disabled:h=!1,disableFocusRipple:m=!1,focusVisibleClassName:y,size:S="large",variant:E="circular",...R}=l,x={...l,color:d,component:p,disabled:h,disableFocusRipple:m,size:S,variant:E},C=x4(x);return T.jsx(C4,{className:ht(C.root,c),component:p,disabled:h,focusRipple:!m,focusVisibleClassName:ht(C.focusVisible,y),ownerState:x,ref:o,...R,classes:C,children:u})}),E4=n=>{const{classes:r,disableUnderline:o,startAdornment:l,endAdornment:u,size:c,hiddenLabel:d,multiline:p}=n,h={root:["root",!o&&"underline",l&&"adornedStart",u&&"adornedEnd",c==="small"&&`size${st(c)}`,d&&"hiddenLabel",p&&"multiline"],input:["input"]},m=Bt(h,tw,r);return{...r,...m}},R4=ut(ku,{shouldForwardProp:n=>On(n)||n==="classes",name:"MuiFilledInput",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[...Nu(n,r),!o.disableUnderline&&r.underline]}})(Gt(({theme:n})=>{const r=n.palette.mode==="light",o=r?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)",l=r?"rgba(0, 0, 0, 0.06)":"rgba(255, 255, 255, 0.09)",u=r?"rgba(0, 0, 0, 0.09)":"rgba(255, 255, 255, 0.13)",c=r?"rgba(0, 0, 0, 0.12)":"rgba(255, 255, 255, 0.12)";return{position:"relative",backgroundColor:n.vars?n.vars.palette.FilledInput.bg:l,borderTopLeftRadius:(n.vars||n).shape.borderRadius,borderTopRightRadius:(n.vars||n).shape.borderRadius,transition:n.transitions.create("background-color",{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut}),"&:hover":{backgroundColor:n.vars?n.vars.palette.FilledInput.hoverBg:u,"@media (hover: none)":{backgroundColor:n.vars?n.vars.palette.FilledInput.bg:l}},[`&.${Or.focused}`]:{backgroundColor:n.vars?n.vars.palette.FilledInput.bg:l},[`&.${Or.disabled}`]:{backgroundColor:n.vars?n.vars.palette.FilledInput.disabledBg:c},variants:[{props:({ownerState:d})=>!d.disableUnderline,style:{"&::after":{left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:n.transitions.create("transform",{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut}),pointerEvents:"none"},[`&.${Or.focused}:after`]:{transform:"scaleX(1) translateX(0)"},[`&.${Or.error}`]:{"&::before, &::after":{borderBottomColor:(n.vars||n).palette.error.main}},"&::before":{borderBottom:`1px solid ${n.vars?`rgba(${n.vars.palette.common.onBackgroundChannel} / ${n.vars.opacity.inputUnderline})`:o}`,left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:n.transitions.create("border-bottom-color",{duration:n.transitions.duration.shorter}),pointerEvents:"none"},[`&:hover:not(.${Or.disabled}, .${Or.error}):before`]:{borderBottom:`1px solid ${(n.vars||n).palette.text.primary}`},[`&.${Or.disabled}:before`]:{borderBottomStyle:"dotted"}}},...Object.entries(n.palette).filter(rn()).map(([d])=>{var p;return{props:{disableUnderline:!1,color:d},style:{"&::after":{borderBottom:`2px solid ${(p=(n.vars||n).palette[d])==null?void 0:p.main}`}}}}),{props:({ownerState:d})=>d.startAdornment,style:{paddingLeft:12}},{props:({ownerState:d})=>d.endAdornment,style:{paddingRight:12}},{props:({ownerState:d})=>d.multiline,style:{padding:"25px 12px 8px"}},{props:({ownerState:d,size:p})=>d.multiline&&p==="small",style:{paddingTop:21,paddingBottom:4}},{props:({ownerState:d})=>d.multiline&&d.hiddenLabel,style:{paddingTop:16,paddingBottom:17}},{props:({ownerState:d})=>d.multiline&&d.hiddenLabel&&d.size==="small",style:{paddingTop:8,paddingBottom:9}}]}})),w4=ut($u,{name:"MuiFilledInput",slot:"Input",overridesResolver:ju})(Gt(({theme:n})=>({paddingTop:25,paddingRight:12,paddingBottom:8,paddingLeft:12,...!n.vars&&{"&:-webkit-autofill":{WebkitBoxShadow:n.palette.mode==="light"?null:"0 0 0 100px #266798 inset",WebkitTextFillColor:n.palette.mode==="light"?null:"#fff",caretColor:n.palette.mode==="light"?null:"#fff",borderTopLeftRadius:"inherit",borderTopRightRadius:"inherit"}},...n.vars&&{"&:-webkit-autofill":{borderTopLeftRadius:"inherit",borderTopRightRadius:"inherit"},[n.getColorSchemeSelector("dark")]:{"&:-webkit-autofill":{WebkitBoxShadow:"0 0 0 100px #266798 inset",WebkitTextFillColor:"#fff",caretColor:"#fff"}}},variants:[{props:{size:"small"},style:{paddingTop:21,paddingBottom:4}},{props:({ownerState:r})=>r.hiddenLabel,style:{paddingTop:16,paddingBottom:17}},{props:({ownerState:r})=>r.startAdornment,style:{paddingLeft:0}},{props:({ownerState:r})=>r.endAdornment,style:{paddingRight:0}},{props:({ownerState:r})=>r.hiddenLabel&&r.size==="small",style:{paddingTop:8,paddingBottom:9}},{props:({ownerState:r})=>r.multiline,style:{paddingTop:0,paddingBottom:0,paddingLeft:0,paddingRight:0}}]}))),vp=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiFilledInput"}),{disableUnderline:u=!1,components:c={},componentsProps:d,fullWidth:p=!1,hiddenLabel:h,inputComponent:m="input",multiline:y=!1,slotProps:S,slots:E={},type:R="text",...x}=l,C={...l,disableUnderline:u,fullWidth:p,inputComponent:m,multiline:y,type:R},D=E4(l),j={root:{ownerState:C},input:{ownerState:C}},U=S??d?nn(j,S??d):j,M=E.root??c.Root??R4,O=E.input??c.Input??w4;return T.jsx(yp,{slots:{root:M,input:O},slotProps:U,fullWidth:p,inputComponent:m,multiline:y,ref:o,type:R,...x,classes:D})});vp.muiName="Input";function A4(n){return Dt("MuiFormControl",n)}Mt("MuiFormControl",["root","marginNone","marginNormal","marginDense","fullWidth","disabled"]);const O4=n=>{const{classes:r,margin:o,fullWidth:l}=n,u={root:["root",o!=="none"&&`margin${st(o)}`,l&&"fullWidth"]};return Bt(u,A4,r)},M4=ut("div",{name:"MuiFormControl",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,r[`margin${st(o.margin)}`],o.fullWidth&&r.fullWidth]}})({display:"inline-flex",flexDirection:"column",position:"relative",minWidth:0,padding:0,margin:0,border:0,verticalAlign:"top",variants:[{props:{margin:"normal"},style:{marginTop:16,marginBottom:8}},{props:{margin:"dense"},style:{marginTop:8,marginBottom:4}},{props:{fullWidth:!0},style:{width:"100%"}}]}),z4=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiFormControl"}),{children:u,className:c,color:d="primary",component:p="div",disabled:h=!1,error:m=!1,focused:y,fullWidth:S=!1,hiddenLabel:E=!1,margin:R="none",required:x=!1,size:C="medium",variant:D="outlined",...j}=l,U={...l,color:d,component:p,disabled:h,error:m,fullWidth:S,hiddenLabel:E,margin:R,required:x,size:C,variant:D},M=O4(U),[O,A]=w.useState(()=>{let F=!1;return u&&w.Children.forEach(u,B=>{if(!ru(B,["Input","Select"]))return;const V=ru(B,["Select"])?B.props.input:B;V&&KR(V.props)&&(F=!0)}),F}),[$,H]=w.useState(()=>{let F=!1;return u&&w.Children.forEach(u,B=>{ru(B,["Input","Select"])&&(gu(B.props,!0)||gu(B.props.inputProps,!0))&&(F=!0)}),F}),[P,K]=w.useState(!1);h&&P&&K(!1);const v=y!==void 0&&!h?y:P;let _;w.useRef(!1);const W=w.useCallback(()=>{H(!0)},[]),Z=w.useCallback(()=>{H(!1)},[]),it=w.useMemo(()=>({adornedStart:O,setAdornedStart:A,color:d,disabled:h,error:m,filled:$,focused:v,fullWidth:S,hiddenLabel:E,size:C,onBlur:()=>{K(!1)},onFocus:()=>{K(!0)},onEmpty:Z,onFilled:W,registerEffect:_,required:x,variant:D}),[O,d,h,m,$,v,S,E,_,Z,W,x,C,D]);return T.jsx(gp.Provider,{value:it,children:T.jsx(M4,{as:p,ownerState:U,className:ht(M.root,c),ref:o,...j,children:u})})});function D4(n){return Dt("MuiFormHelperText",n)}const Rv=Mt("MuiFormHelperText",["root","error","disabled","sizeSmall","sizeMedium","contained","focused","filled","required"]);var wv;const B4=n=>{const{classes:r,contained:o,size:l,disabled:u,error:c,filled:d,focused:p,required:h}=n,m={root:["root",u&&"disabled",c&&"error",l&&`size${st(l)}`,o&&"contained",p&&"focused",d&&"filled",h&&"required"]};return Bt(m,D4,r)},N4=ut("p",{name:"MuiFormHelperText",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.size&&r[`size${st(o.size)}`],o.contained&&r.contained,o.filled&&r.filled]}})(Gt(({theme:n})=>({color:(n.vars||n).palette.text.secondary,...n.typography.caption,textAlign:"left",marginTop:3,marginRight:0,marginBottom:0,marginLeft:0,[`&.${Rv.disabled}`]:{color:(n.vars||n).palette.text.disabled},[`&.${Rv.error}`]:{color:(n.vars||n).palette.error.main},variants:[{props:{size:"small"},style:{marginTop:4}},{props:({ownerState:r})=>r.contained,style:{marginLeft:14,marginRight:14}}]}))),j4=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiFormHelperText"}),{children:u,className:c,component:d="p",disabled:p,error:h,filled:m,focused:y,margin:S,required:E,variant:R,...x}=l,C=Io(),D=Xo({props:l,muiFormControl:C,states:["variant","size","disabled","error","filled","focused","required"]}),j={...l,component:d,contained:D.variant==="filled"||D.variant==="outlined",variant:D.variant,size:D.size,disabled:D.disabled,error:D.error,filled:D.filled,focused:D.focused,required:D.required};delete j.ownerState;const U=B4(j);return T.jsx(N4,{as:d,className:ht(U.root,c),ref:o,...x,ownerState:j,children:u===" "?wv||(wv=T.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"})):u})});function k4(n){return Dt("MuiFormLabel",n)}const ul=Mt("MuiFormLabel",["root","colorSecondary","focused","disabled","error","filled","required","asterisk"]),$4=n=>{const{classes:r,color:o,focused:l,disabled:u,error:c,filled:d,required:p}=n,h={root:["root",`color${st(o)}`,u&&"disabled",c&&"error",d&&"filled",l&&"focused",p&&"required"],asterisk:["asterisk",c&&"error"]};return Bt(h,k4,r)},_4=ut("label",{name:"MuiFormLabel",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.color==="secondary"&&r.colorSecondary,o.filled&&r.filled]}})(Gt(({theme:n})=>({color:(n.vars||n).palette.text.secondary,...n.typography.body1,lineHeight:"1.4375em",padding:0,position:"relative",variants:[...Object.entries(n.palette).filter(rn()).map(([r])=>({props:{color:r},style:{[`&.${ul.focused}`]:{color:(n.vars||n).palette[r].main}}})),{props:{},style:{[`&.${ul.disabled}`]:{color:(n.vars||n).palette.text.disabled},[`&.${ul.error}`]:{color:(n.vars||n).palette.error.main}}}]}))),U4=ut("span",{name:"MuiFormLabel",slot:"Asterisk"})(Gt(({theme:n})=>({[`&.${ul.error}`]:{color:(n.vars||n).palette.error.main}}))),L4=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiFormLabel"}),{children:u,className:c,color:d,component:p="label",disabled:h,error:m,filled:y,focused:S,required:E,...R}=l,x=Io(),C=Xo({props:l,muiFormControl:x,states:["color","required","focused","disabled","error","filled"]}),D={...l,color:C.color||"primary",component:p,disabled:C.disabled,error:C.error,filled:C.filled,focused:C.focused,required:C.required},j=$4(D);return T.jsxs(_4,{as:p,ownerState:D,className:ht(j.root,c),ref:o,...R,children:[u,C.required&&T.jsxs(U4,{ownerState:D,"aria-hidden":!0,className:j.asterisk,children:[" ","*"]})]})}),Tn=kC({createStyledComponent:ut("div",{name:"MuiGrid",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,o.container&&r.container]}}),componentName:"MuiGrid",useThemeProps:n=>Nt({props:n,name:"MuiGrid"}),useTheme:Go});function jd(n){return`scale(${n}, ${n**2})`}const H4={entering:{opacity:1,transform:jd(1)},entered:{opacity:1,transform:"none"}},ud=typeof navigator<"u"&&/^((?!chrome|android).)*(safari|mobile)/i.test(navigator.userAgent)&&/(os |version\/)15(.|_)4/i.test(navigator.userAgent),yu=w.forwardRef(function(r,o){const{addEndListener:l,appear:u=!0,children:c,easing:d,in:p,onEnter:h,onEntered:m,onEntering:y,onExit:S,onExited:E,onExiting:R,style:x,timeout:C="auto",TransitionComponent:D=ca,...j}=r,U=zo(),M=w.useRef(),O=Go(),A=w.useRef(null),$=Pe(A,Yo(c),o),H=F=>B=>{if(F){const V=A.current;B===void 0?F(V):F(V,B)}},P=H(y),K=H((F,B)=>{B0(F);const{duration:V,delay:ot,easing:Q}=du({style:x,timeout:C,easing:d},{mode:"enter"});let z;C==="auto"?(z=O.transitions.getAutoHeightDuration(F.clientHeight),M.current=z):z=V,F.style.transition=[O.transitions.create("opacity",{duration:z,delay:ot}),O.transitions.create("transform",{duration:ud?z:z*.666,delay:ot,easing:Q})].join(","),h&&h(F,B)}),v=H(m),_=H(R),W=H(F=>{const{duration:B,delay:V,easing:ot}=du({style:x,timeout:C,easing:d},{mode:"exit"});let Q;C==="auto"?(Q=O.transitions.getAutoHeightDuration(F.clientHeight),M.current=Q):Q=B,F.style.transition=[O.transitions.create("opacity",{duration:Q,delay:V}),O.transitions.create("transform",{duration:ud?Q:Q*.666,delay:ud?V:V||Q*.333,easing:ot})].join(","),F.style.opacity=0,F.style.transform=jd(.75),S&&S(F)}),Z=H(E),it=F=>{C==="auto"&&U.start(M.current||0,F),l&&l(A.current,F)};return T.jsx(D,{appear:u,in:p,nodeRef:A,onEnter:K,onEntered:v,onEntering:P,onExit:W,onExited:Z,onExiting:_,addEndListener:it,timeout:C==="auto"?null:C,...j,children:(F,{ownerState:B,...V})=>w.cloneElement(c,{style:{opacity:0,transform:jd(.75),visibility:F==="exited"&&!p?"hidden":void 0,...H4[F],...x,...c.props.style},ref:$,...V})})});yu&&(yu.muiSupportAuto=!0);const q4=n=>{const{classes:r,disableUnderline:o}=n,u=Bt({root:["root",!o&&"underline"],input:["input"]},FR,r);return{...r,...u}},P4=ut(ku,{shouldForwardProp:n=>On(n)||n==="classes",name:"MuiInput",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[...Nu(n,r),!o.disableUnderline&&r.underline]}})(Gt(({theme:n})=>{let o=n.palette.mode==="light"?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)";return n.vars&&(o=`rgba(${n.vars.palette.common.onBackgroundChannel} / ${n.vars.opacity.inputUnderline})`),{position:"relative",variants:[{props:({ownerState:l})=>l.formControl,style:{"label + &":{marginTop:16}}},{props:({ownerState:l})=>!l.disableUnderline,style:{"&::after":{left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:n.transitions.create("transform",{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut}),pointerEvents:"none"},[`&.${Qi.focused}:after`]:{transform:"scaleX(1) translateX(0)"},[`&.${Qi.error}`]:{"&::before, &::after":{borderBottomColor:(n.vars||n).palette.error.main}},"&::before":{borderBottom:`1px solid ${o}`,left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:n.transitions.create("border-bottom-color",{duration:n.transitions.duration.shorter}),pointerEvents:"none"},[`&:hover:not(.${Qi.disabled}, .${Qi.error}):before`]:{borderBottom:`2px solid ${(n.vars||n).palette.text.primary}`,"@media (hover: none)":{borderBottom:`1px solid ${o}`}},[`&.${Qi.disabled}:before`]:{borderBottomStyle:"dotted"}}},...Object.entries(n.palette).filter(rn()).map(([l])=>({props:{color:l,disableUnderline:!1},style:{"&::after":{borderBottom:`2px solid ${(n.vars||n).palette[l].main}`}}}))]}})),G4=ut($u,{name:"MuiInput",slot:"Input",overridesResolver:ju})({}),bp=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiInput"}),{disableUnderline:u=!1,components:c={},componentsProps:d,fullWidth:p=!1,inputComponent:h="input",multiline:m=!1,slotProps:y,slots:S={},type:E="text",...R}=l,x=q4(l),D={root:{ownerState:{disableUnderline:u}}},j=y??d?nn(y??d,D):D,U=S.root??c.Root??P4,M=S.input??c.Input??G4;return T.jsx(yp,{slots:{root:U,input:M},slotProps:j,fullWidth:p,inputComponent:h,multiline:m,ref:o,type:E,...R,classes:x})});bp.muiName="Input";function V4(n){return Dt("MuiInputLabel",n)}Mt("MuiInputLabel",["root","focused","disabled","error","required","asterisk","formControl","sizeSmall","shrink","animated","standard","filled","outlined"]);const Y4=n=>{const{classes:r,formControl:o,size:l,shrink:u,disableAnimation:c,variant:d,required:p}=n,h={root:["root",o&&"formControl",!c&&"animated",u&&"shrink",l&&l!=="medium"&&`size${st(l)}`,d],asterisk:[p&&"asterisk"]},m=Bt(h,V4,r);return{...r,...m}},X4=ut(L4,{shouldForwardProp:n=>On(n)||n==="classes",name:"MuiInputLabel",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[{[`& .${ul.asterisk}`]:r.asterisk},r.root,o.formControl&&r.formControl,o.size==="small"&&r.sizeSmall,o.shrink&&r.shrink,!o.disableAnimation&&r.animated,o.focused&&r.focused,r[o.variant]]}})(Gt(({theme:n})=>({display:"block",transformOrigin:"top left",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%",variants:[{props:({ownerState:r})=>r.formControl,style:{position:"absolute",left:0,top:0,transform:"translate(0, 20px) scale(1)"}},{props:{size:"small"},style:{transform:"translate(0, 17px) scale(1)"}},{props:({ownerState:r})=>r.shrink,style:{transform:"translate(0, -1.5px) scale(0.75)",transformOrigin:"top left",maxWidth:"133%"}},{props:({ownerState:r})=>!r.disableAnimation,style:{transition:n.transitions.create(["color","transform","max-width"],{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut})}},{props:{variant:"filled"},style:{zIndex:1,pointerEvents:"none",transform:"translate(12px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"}},{props:{variant:"filled",size:"small"},style:{transform:"translate(12px, 13px) scale(1)"}},{props:({variant:r,ownerState:o})=>r==="filled"&&o.shrink,style:{userSelect:"none",pointerEvents:"auto",transform:"translate(12px, 7px) scale(0.75)",maxWidth:"calc(133% - 24px)"}},{props:({variant:r,ownerState:o,size:l})=>r==="filled"&&o.shrink&&l==="small",style:{transform:"translate(12px, 4px) scale(0.75)"}},{props:{variant:"outlined"},style:{zIndex:1,pointerEvents:"none",transform:"translate(14px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"}},{props:{variant:"outlined",size:"small"},style:{transform:"translate(14px, 9px) scale(1)"}},{props:({variant:r,ownerState:o})=>r==="outlined"&&o.shrink,style:{userSelect:"none",pointerEvents:"auto",maxWidth:"calc(133% - 32px)",transform:"translate(14px, -9px) scale(0.75)"}}]}))),I4=w.forwardRef(function(r,o){const l=Nt({name:"MuiInputLabel",props:r}),{disableAnimation:u=!1,margin:c,shrink:d,variant:p,className:h,...m}=l,y=Io();let S=d;typeof S>"u"&&y&&(S=y.filled||y.focused||y.adornedStart);const E=Xo({props:l,muiFormControl:y,states:["size","variant","required","focused"]}),R={...l,disableAnimation:u,formControl:y,shrink:S,size:E.size,variant:E.variant,required:E.required,focused:E.focused},x=Y4(R);return T.jsx(X4,{"data-shrink":S,ref:o,className:ht(x.root,h),...m,ownerState:R,classes:x})}),kd=w.createContext({});function W4(n){return Dt("MuiList",n)}Mt("MuiList",["root","padding","dense","subheader"]);const K4=n=>{const{classes:r,disablePadding:o,dense:l,subheader:u}=n;return Bt({root:["root",!o&&"padding",l&&"dense",u&&"subheader"]},W4,r)},Q4=ut("ul",{name:"MuiList",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,!o.disablePadding&&r.padding,o.dense&&r.dense,o.subheader&&r.subheader]}})({listStyle:"none",margin:0,padding:0,position:"relative",variants:[{props:({ownerState:n})=>!n.disablePadding,style:{paddingTop:8,paddingBottom:8}},{props:({ownerState:n})=>n.subheader,style:{paddingTop:0}}]}),Z4=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiList"}),{children:u,className:c,component:d="ul",dense:p=!1,disablePadding:h=!1,subheader:m,...y}=l,S=w.useMemo(()=>({dense:p}),[p]),E={...l,component:d,dense:p,disablePadding:h},R=K4(E);return T.jsx(kd.Provider,{value:S,children:T.jsxs(Q4,{as:d,className:ht(R.root,c),ref:o,ownerState:E,...y,children:[m,u]})})}),Av=Mt("MuiListItemIcon",["root","alignItemsFlexStart"]),Ov=Mt("MuiListItemText",["root","multiline","dense","inset","primary","secondary"]);function cd(n,r,o){return n===r?n.firstChild:r&&r.nextElementSibling?r.nextElementSibling:o?null:n.firstChild}function Mv(n,r,o){return n===r?o?n.firstChild:n.lastChild:r&&r.previousElementSibling?r.previousElementSibling:o?null:n.lastChild}function eb(n,r){if(r===void 0)return!0;let o=n.innerText;return o===void 0&&(o=n.textContent),o=o.trim().toLowerCase(),o.length===0?!1:r.repeating?o[0]===r.keys[0]:o.startsWith(r.keys.join(""))}function Zi(n,r,o,l,u,c){let d=!1,p=u(n,r,r?o:!1);for(;p;){if(p===n.firstChild){if(d)return!1;d=!0}const h=l?!1:p.disabled||p.getAttribute("aria-disabled")==="true";if(!p.hasAttribute("tabindex")||!eb(p,c)||h)p=u(n,p,o);else return p.focus(),!0}return!1}const F4=w.forwardRef(function(r,o){const{actions:l,autoFocus:u=!1,autoFocusItem:c=!1,children:d,className:p,disabledItemsFocusable:h=!1,disableListWrap:m=!1,onKeyDown:y,variant:S="selectedMenu",...E}=r,R=w.useRef(null),x=w.useRef({keys:[],repeating:!0,previousKeyMatched:!0,lastTime:null});Wn(()=>{u&&R.current.focus()},[u]),w.useImperativeHandle(l,()=>({adjustStyleForScrollbar:(M,{direction:O})=>{const A=!R.current.style.width;if(M.clientHeight<R.current.clientHeight&&A){const $=`${F0(Ba(M))}px`;R.current.style[O==="rtl"?"paddingLeft":"paddingRight"]=$,R.current.style.width=`calc(100% + ${$})`}return R.current}}),[]);const C=M=>{const O=R.current,A=M.key;if(M.ctrlKey||M.metaKey||M.altKey){y&&y(M);return}const H=wn(O).activeElement;if(A==="ArrowDown")M.preventDefault(),Zi(O,H,m,h,cd);else if(A==="ArrowUp")M.preventDefault(),Zi(O,H,m,h,Mv);else if(A==="Home")M.preventDefault(),Zi(O,null,m,h,cd);else if(A==="End")M.preventDefault(),Zi(O,null,m,h,Mv);else if(A.length===1){const P=x.current,K=A.toLowerCase(),v=performance.now();P.keys.length>0&&(v-P.lastTime>500?(P.keys=[],P.repeating=!0,P.previousKeyMatched=!0):P.repeating&&K!==P.keys[0]&&(P.repeating=!1)),P.lastTime=v,P.keys.push(K);const _=H&&!P.repeating&&eb(H,P);P.previousKeyMatched&&(_||Zi(O,H,!1,h,cd,P))?M.preventDefault():P.previousKeyMatched=!1}y&&y(M)},D=Pe(R,o);let j=-1;w.Children.forEach(d,(M,O)=>{if(!w.isValidElement(M)){j===O&&(j+=1,j>=d.length&&(j=-1));return}M.props.disabled||(S==="selectedMenu"&&M.props.selected||j===-1)&&(j=O),j===O&&(M.props.disabled||M.props.muiSkipListHighlight||M.type.muiSkipListHighlight)&&(j+=1,j>=d.length&&(j=-1))});const U=w.Children.map(d,(M,O)=>{if(O===j){const A={};return c&&(A.autoFocus=!0),M.props.tabIndex===void 0&&S==="selectedMenu"&&(A.tabIndex=0),w.cloneElement(M,A)}return M});return T.jsx(Z4,{role:"menu",ref:D,className:p,onKeyDown:C,tabIndex:u?0:-1,...E,children:U})});function J4(n){return Dt("MuiPopover",n)}Mt("MuiPopover",["root","paper"]);function zv(n,r){let o=0;return typeof r=="number"?o=r:r==="center"?o=n.height/2:r==="bottom"&&(o=n.height),o}function Dv(n,r){let o=0;return typeof r=="number"?o=r:r==="center"?o=n.width/2:r==="right"&&(o=n.width),o}function Bv(n){return[n.horizontal,n.vertical].map(r=>typeof r=="number"?`${r}px`:r).join(" ")}function Zs(n){return typeof n=="function"?n():n}const tA=n=>{const{classes:r}=n;return Bt({root:["root"],paper:["paper"]},J4,r)},eA=ut(J0,{name:"MuiPopover",slot:"Root"})({}),nb=ut(wl,{name:"MuiPopover",slot:"Paper"})({position:"absolute",overflowY:"auto",overflowX:"hidden",minWidth:16,minHeight:16,maxWidth:"calc(100% - 32px)",maxHeight:"calc(100% - 32px)",outline:0}),nA=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiPopover"}),{action:u,anchorEl:c,anchorOrigin:d={vertical:"top",horizontal:"left"},anchorPosition:p,anchorReference:h="anchorEl",children:m,className:y,container:S,elevation:E=8,marginThreshold:R=16,open:x,PaperProps:C={},slots:D={},slotProps:j={},transformOrigin:U={vertical:"top",horizontal:"left"},TransitionComponent:M,transitionDuration:O="auto",TransitionProps:A={},disableScrollLock:$=!1,...H}=l,P=w.useRef(),K={...l,anchorOrigin:d,anchorReference:h,elevation:E,marginThreshold:R,transformOrigin:U,TransitionComponent:M,transitionDuration:O,TransitionProps:A},v=tA(K),_=w.useCallback(()=>{if(h==="anchorPosition")return p;const yt=Zs(c),St=(yt&&yt.nodeType===1?yt:wn(P.current).body).getBoundingClientRect();return{top:St.top+zv(St,d.vertical),left:St.left+Dv(St,d.horizontal)}},[c,d.horizontal,d.vertical,p,h]),W=w.useCallback(yt=>({vertical:zv(yt,U.vertical),horizontal:Dv(yt,U.horizontal)}),[U.horizontal,U.vertical]),Z=w.useCallback(yt=>{const wt={width:yt.offsetWidth,height:yt.offsetHeight},St=W(wt);if(h==="none")return{top:null,left:null,transformOrigin:Bv(St)};const $t=_();let Et=$t.top-St.vertical,Vt=$t.left-St.horizontal;const Te=Et+wt.height,Lt=Vt+wt.width,Kt=Ba(Zs(c)),Qt=Kt.innerHeight-R,Yt=Kt.innerWidth-R;if(R!==null&&Et<R){const _t=Et-R;Et-=_t,St.vertical+=_t}else if(R!==null&&Te>Qt){const _t=Te-Qt;Et-=_t,St.vertical+=_t}if(R!==null&&Vt<R){const _t=Vt-R;Vt-=_t,St.horizontal+=_t}else if(Lt>Yt){const _t=Lt-Yt;Vt-=_t,St.horizontal+=_t}return{top:`${Math.round(Et)}px`,left:`${Math.round(Vt)}px`,transformOrigin:Bv(St)}},[c,h,_,W,R]),[it,F]=w.useState(x),B=w.useCallback(()=>{const yt=P.current;if(!yt)return;const wt=Z(yt);wt.top!==null&&yt.style.setProperty("top",wt.top),wt.left!==null&&(yt.style.left=wt.left),yt.style.transformOrigin=wt.transformOrigin,F(!0)},[Z]);w.useEffect(()=>($&&window.addEventListener("scroll",B),()=>window.removeEventListener("scroll",B)),[c,$,B]);const V=()=>{B()},ot=()=>{F(!1)};w.useEffect(()=>{x&&B()}),w.useImperativeHandle(u,()=>x?{updatePosition:()=>{B()}}:null,[x,B]),w.useEffect(()=>{if(!x)return;const yt=A0(()=>{B()}),wt=Ba(Zs(c));return wt.addEventListener("resize",yt),()=>{yt.clear(),wt.removeEventListener("resize",yt)}},[c,x,B]);let Q=O;const z={slots:{transition:M,...D},slotProps:{transition:A,paper:C,...j}},[X,et]=pe("transition",{elementType:yu,externalForwardedProps:z,ownerState:K,getSlotProps:yt=>({...yt,onEntering:(wt,St)=>{var $t;($t=yt.onEntering)==null||$t.call(yt,wt,St),V()},onExited:wt=>{var St;(St=yt.onExited)==null||St.call(yt,wt),ot()}}),additionalProps:{appear:!0,in:x}});O==="auto"&&!X.muiSupportAuto&&(Q=void 0);const nt=S||(c?wn(Zs(c)).body:void 0),[lt,{slots:ct,slotProps:ft,...Tt}]=pe("root",{ref:o,elementType:eA,externalForwardedProps:{...z,...H},shouldForwardComponentProp:!0,additionalProps:{slots:{backdrop:D.backdrop},slotProps:{backdrop:zT(typeof j.backdrop=="function"?j.backdrop(K):j.backdrop,{invisible:!0})},container:nt,open:x},ownerState:K,className:ht(v.root,y)}),[bt,At]=pe("paper",{ref:P,className:v.paper,elementType:nb,externalForwardedProps:z,shouldForwardComponentProp:!0,additionalProps:{elevation:E,style:it?void 0:{opacity:0}},ownerState:K});return T.jsx(lt,{...Tt,...!Dd(lt)&&{slots:ct,slotProps:ft,disableScrollLock:$},children:T.jsx(X,{...et,timeout:Q,children:T.jsx(bt,{...At,children:m})})})});function aA(n){return Dt("MuiMenu",n)}Mt("MuiMenu",["root","paper","list"]);const rA={vertical:"top",horizontal:"right"},oA={vertical:"top",horizontal:"left"},iA=n=>{const{classes:r}=n;return Bt({root:["root"],paper:["paper"],list:["list"]},aA,r)},lA=ut(nA,{shouldForwardProp:n=>On(n)||n==="classes",name:"MuiMenu",slot:"Root"})({}),sA=ut(nb,{name:"MuiMenu",slot:"Paper"})({maxHeight:"calc(100% - 96px)",WebkitOverflowScrolling:"touch"}),uA=ut(F4,{name:"MuiMenu",slot:"List"})({outline:0}),$d=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiMenu"}),{autoFocus:u=!0,children:c,className:d,disableAutoFocusItem:p=!1,MenuListProps:h={},onClose:m,open:y,PaperProps:S={},PopoverClasses:E,transitionDuration:R="auto",TransitionProps:{onEntering:x,...C}={},variant:D="selectedMenu",slots:j={},slotProps:U={},...M}=l,O=ep(),A={...l,autoFocus:u,disableAutoFocusItem:p,MenuListProps:h,onEntering:x,PaperProps:S,transitionDuration:R,TransitionProps:C,variant:D},$=iA(A),H=u&&!p&&y,P=w.useRef(null),K=(Q,z)=>{P.current&&P.current.adjustStyleForScrollbar(Q,{direction:O?"rtl":"ltr"}),x&&x(Q,z)},v=Q=>{Q.key==="Tab"&&(Q.preventDefault(),m&&m(Q,"tabKeyDown"))};let _=-1;w.Children.map(c,(Q,z)=>{w.isValidElement(Q)&&(Q.props.disabled||(D==="selectedMenu"&&Q.props.selected||_===-1)&&(_=z))});const W={slots:j,slotProps:{list:h,transition:C,paper:S,...U}},Z=I0({elementType:j.root,externalSlotProps:U.root,ownerState:A,className:[$.root,d]}),[it,F]=pe("paper",{className:$.paper,elementType:sA,externalForwardedProps:W,shouldForwardComponentProp:!0,ownerState:A}),[B,V]=pe("list",{className:ht($.list,h.className),elementType:uA,shouldForwardComponentProp:!0,externalForwardedProps:W,getSlotProps:Q=>({...Q,onKeyDown:z=>{var X;v(z),(X=Q.onKeyDown)==null||X.call(Q,z)}}),ownerState:A}),ot=typeof W.slotProps.transition=="function"?W.slotProps.transition(A):W.slotProps.transition;return T.jsx(lA,{onClose:m,anchorOrigin:{vertical:"bottom",horizontal:O?"right":"left"},transformOrigin:O?rA:oA,slots:{root:j.root,paper:it,backdrop:j.backdrop,...j.transition&&{transition:j.transition}},slotProps:{root:Z,paper:F,backdrop:typeof U.backdrop=="function"?U.backdrop(A):U.backdrop,transition:{...ot,onEntering:(...Q)=>{var z;K(...Q),(z=ot==null?void 0:ot.onEntering)==null||z.call(ot,...Q)}}},open:y,ref:o,transitionDuration:R,ownerState:A,...M,classes:E,children:T.jsx(B,{actions:P,autoFocus:u&&(_===-1||p),autoFocusItem:H,variant:D,...V,children:c})})});function cA(n){return Dt("MuiMenuItem",n)}const Fi=Mt("MuiMenuItem",["root","focusVisible","dense","disabled","divider","gutters","selected"]),fA=(n,r)=>{const{ownerState:o}=n;return[r.root,o.dense&&r.dense,o.divider&&r.divider,!o.disableGutters&&r.gutters]},dA=n=>{const{disabled:r,dense:o,divider:l,disableGutters:u,selected:c,classes:d}=n,h=Bt({root:["root",o&&"dense",r&&"disabled",!u&&"gutters",l&&"divider",c&&"selected"]},cA,d);return{...d,...h}},pA=ut(ko,{shouldForwardProp:n=>On(n)||n==="classes",name:"MuiMenuItem",slot:"Root",overridesResolver:fA})(Gt(({theme:n})=>({...n.typography.body1,display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minHeight:48,paddingTop:6,paddingBottom:6,boxSizing:"border-box",whiteSpace:"nowrap","&:hover":{textDecoration:"none",backgroundColor:(n.vars||n).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${Fi.selected}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / ${n.vars.palette.action.selectedOpacity})`:ye(n.palette.primary.main,n.palette.action.selectedOpacity),[`&.${Fi.focusVisible}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.focusOpacity}))`:ye(n.palette.primary.main,n.palette.action.selectedOpacity+n.palette.action.focusOpacity)}},[`&.${Fi.selected}:hover`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.hoverOpacity}))`:ye(n.palette.primary.main,n.palette.action.selectedOpacity+n.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / ${n.vars.palette.action.selectedOpacity})`:ye(n.palette.primary.main,n.palette.action.selectedOpacity)}},[`&.${Fi.focusVisible}`]:{backgroundColor:(n.vars||n).palette.action.focus},[`&.${Fi.disabled}`]:{opacity:(n.vars||n).palette.action.disabledOpacity},[`& + .${Tv.root}`]:{marginTop:n.spacing(1),marginBottom:n.spacing(1)},[`& + .${Tv.inset}`]:{marginLeft:52},[`& .${Ov.root}`]:{marginTop:0,marginBottom:0},[`& .${Ov.inset}`]:{paddingLeft:36},[`& .${Av.root}`]:{minWidth:36},variants:[{props:({ownerState:r})=>!r.disableGutters,style:{paddingLeft:16,paddingRight:16}},{props:({ownerState:r})=>r.divider,style:{borderBottom:`1px solid ${(n.vars||n).palette.divider}`,backgroundClip:"padding-box"}},{props:({ownerState:r})=>!r.dense,style:{[n.breakpoints.up("sm")]:{minHeight:"auto"}}},{props:({ownerState:r})=>r.dense,style:{minHeight:32,paddingTop:4,paddingBottom:4,...n.typography.body2,[`& .${Av.root} svg`]:{fontSize:"1.25rem"}}}]}))),Oo=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiMenuItem"}),{autoFocus:u=!1,component:c="li",dense:d=!1,divider:p=!1,disableGutters:h=!1,focusVisibleClassName:m,role:y="menuitem",tabIndex:S,className:E,...R}=l,x=w.useContext(kd),C=w.useMemo(()=>({dense:d||x.dense||!1,disableGutters:h}),[x.dense,d,h]),D=w.useRef(null);Wn(()=>{u&&D.current&&D.current.focus()},[u]);const j={...l,dense:C.dense,divider:p,disableGutters:h},U=dA(l),M=Pe(D,o);let O;return l.disabled||(O=S!==void 0?S:-1),T.jsx(kd.Provider,{value:C,children:T.jsx(pA,{ref:M,role:y,tabIndex:O,component:c,focusVisibleClassName:ht(U.focusVisible,m),className:ht(U.root,E),...R,ownerState:j,classes:U})})});function hA(n){return Dt("MuiNativeSelect",n)}const Sp=Mt("MuiNativeSelect",["root","select","multiple","filled","outlined","standard","disabled","icon","iconOpen","iconFilled","iconOutlined","iconStandard","nativeInput","error"]),mA=n=>{const{classes:r,variant:o,disabled:l,multiple:u,open:c,error:d}=n,p={select:["select",o,l&&"disabled",u&&"multiple",d&&"error"],icon:["icon",`icon${st(o)}`,c&&"iconOpen",l&&"disabled"]};return Bt(p,hA,r)},ab=ut("select")(({theme:n})=>({MozAppearance:"none",WebkitAppearance:"none",userSelect:"none",borderRadius:0,cursor:"pointer","&:focus":{borderRadius:0},[`&.${Sp.disabled}`]:{cursor:"default"},"&[multiple]":{height:"auto"},"&:not([multiple]) option, &:not([multiple]) optgroup":{backgroundColor:(n.vars||n).palette.background.paper},variants:[{props:({ownerState:r})=>r.variant!=="filled"&&r.variant!=="outlined",style:{"&&&":{paddingRight:24,minWidth:16}}},{props:{variant:"filled"},style:{"&&&":{paddingRight:32}}},{props:{variant:"outlined"},style:{borderRadius:(n.vars||n).shape.borderRadius,"&:focus":{borderRadius:(n.vars||n).shape.borderRadius},"&&&":{paddingRight:32}}}]})),gA=ut(ab,{name:"MuiNativeSelect",slot:"Select",shouldForwardProp:On,overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.select,r[o.variant],o.error&&r.error,{[`&.${Sp.multiple}`]:r.multiple}]}})({}),rb=ut("svg")(({theme:n})=>({position:"absolute",right:0,top:"calc(50% - .5em)",pointerEvents:"none",color:(n.vars||n).palette.action.active,[`&.${Sp.disabled}`]:{color:(n.vars||n).palette.action.disabled},variants:[{props:({ownerState:r})=>r.open,style:{transform:"rotate(180deg)"}},{props:{variant:"filled"},style:{right:7}},{props:{variant:"outlined"},style:{right:7}}]})),yA=ut(rb,{name:"MuiNativeSelect",slot:"Icon",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.icon,o.variant&&r[`icon${st(o.variant)}`],o.open&&r.iconOpen]}})({}),vA=w.forwardRef(function(r,o){const{className:l,disabled:u,error:c,IconComponent:d,inputRef:p,variant:h="standard",...m}=r,y={...r,disabled:u,variant:h,error:c},S=mA(y);return T.jsxs(w.Fragment,{children:[T.jsx(gA,{ownerState:y,className:ht(S.select,l),disabled:u,ref:p||o,...m}),r.multiple?null:T.jsx(yA,{as:d,ownerState:y,className:S.icon})]})});var Nv;const bA=ut("fieldset",{shouldForwardProp:On})({textAlign:"left",position:"absolute",bottom:0,right:0,top:-5,left:0,margin:0,padding:"0 8px",pointerEvents:"none",borderRadius:"inherit",borderStyle:"solid",borderWidth:1,overflow:"hidden",minWidth:"0%"}),SA=ut("legend",{shouldForwardProp:On})(Gt(({theme:n})=>({float:"unset",width:"auto",overflow:"hidden",variants:[{props:({ownerState:r})=>!r.withLabel,style:{padding:0,lineHeight:"11px",transition:n.transitions.create("width",{duration:150,easing:n.transitions.easing.easeOut})}},{props:({ownerState:r})=>r.withLabel,style:{display:"block",padding:0,height:11,fontSize:"0.75em",visibility:"hidden",maxWidth:.01,transition:n.transitions.create("max-width",{duration:50,easing:n.transitions.easing.easeOut}),whiteSpace:"nowrap","& > span":{paddingLeft:5,paddingRight:5,display:"inline-block",opacity:0,visibility:"visible"}}},{props:({ownerState:r})=>r.withLabel&&r.notched,style:{maxWidth:"100%",transition:n.transitions.create("max-width",{duration:100,easing:n.transitions.easing.easeOut,delay:50})}}]})));function xA(n){const{children:r,classes:o,className:l,label:u,notched:c,...d}=n,p=u!=null&&u!=="",h={...n,notched:c,withLabel:p};return T.jsx(bA,{"aria-hidden":!0,className:l,ownerState:h,...d,children:T.jsx(SA,{ownerState:h,children:p?T.jsx("span",{children:u}):Nv||(Nv=T.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"}))})})}const CA=n=>{const{classes:r}=n,l=Bt({root:["root"],notchedOutline:["notchedOutline"],input:["input"]},JR,r);return{...r,...l}},TA=ut(ku,{shouldForwardProp:n=>On(n)||n==="classes",name:"MuiOutlinedInput",slot:"Root",overridesResolver:Nu})(Gt(({theme:n})=>{const r=n.palette.mode==="light"?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)";return{position:"relative",borderRadius:(n.vars||n).shape.borderRadius,[`&:hover .${ea.notchedOutline}`]:{borderColor:(n.vars||n).palette.text.primary},"@media (hover: none)":{[`&:hover .${ea.notchedOutline}`]:{borderColor:n.vars?`rgba(${n.vars.palette.common.onBackgroundChannel} / 0.23)`:r}},[`&.${ea.focused} .${ea.notchedOutline}`]:{borderWidth:2},variants:[...Object.entries(n.palette).filter(rn()).map(([o])=>({props:{color:o},style:{[`&.${ea.focused} .${ea.notchedOutline}`]:{borderColor:(n.vars||n).palette[o].main}}})),{props:{},style:{[`&.${ea.error} .${ea.notchedOutline}`]:{borderColor:(n.vars||n).palette.error.main},[`&.${ea.disabled} .${ea.notchedOutline}`]:{borderColor:(n.vars||n).palette.action.disabled}}},{props:({ownerState:o})=>o.startAdornment,style:{paddingLeft:14}},{props:({ownerState:o})=>o.endAdornment,style:{paddingRight:14}},{props:({ownerState:o})=>o.multiline,style:{padding:"16.5px 14px"}},{props:({ownerState:o,size:l})=>o.multiline&&l==="small",style:{padding:"8.5px 14px"}}]}})),EA=ut(xA,{name:"MuiOutlinedInput",slot:"NotchedOutline"})(Gt(({theme:n})=>{const r=n.palette.mode==="light"?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)";return{borderColor:n.vars?`rgba(${n.vars.palette.common.onBackgroundChannel} / 0.23)`:r}})),RA=ut($u,{name:"MuiOutlinedInput",slot:"Input",overridesResolver:ju})(Gt(({theme:n})=>({padding:"16.5px 14px",...!n.vars&&{"&:-webkit-autofill":{WebkitBoxShadow:n.palette.mode==="light"?null:"0 0 0 100px #266798 inset",WebkitTextFillColor:n.palette.mode==="light"?null:"#fff",caretColor:n.palette.mode==="light"?null:"#fff",borderRadius:"inherit"}},...n.vars&&{"&:-webkit-autofill":{borderRadius:"inherit"},[n.getColorSchemeSelector("dark")]:{"&:-webkit-autofill":{WebkitBoxShadow:"0 0 0 100px #266798 inset",WebkitTextFillColor:"#fff",caretColor:"#fff"}}},variants:[{props:{size:"small"},style:{padding:"8.5px 14px"}},{props:({ownerState:r})=>r.multiline,style:{padding:0}},{props:({ownerState:r})=>r.startAdornment,style:{paddingLeft:0}},{props:({ownerState:r})=>r.endAdornment,style:{paddingRight:0}}]}))),xp=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiOutlinedInput"}),{components:u={},fullWidth:c=!1,inputComponent:d="input",label:p,multiline:h=!1,notched:m,slots:y={},slotProps:S={},type:E="text",...R}=l,x=CA(l),C=Io(),D=Xo({props:l,muiFormControl:C,states:["color","disabled","error","focused","hiddenLabel","size","required"]}),j={...l,color:D.color||"primary",disabled:D.disabled,error:D.error,focused:D.focused,formControl:C,fullWidth:c,hiddenLabel:D.hiddenLabel,multiline:h,size:D.size,type:E},U=y.root??u.Root??TA,M=y.input??u.Input??RA,[O,A]=pe("notchedOutline",{elementType:EA,className:x.notchedOutline,shouldForwardComponentProp:!0,ownerState:j,externalForwardedProps:{slots:y,slotProps:S},additionalProps:{label:p!=null&&p!==""&&D.required?T.jsxs(w.Fragment,{children:[p," ","*"]}):p}});return T.jsx(yp,{slots:{root:U,input:M},slotProps:S,renderSuffix:$=>T.jsx(O,{...A,notched:typeof m<"u"?m:!!($.startAdornment||$.filled||$.focused)}),fullWidth:c,inputComponent:d,multiline:h,ref:o,type:E,...R,classes:{...x,notchedOutline:null}})});xp.muiName="Input";function ob(n){return Dt("MuiSelect",n)}const Ji=Mt("MuiSelect",["root","select","multiple","filled","outlined","standard","disabled","focused","icon","iconOpen","iconFilled","iconOutlined","iconStandard","nativeInput","error"]);var jv;const wA=ut(ab,{name:"MuiSelect",slot:"Select",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[{[`&.${Ji.select}`]:r.select},{[`&.${Ji.select}`]:r[o.variant]},{[`&.${Ji.error}`]:r.error},{[`&.${Ji.multiple}`]:r.multiple}]}})({[`&.${Ji.select}`]:{height:"auto",minHeight:"1.4375em",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden"}}),AA=ut(rb,{name:"MuiSelect",slot:"Icon",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.icon,o.variant&&r[`icon${st(o.variant)}`],o.open&&r.iconOpen]}})({}),OA=ut("input",{shouldForwardProp:n=>R0(n)&&n!=="classes",name:"MuiSelect",slot:"NativeInput"})({bottom:0,left:0,position:"absolute",opacity:0,pointerEvents:"none",width:"100%",boxSizing:"border-box"});function kv(n,r){return typeof r=="object"&&r!==null?n===r:String(n)===String(r)}function MA(n){return n==null||typeof n=="string"&&!n.trim()}const zA=n=>{const{classes:r,variant:o,disabled:l,multiple:u,open:c,error:d}=n,p={select:["select",o,l&&"disabled",u&&"multiple",d&&"error"],icon:["icon",`icon${st(o)}`,c&&"iconOpen",l&&"disabled"],nativeInput:["nativeInput"]};return Bt(p,ob,r)},DA=w.forwardRef(function(r,o){var Zt;const{"aria-describedby":l,"aria-label":u,autoFocus:c,autoWidth:d,children:p,className:h,defaultOpen:m,defaultValue:y,disabled:S,displayEmpty:E,error:R=!1,IconComponent:x,inputRef:C,labelId:D,MenuProps:j={},multiple:U,name:M,onBlur:O,onChange:A,onClose:$,onFocus:H,onOpen:P,open:K,readOnly:v,renderValue:_,required:W,SelectDisplayProps:Z={},tabIndex:it,type:F,value:B,variant:V="standard",...ot}=r,[Q,z]=Cd({controlled:B,default:y,name:"Select"}),[X,et]=Cd({controlled:K,default:m,name:"Select"}),nt=w.useRef(null),lt=w.useRef(null),[ct,ft]=w.useState(null),{current:Tt}=w.useRef(K!=null),[bt,At]=w.useState(),yt=Pe(o,C),wt=w.useCallback(gt=>{lt.current=gt,gt&&ft(gt)},[]),St=ct==null?void 0:ct.parentNode;w.useImperativeHandle(yt,()=>({focus:()=>{lt.current.focus()},node:nt.current,value:Q}),[Q]),w.useEffect(()=>{m&&X&&ct&&!Tt&&(At(d?null:St.clientWidth),lt.current.focus())},[ct,d]),w.useEffect(()=>{c&&lt.current.focus()},[c]),w.useEffect(()=>{if(!D)return;const gt=wn(lt.current).getElementById(D);if(gt){const Xt=()=>{getSelection().isCollapsed&&lt.current.focus()};return gt.addEventListener("click",Xt),()=>{gt.removeEventListener("click",Xt)}}},[D]);const $t=(gt,Xt)=>{gt?P&&P(Xt):$&&$(Xt),Tt||(At(d?null:St.clientWidth),et(gt))},Et=gt=>{gt.button===0&&(gt.preventDefault(),lt.current.focus(),$t(!0,gt))},Vt=gt=>{$t(!1,gt)},Te=w.Children.toArray(p),Lt=gt=>{const Xt=Te.find(be=>be.props.value===gt.target.value);Xt!==void 0&&(z(Xt.props.value),A&&A(gt,Xt))},Kt=gt=>Xt=>{let be;if(Xt.currentTarget.hasAttribute("tabindex")){if(U){be=Array.isArray(Q)?Q.slice():[];const mn=Q.indexOf(gt.props.value);mn===-1?be.push(gt.props.value):be.splice(mn,1)}else be=gt.props.value;if(gt.props.onClick&&gt.props.onClick(Xt),Q!==be&&(z(be),A)){const mn=Xt.nativeEvent||Xt,ur=new mn.constructor(mn.type,mn);Object.defineProperty(ur,"target",{writable:!0,value:{value:be,name:M}}),A(ur,gt)}U||$t(!1,Xt)}},Qt=gt=>{v||[" ","ArrowUp","ArrowDown","Enter"].includes(gt.key)&&(gt.preventDefault(),$t(!0,gt))},Yt=ct!==null&&X,_t=gt=>{!Yt&&O&&(Object.defineProperty(gt,"target",{writable:!0,value:{value:Q,name:M}}),O(gt))};delete ot["aria-invalid"];let dt,Ue;const le=[];let Ze=!1;(gu({value:Q})||E)&&(_?dt=_(Q):Ze=!0);const Le=Te.map(gt=>{if(!w.isValidElement(gt))return null;let Xt;if(U){if(!Array.isArray(Q))throw new Error(za(2));Xt=Q.some(be=>kv(be,gt.props.value)),Xt&&Ze&&le.push(gt.props.children)}else Xt=kv(Q,gt.props.value),Xt&&Ze&&(Ue=gt.props.children);return w.cloneElement(gt,{"aria-selected":Xt?"true":"false",onClick:Kt(gt),onKeyUp:be=>{be.key===" "&&be.preventDefault(),gt.props.onKeyUp&&gt.props.onKeyUp(be)},role:"option",selected:Xt,value:void 0,"data-value":gt.props.value})});Ze&&(U?le.length===0?dt=null:dt=le.reduce((gt,Xt,be)=>(gt.push(Xt),be<le.length-1&&gt.push(", "),gt),[]):dt=Ue);let ne=bt;!d&&Tt&&ct&&(ne=St.clientWidth);let Ee;typeof it<"u"?Ee=it:Ee=S?null:0;const ae=Z.id||(M?`mui-component-select-${M}`:void 0),se={...r,variant:V,value:Q,open:Yt,error:R},mt=zA(se),we={...j.PaperProps,...(Zt=j.slotProps)==null?void 0:Zt.paper},re=Vo();return T.jsxs(w.Fragment,{children:[T.jsx(wA,{as:"div",ref:wt,tabIndex:Ee,role:"combobox","aria-controls":Yt?re:void 0,"aria-disabled":S?"true":void 0,"aria-expanded":Yt?"true":"false","aria-haspopup":"listbox","aria-label":u,"aria-labelledby":[D,ae].filter(Boolean).join(" ")||void 0,"aria-describedby":l,"aria-required":W?"true":void 0,"aria-invalid":R?"true":void 0,onKeyDown:Qt,onMouseDown:S||v?null:Et,onBlur:_t,onFocus:H,...Z,ownerState:se,className:ht(Z.className,mt.select,h),id:ae,children:MA(dt)?jv||(jv=T.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"})):dt}),T.jsx(OA,{"aria-invalid":R,value:Array.isArray(Q)?Q.join(","):Q,name:M,ref:nt,"aria-hidden":!0,onChange:Lt,tabIndex:-1,disabled:S,className:mt.nativeInput,autoFocus:c,required:W,...ot,ownerState:se}),T.jsx(AA,{as:x,className:mt.icon,ownerState:se}),T.jsx($d,{id:`menu-${M||""}`,anchorEl:St,open:Yt,onClose:Vt,anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"},...j,slotProps:{...j.slotProps,list:{"aria-labelledby":D,role:"listbox","aria-multiselectable":U?"true":void 0,disableListWrap:!0,id:re,...j.MenuListProps},paper:{...we,style:{minWidth:ne,...we!=null?we.style:null}}},children:Le})]})}),BA=n=>{const{classes:r}=n,l=Bt({root:["root"]},ob,r);return{...r,...l}},Cp={name:"MuiSelect",slot:"Root",shouldForwardProp:n=>On(n)&&n!=="variant"},NA=ut(bp,Cp)(""),jA=ut(xp,Cp)(""),kA=ut(vp,Cp)(""),ib=w.forwardRef(function(r,o){const l=Nt({name:"MuiSelect",props:r}),{autoWidth:u=!1,children:c,classes:d={},className:p,defaultOpen:h=!1,displayEmpty:m=!1,IconComponent:y=ew,id:S,input:E,inputProps:R,label:x,labelId:C,MenuProps:D,multiple:j=!1,native:U=!1,onClose:M,onOpen:O,open:A,renderValue:$,SelectDisplayProps:H,variant:P="outlined",...K}=l,v=U?vA:DA,_=Io(),W=Xo({props:l,muiFormControl:_,states:["variant","error"]}),Z=W.variant||P,it={...l,variant:Z,classes:d},F=BA(it),{root:B,...V}=F,ot=E||{standard:T.jsx(NA,{ownerState:it}),outlined:T.jsx(jA,{label:x,ownerState:it}),filled:T.jsx(kA,{ownerState:it})}[Z],Q=Pe(o,Yo(ot));return T.jsx(w.Fragment,{children:w.cloneElement(ot,{inputComponent:v,inputProps:{children:c,error:W.error,IconComponent:y,variant:Z,type:void 0,multiple:j,...U?{id:S}:{autoWidth:u,defaultOpen:h,displayEmpty:m,labelId:C,MenuProps:D,onClose:M,onOpen:O,open:A,renderValue:$,SelectDisplayProps:{id:S,...H}},...R,classes:R?nn(V,R.classes):V,...E?E.props.inputProps:{}},...(j&&U||m)&&Z==="outlined"?{notched:!0}:{},ref:Q,className:ht(ot.props.className,p,F.root),...!E&&{variant:Z},...K})})});ib.muiName="Select";function $A(n){return Dt("MuiTooltip",n)}const De=Mt("MuiTooltip",["popper","popperInteractive","popperArrow","popperClose","tooltip","tooltipArrow","touch","tooltipPlacementLeft","tooltipPlacementRight","tooltipPlacementTop","tooltipPlacementBottom","arrow"]);function _A(n){return Math.round(n*1e5)/1e5}const UA=n=>{const{classes:r,disableInteractive:o,arrow:l,touch:u,placement:c}=n,d={popper:["popper",!o&&"popperInteractive",l&&"popperArrow"],tooltip:["tooltip",l&&"tooltipArrow",u&&"touch",`tooltipPlacement${st(c.split("-")[0])}`],arrow:["arrow"]};return Bt(d,$A,r)},LA=ut(K0,{name:"MuiTooltip",slot:"Popper",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.popper,!o.disableInteractive&&r.popperInteractive,o.arrow&&r.popperArrow,!o.open&&r.popperClose]}})(Gt(({theme:n})=>({zIndex:(n.vars||n).zIndex.tooltip,pointerEvents:"none",variants:[{props:({ownerState:r})=>!r.disableInteractive,style:{pointerEvents:"auto"}},{props:({open:r})=>!r,style:{pointerEvents:"none"}},{props:({ownerState:r})=>r.arrow,style:{[`&[data-popper-placement*="bottom"] .${De.arrow}`]:{top:0,marginTop:"-0.71em","&::before":{transformOrigin:"0 100%"}},[`&[data-popper-placement*="top"] .${De.arrow}`]:{bottom:0,marginBottom:"-0.71em","&::before":{transformOrigin:"100% 0"}},[`&[data-popper-placement*="right"] .${De.arrow}`]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"100% 100%"}},[`&[data-popper-placement*="left"] .${De.arrow}`]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"0 0"}}}},{props:({ownerState:r})=>r.arrow&&!r.isRtl,style:{[`&[data-popper-placement*="right"] .${De.arrow}`]:{left:0,marginLeft:"-0.71em"}}},{props:({ownerState:r})=>r.arrow&&!!r.isRtl,style:{[`&[data-popper-placement*="right"] .${De.arrow}`]:{right:0,marginRight:"-0.71em"}}},{props:({ownerState:r})=>r.arrow&&!r.isRtl,style:{[`&[data-popper-placement*="left"] .${De.arrow}`]:{right:0,marginRight:"-0.71em"}}},{props:({ownerState:r})=>r.arrow&&!!r.isRtl,style:{[`&[data-popper-placement*="left"] .${De.arrow}`]:{left:0,marginLeft:"-0.71em"}}}]}))),HA=ut("div",{name:"MuiTooltip",slot:"Tooltip",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.tooltip,o.touch&&r.touch,o.arrow&&r.tooltipArrow,r[`tooltipPlacement${st(o.placement.split("-")[0])}`]]}})(Gt(({theme:n})=>({backgroundColor:n.vars?n.vars.palette.Tooltip.bg:ye(n.palette.grey[700],.92),borderRadius:(n.vars||n).shape.borderRadius,color:(n.vars||n).palette.common.white,fontFamily:n.typography.fontFamily,padding:"4px 8px",fontSize:n.typography.pxToRem(11),maxWidth:300,margin:2,wordWrap:"break-word",fontWeight:n.typography.fontWeightMedium,[`.${De.popper}[data-popper-placement*="left"] &`]:{transformOrigin:"right center"},[`.${De.popper}[data-popper-placement*="right"] &`]:{transformOrigin:"left center"},[`.${De.popper}[data-popper-placement*="top"] &`]:{transformOrigin:"center bottom",marginBottom:"14px"},[`.${De.popper}[data-popper-placement*="bottom"] &`]:{transformOrigin:"center top",marginTop:"14px"},variants:[{props:({ownerState:r})=>r.arrow,style:{position:"relative",margin:0}},{props:({ownerState:r})=>r.touch,style:{padding:"8px 16px",fontSize:n.typography.pxToRem(14),lineHeight:`${_A(16/14)}em`,fontWeight:n.typography.fontWeightRegular}},{props:({ownerState:r})=>!r.isRtl,style:{[`.${De.popper}[data-popper-placement*="left"] &`]:{marginRight:"14px"},[`.${De.popper}[data-popper-placement*="right"] &`]:{marginLeft:"14px"}}},{props:({ownerState:r})=>!r.isRtl&&r.touch,style:{[`.${De.popper}[data-popper-placement*="left"] &`]:{marginRight:"24px"},[`.${De.popper}[data-popper-placement*="right"] &`]:{marginLeft:"24px"}}},{props:({ownerState:r})=>!!r.isRtl,style:{[`.${De.popper}[data-popper-placement*="left"] &`]:{marginLeft:"14px"},[`.${De.popper}[data-popper-placement*="right"] &`]:{marginRight:"14px"}}},{props:({ownerState:r})=>!!r.isRtl&&r.touch,style:{[`.${De.popper}[data-popper-placement*="left"] &`]:{marginLeft:"24px"},[`.${De.popper}[data-popper-placement*="right"] &`]:{marginRight:"24px"}}},{props:({ownerState:r})=>r.touch,style:{[`.${De.popper}[data-popper-placement*="top"] &`]:{marginBottom:"24px"}}},{props:({ownerState:r})=>r.touch,style:{[`.${De.popper}[data-popper-placement*="bottom"] &`]:{marginTop:"24px"}}}]}))),qA=ut("span",{name:"MuiTooltip",slot:"Arrow"})(Gt(({theme:n})=>({overflow:"hidden",position:"absolute",width:"1em",height:"0.71em",boxSizing:"border-box",color:n.vars?n.vars.palette.Tooltip.bg:ye(n.palette.grey[700],.9),"&::before":{content:'""',margin:"auto",display:"block",width:"100%",height:"100%",backgroundColor:"currentColor",transform:"rotate(45deg)"}})));let Fs=!1;const $v=new Du;let tl={x:0,y:0};function Js(n,r){return(o,...l)=>{r&&r(o,...l),n(o,...l)}}const fd=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiTooltip"}),{arrow:u=!1,children:c,classes:d,components:p={},componentsProps:h={},describeChild:m=!1,disableFocusListener:y=!1,disableHoverListener:S=!1,disableInteractive:E=!1,disableTouchListener:R=!1,enterDelay:x=100,enterNextDelay:C=0,enterTouchDelay:D=700,followCursor:j=!1,id:U,leaveDelay:M=0,leaveTouchDelay:O=1500,onClose:A,onOpen:$,open:H,placement:P="bottom",PopperComponent:K,PopperProps:v={},slotProps:_={},slots:W={},title:Z,TransitionComponent:it,TransitionProps:F,...B}=l,V=w.isValidElement(c)?c:T.jsx("span",{children:c}),ot=Go(),Q=ep(),[z,X]=w.useState(),[et,nt]=w.useState(null),lt=w.useRef(!1),ct=E||j,ft=zo(),Tt=zo(),bt=zo(),At=zo(),[yt,wt]=Cd({controlled:H,default:!1,name:"Tooltip",state:"open"});let St=yt;const $t=Vo(U),Et=w.useRef(),Vt=Ma(()=>{Et.current!==void 0&&(document.body.style.WebkitUserSelect=Et.current,Et.current=void 0),At.clear()});w.useEffect(()=>Vt,[Vt]);const Te=Ot=>{$v.clear(),Fs=!0,wt(!0),$&&!St&&$(Ot)},Lt=Ma(Ot=>{$v.start(800+M,()=>{Fs=!1}),wt(!1),A&&St&&A(Ot),ft.start(ot.transitions.duration.shortest,()=>{lt.current=!1})}),Kt=Ot=>{lt.current&&Ot.type!=="touchstart"||(z&&z.removeAttribute("title"),Tt.clear(),bt.clear(),x||Fs&&C?Tt.start(Fs?C:x,()=>{Te(Ot)}):Te(Ot))},Qt=Ot=>{Tt.clear(),bt.start(M,()=>{Lt(Ot)})},[,Yt]=w.useState(!1),_t=Ot=>{pu(Ot.target)||(Yt(!1),Qt(Ot))},dt=Ot=>{z||X(Ot.currentTarget),pu(Ot.target)&&(Yt(!0),Kt(Ot))},Ue=Ot=>{lt.current=!0;const on=V.props;on.onTouchStart&&on.onTouchStart(Ot)},le=Ot=>{Ue(Ot),bt.clear(),ft.clear(),Vt(),Et.current=document.body.style.WebkitUserSelect,document.body.style.WebkitUserSelect="none",At.start(D,()=>{document.body.style.WebkitUserSelect=Et.current,Kt(Ot)})},Ze=Ot=>{V.props.onTouchEnd&&V.props.onTouchEnd(Ot),Vt(),bt.start(O,()=>{Lt(Ot)})};w.useEffect(()=>{if(!St)return;function Ot(on){on.key==="Escape"&&Lt(on)}return document.addEventListener("keydown",Ot),()=>{document.removeEventListener("keydown",Ot)}},[Lt,St]);const Le=Pe(Yo(V),X,o);!Z&&Z!==0&&(St=!1);const ne=w.useRef(),Ee=Ot=>{const on=V.props;on.onMouseMove&&on.onMouseMove(Ot),tl={x:Ot.clientX,y:Ot.clientY},ne.current&&ne.current.update()},ae={},se=typeof Z=="string";m?(ae.title=!St&&se&&!S?Z:null,ae["aria-describedby"]=St?$t:null):(ae["aria-label"]=se?Z:null,ae["aria-labelledby"]=St&&!se?$t:null);const mt={...ae,...B,...V.props,className:ht(B.className,V.props.className),onTouchStart:Ue,ref:Le,...j?{onMouseMove:Ee}:{}},we={};R||(mt.onTouchStart=le,mt.onTouchEnd=Ze),S||(mt.onMouseOver=Js(Kt,mt.onMouseOver),mt.onMouseLeave=Js(Qt,mt.onMouseLeave),ct||(we.onMouseOver=Kt,we.onMouseLeave=Qt)),y||(mt.onFocus=Js(dt,mt.onFocus),mt.onBlur=Js(_t,mt.onBlur),ct||(we.onFocus=dt,we.onBlur=_t));const re={...l,isRtl:Q,arrow:u,disableInteractive:ct,placement:P,PopperComponentProp:K,touch:lt.current},Zt=typeof _.popper=="function"?_.popper(re):_.popper,gt=w.useMemo(()=>{var on,Bl;let Ot=[{name:"arrow",enabled:!!et,options:{element:et,padding:4}}];return(on=v.popperOptions)!=null&&on.modifiers&&(Ot=Ot.concat(v.popperOptions.modifiers)),(Bl=Zt==null?void 0:Zt.popperOptions)!=null&&Bl.modifiers&&(Ot=Ot.concat(Zt.popperOptions.modifiers)),{...v.popperOptions,...Zt==null?void 0:Zt.popperOptions,modifiers:Ot}},[et,v.popperOptions,Zt==null?void 0:Zt.popperOptions]),Xt=UA(re),be=typeof _.transition=="function"?_.transition(re):_.transition,mn={slots:{popper:p.Popper,transition:p.Transition??it,tooltip:p.Tooltip,arrow:p.Arrow,...W},slotProps:{arrow:_.arrow??h.arrow,popper:{...v,...Zt??h.popper},tooltip:_.tooltip??h.tooltip,transition:{...F,...be??h.transition}}},[ur,Ml]=pe("popper",{elementType:LA,externalForwardedProps:mn,ownerState:re,className:ht(Xt.popper,v==null?void 0:v.className)}),[Wo,cr]=pe("transition",{elementType:yu,externalForwardedProps:mn,ownerState:re}),[_u,zl]=pe("tooltip",{elementType:HA,className:Xt.tooltip,externalForwardedProps:mn,ownerState:re}),[Dl,Ko]=pe("arrow",{elementType:qA,className:Xt.arrow,externalForwardedProps:mn,ownerState:re,ref:nt});return T.jsxs(w.Fragment,{children:[w.cloneElement(V,mt),T.jsx(ur,{as:K??K0,placement:P,anchorEl:j?{getBoundingClientRect:()=>({top:tl.y,left:tl.x,right:tl.x,bottom:tl.y,width:0,height:0})}:z,popperRef:ne,open:z?St:!1,id:$t,transition:!0,...we,...Ml,popperOptions:gt,children:({TransitionProps:Ot})=>T.jsx(Wo,{timeout:ot.transitions.duration.shorter,...Ot,...cr,children:T.jsxs(_u,{...zl,children:[Z,u?T.jsx(Dl,{...Ko}):null]})})})]})});function PA(n){return Dt("MuiToolbar",n)}Mt("MuiToolbar",["root","gutters","regular","dense"]);const GA=n=>{const{classes:r,disableGutters:o,variant:l}=n;return Bt({root:["root",!o&&"gutters",l]},PA,r)},VA=ut("div",{name:"MuiToolbar",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:o}=n;return[r.root,!o.disableGutters&&r.gutters,r[o.variant]]}})(Gt(({theme:n})=>({position:"relative",display:"flex",alignItems:"center",variants:[{props:({ownerState:r})=>!r.disableGutters,style:{paddingLeft:n.spacing(2),paddingRight:n.spacing(2),[n.breakpoints.up("sm")]:{paddingLeft:n.spacing(3),paddingRight:n.spacing(3)}}},{props:{variant:"dense"},style:{minHeight:48}},{props:{variant:"regular"},style:n.mixins.toolbar}]}))),YA=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiToolbar"}),{className:u,component:c="div",disableGutters:d=!1,variant:p="regular",...h}=l,m={...l,component:c,disableGutters:d,variant:p},y=GA(m);return T.jsx(VA,{as:c,className:ht(y.root,u),ref:o,ownerState:m,...h})});function XA(n){return Dt("MuiTextField",n)}Mt("MuiTextField",["root"]);const IA={standard:bp,filled:vp,outlined:xp},WA=n=>{const{classes:r}=n;return Bt({root:["root"]},XA,r)},KA=ut(z4,{name:"MuiTextField",slot:"Root"})({}),QA=w.forwardRef(function(r,o){const l=Nt({props:r,name:"MuiTextField"}),{autoComplete:u,autoFocus:c=!1,children:d,className:p,color:h="primary",defaultValue:m,disabled:y=!1,error:S=!1,FormHelperTextProps:E,fullWidth:R=!1,helperText:x,id:C,InputLabelProps:D,inputProps:j,InputProps:U,inputRef:M,label:O,maxRows:A,minRows:$,multiline:H=!1,name:P,onBlur:K,onChange:v,onFocus:_,placeholder:W,required:Z=!1,rows:it,select:F=!1,SelectProps:B,slots:V={},slotProps:ot={},type:Q,value:z,variant:X="outlined",...et}=l,nt={...l,autoFocus:c,color:h,disabled:y,error:S,fullWidth:R,multiline:H,required:Z,select:F,variant:X},lt=WA(nt),ct=Vo(C),ft=x&&ct?`${ct}-helper-text`:void 0,Tt=O&&ct?`${ct}-label`:void 0,bt=IA[X],At={slots:V,slotProps:{input:U,inputLabel:D,htmlInput:j,formHelperText:E,select:B,...ot}},yt={},wt=At.slotProps.inputLabel;X==="outlined"&&(wt&&typeof wt.shrink<"u"&&(yt.notched=wt.shrink),yt.label=O),F&&((!B||!B.native)&&(yt.id=void 0),yt["aria-describedby"]=void 0);const[St,$t]=pe("root",{elementType:KA,shouldForwardComponentProp:!0,externalForwardedProps:{...At,...et},ownerState:nt,className:ht(lt.root,p),ref:o,additionalProps:{disabled:y,error:S,fullWidth:R,required:Z,color:h,variant:X}}),[Et,Vt]=pe("input",{elementType:bt,externalForwardedProps:At,additionalProps:yt,ownerState:nt}),[Te,Lt]=pe("inputLabel",{elementType:I4,externalForwardedProps:At,ownerState:nt}),[Kt,Qt]=pe("htmlInput",{elementType:"input",externalForwardedProps:At,ownerState:nt}),[Yt,_t]=pe("formHelperText",{elementType:j4,externalForwardedProps:At,ownerState:nt}),[dt,Ue]=pe("select",{elementType:ib,externalForwardedProps:At,ownerState:nt}),le=T.jsx(Et,{"aria-describedby":ft,autoComplete:u,autoFocus:c,defaultValue:m,fullWidth:R,multiline:H,name:P,rows:it,maxRows:A,minRows:$,type:Q,value:z,id:ct,inputRef:M,onBlur:K,onChange:v,onFocus:_,placeholder:W,inputProps:Qt,slots:{input:V.htmlInput?Kt:void 0},...Vt});return T.jsxs(St,{...$t,children:[O!=null&&O!==""&&T.jsx(Te,{htmlFor:ct,id:Tt,...Lt,children:O}),F?T.jsx(dt,{"aria-describedby":ft,id:ct,labelId:Tt,value:z,input:le,...Ue,children:d}):le,x&&T.jsx(Yt,{id:ft,..._t,children:x})]})}),ZA=Mn(T.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 4c1.93 0 3.5 1.57 3.5 3.5S13.93 13 12 13s-3.5-1.57-3.5-3.5S10.07 6 12 6m0 14c-2.03 0-4.43-.82-6.14-2.88C7.55 15.8 9.68 15 12 15s4.45.8 6.14 2.12C16.43 19.18 14.03 20 12 20"})),lb=Mn(T.jsx("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"})),FA=Mn(T.jsx("path",{d:"M3 13h8V3H3zm0 8h8v-6H3zm10 0h8V11h-8zm0-18v6h8V3z"})),JA=Mn(T.jsx("path",{d:"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"})),t5=Mn(T.jsx("path",{d:"M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2m0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2m0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2"})),e5=Mn(T.jsx("path",{d:"M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2m6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1z"})),n5=Mn(T.jsx("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4m0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4"})),_v=Mn(T.jsx("path",{d:"M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6"})),a5=Mn(T.jsx("path",{d:"M2.5 4v3h5v12h3V7h5V4zm19 5h-9v3h3v7h3v-7h3z"})),r5=Mn(T.jsx("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2M9 17H7V7h2zm4-5h-2V7h2zm4 3h-2V7h2z"})),o5=({currentView:n,onViewChange:r})=>{const[o,l]=w.useState(null),[u,c]=w.useState(null),d=E=>{l(E.currentTarget)},p=()=>{l(null)},h=E=>{c(E.currentTarget)},m=()=>{c(null)},y=!!o,S=!!u;return T.jsx(RE,{position:"static",elevation:1,children:T.jsxs(YA,{children:[T.jsx(FA,{sx:{mr:2}}),T.jsx(jt,{variant:"h6",component:"div",sx:{flexGrow:1},children:"Trello Clone"}),T.jsxs(Se,{sx:{display:"flex",gap:1,mr:2},children:[T.jsx(or,{color:"inherit",startIcon:T.jsx(JA,{}),onClick:()=>r("home"),variant:n==="home"?"outlined":"text",sx:{borderColor:n==="home"?"rgba(255,255,255,0.5)":"transparent","&:hover":{backgroundColor:"rgba(255,255,255,0.1)"}},children:"Home"}),T.jsx(or,{color:"inherit",startIcon:T.jsx(r5,{}),onClick:()=>r("board"),variant:n==="board"?"outlined":"text",sx:{borderColor:n==="board"?"rgba(255,255,255,0.5)":"transparent","&:hover":{backgroundColor:"rgba(255,255,255,0.1)"}},children:"Board"}),T.jsx(or,{color:"inherit",startIcon:T.jsx(a5,{}),onClick:()=>r("typography"),variant:n==="typography"?"outlined":"text",sx:{borderColor:n==="typography"?"rgba(255,255,255,0.5)":"transparent","&:hover":{backgroundColor:"rgba(255,255,255,0.1)"}},children:"Typography"})]}),T.jsxs(Se,{sx:{display:"flex",alignItems:"center",gap:1},children:[T.jsx(fd,{title:"Notifications",children:T.jsxs(ol,{color:"inherit",onClick:h,sx:{position:"relative"},children:[T.jsx(e5,{}),T.jsx(Se,{sx:{position:"absolute",top:8,right:8,width:8,height:8,backgroundColor:"error.main",borderRadius:"50%"}})]})}),T.jsx(fd,{title:"Settings",children:T.jsx(ol,{color:"inherit",children:T.jsx(_v,{})})}),T.jsx(fd,{title:"Profile",children:T.jsx(ol,{onClick:d,color:"inherit",children:T.jsx(uw,{sx:{width:32,height:32,bgcolor:"secondary.main"},children:"JD"})})})]}),T.jsxs($d,{anchorEl:o,open:y,onClose:p,onClick:p,PaperProps:{elevation:3,sx:{mt:1.5,minWidth:200}},children:[T.jsxs(Oo,{children:[T.jsx(ZA,{sx:{mr:2}}),"Profile"]}),T.jsxs(Oo,{children:[T.jsx(_v,{sx:{mr:2}}),"Settings"]}),T.jsx(Oo,{children:"Logout"})]}),T.jsxs($d,{anchorEl:u,open:S,onClose:m,onClick:m,PaperProps:{elevation:3,sx:{mt:1.5,minWidth:300}},children:[T.jsx(Oo,{children:T.jsxs(Se,{children:[T.jsx(jt,{variant:"subtitle2",children:"New card added"}),T.jsx(jt,{variant:"body2",color:"text.secondary",children:'"Implement authentication" was added to In Progress'})]})}),T.jsx(Oo,{children:T.jsxs(Se,{children:[T.jsx(jt,{variant:"subtitle2",children:"Card moved"}),T.jsx(jt,{variant:"body2",color:"text.secondary",children:'"Project setup" was moved to Done'})]})}),T.jsx(Oo,{children:T.jsxs(Se,{children:[T.jsx(jt,{variant:"subtitle2",children:"Due date reminder"}),T.jsx(jt,{variant:"body2",color:"text.secondary",children:'"Design homepage" is due tomorrow'})]})})]})]})})},i5=()=>{const[n,r]=w.useState([{id:1,title:"To Do",cards:[{id:1,title:"Design homepage",description:"Create wireframes and mockups",labels:["Design","High Priority"]},{id:2,title:"Setup database",description:"Configure PostgreSQL",labels:["Backend"]}]},{id:2,title:"In Progress",cards:[{id:3,title:"Implement authentication",description:"Add login/signup functionality",labels:["Backend","Security"]}]},{id:3,title:"Done",cards:[{id:4,title:"Project setup",description:"Initialize React app with Vite",labels:["Setup"]}]}]),[o,l]=w.useState(!1),[u,c]=w.useState(""),[d,p]=w.useState(null),[h,m]=w.useState(null),y=R=>{p(R),l(!0)},S=()=>{if(u.trim()){const R={id:Date.now(),title:u,description:"",labels:[]};r(n.map(x=>x.id===d?{...x,cards:[...x.cards,R]}:x)),c(""),l(!1)}},E=R=>({Design:"primary",Backend:"secondary","High Priority":"error",Security:"warning",Setup:"success"})[R]||"default";return T.jsxs(Se,{sx:{p:3},children:[T.jsx(jt,{variant:"h4",gutterBottom:!0,children:"Project Board"}),T.jsx(Se,{sx:{display:"flex",gap:3,overflowX:"auto",pb:2},children:n.map(R=>T.jsxs(Se,{sx:{minWidth:300,maxWidth:300,backgroundColor:"#f4f5f7",borderRadius:2,p:2},children:[T.jsxs(Se,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[T.jsx(jt,{variant:"h6",fontWeight:"bold",children:R.title}),T.jsx(ol,{size:"small",children:T.jsx(t5,{})})]}),T.jsxs(Se,{sx:{display:"flex",flexDirection:"column",gap:2},children:[R.cards.map(x=>T.jsx(Oa,{sx:{cursor:"pointer","&:hover":{boxShadow:3}},children:T.jsxs(rr,{sx:{p:2,"&:last-child":{pb:2}},children:[T.jsx(jt,{variant:"subtitle1",fontWeight:"medium",gutterBottom:!0,children:x.title}),x.description&&T.jsx(jt,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:x.description}),x.labels.length>0&&T.jsx(Se,{sx:{display:"flex",flexWrap:"wrap",gap:.5,mt:1},children:x.labels.map((C,D)=>T.jsx(Nr,{label:C,size:"small",color:E(C),variant:"outlined"},D))}),T.jsx(Se,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mt:2},children:T.jsx(Se,{sx:{display:"flex",gap:.5},children:T.jsx(ol,{size:"small",children:T.jsx(n5,{fontSize:"small"})})})})]})},x.id)),T.jsx(or,{variant:"outlined",startIcon:T.jsx(lb,{}),onClick:()=>y(R.id),sx:{borderStyle:"dashed",color:"text.secondary",borderColor:"divider","&:hover":{borderStyle:"solid",borderColor:"primary.main",color:"primary.main"}},children:"Add a card"})]})]},R.id))}),T.jsxs(n4,{open:o,onClose:()=>l(!1),maxWidth:"sm",fullWidth:!0,children:[T.jsx(m4,{children:"Add New Card"}),T.jsx(d4,{children:T.jsx(QA,{autoFocus:!0,margin:"dense",label:"Card Title",fullWidth:!0,variant:"outlined",value:u,onChange:R=>c(R.target.value),onKeyPress:R=>{R.key==="Enter"&&S()}})}),T.jsxs(i4,{children:[T.jsx(or,{onClick:()=>l(!1),children:"Cancel"}),T.jsx(or,{onClick:S,variant:"contained",children:"Add Card"})]})]})]})},l5=()=>{const n=[{name:"Light",weight:300,description:"For subtle text"},{name:"Regular",weight:400,description:"Default body text"},{name:"Medium",weight:500,description:"Emphasis and headings"},{name:"Bold",weight:700,description:"Strong emphasis"}],r=[{variant:"h1",label:"Heading 1",sample:"The quick brown fox jumps"},{variant:"h2",label:"Heading 2",sample:"The quick brown fox jumps"},{variant:"h3",label:"Heading 3",sample:"The quick brown fox jumps"},{variant:"h4",label:"Heading 4",sample:"The quick brown fox jumps"},{variant:"h5",label:"Heading 5",sample:"The quick brown fox jumps"},{variant:"h6",label:"Heading 6",sample:"The quick brown fox jumps"},{variant:"subtitle1",label:"Subtitle 1",sample:"The quick brown fox jumps over the lazy dog"},{variant:"subtitle2",label:"Subtitle 2",sample:"The quick brown fox jumps over the lazy dog"},{variant:"body1",label:"Body 1",sample:"The quick brown fox jumps over the lazy dog. This is the default body text style."},{variant:"body2",label:"Body 2",sample:"The quick brown fox jumps over the lazy dog. This is secondary body text."},{variant:"caption",label:"Caption",sample:"The quick brown fox jumps over the lazy dog"},{variant:"overline",label:"Overline",sample:"The quick brown fox jumps"}];return T.jsxs(Se,{sx:{p:3},children:[T.jsx(jt,{variant:"h3",gutterBottom:!0,children:"Roboto Font Typography Showcase"}),T.jsx(jt,{variant:"body1",paragraph:!0,children:"This application now uses the Roboto font family as the primary typeface. Roboto is Google's signature family of fonts, designed for optimal readability across platforms and devices."}),T.jsxs(Se,{sx:{mb:4},children:[T.jsx(jt,{variant:"h4",gutterBottom:!0,children:"Font Weights"}),T.jsx(Tn,{container:!0,spacing:2,children:n.map(o=>T.jsx(Tn,{item:!0,xs:12,sm:6,md:3,children:T.jsx(Oa,{children:T.jsxs(rr,{children:[T.jsxs(jt,{variant:"h6",sx:{fontWeight:o.weight},gutterBottom:!0,children:["Roboto ",o.name]}),T.jsx(Nr,{label:`Weight: ${o.weight}`,size:"small",color:"primary",sx:{mb:1}}),T.jsx(jt,{variant:"body2",color:"text.secondary",children:o.description}),T.jsx(jt,{variant:"body1",sx:{fontWeight:o.weight,mt:1},children:"The quick brown fox jumps over the lazy dog."})]})})},o.weight))})]}),T.jsx(Nd,{sx:{my:4}}),T.jsxs(Se,{children:[T.jsx(jt,{variant:"h4",gutterBottom:!0,children:"Typography Variants"}),T.jsx(jt,{variant:"body1",paragraph:!0,children:"All typography variants are now using the Roboto font with optimized font weights and line heights for better readability."}),T.jsx(Tn,{container:!0,spacing:3,children:r.map(o=>T.jsx(Tn,{item:!0,xs:12,children:T.jsx(Oa,{sx:{p:2},children:T.jsxs(Se,{sx:{display:"flex",alignItems:"flex-start",gap:2},children:[T.jsx(Se,{sx:{minWidth:120},children:T.jsx(Nr,{label:o.label,variant:"outlined",size:"small",color:"secondary"})}),T.jsx(Se,{sx:{flex:1},children:T.jsx(jt,{variant:o.variant,children:o.sample})})]})})},o.variant))})]}),T.jsxs(Se,{sx:{mt:4,p:3,bgcolor:"background.paper",borderRadius:2},children:[T.jsx(jt,{variant:"h5",gutterBottom:!0,children:"Font Loading Strategy"}),T.jsx(jt,{variant:"body1",paragraph:!0,children:"This application uses a dual font loading strategy for optimal performance:"}),T.jsxs(Se,{component:"ul",sx:{pl:3},children:[T.jsxs(jt,{component:"li",variant:"body1",paragraph:!0,children:[T.jsx("strong",{children:"Google Fonts CDN:"})," Fast loading via CDN with font-display: swap for immediate text rendering"]}),T.jsxs(jt,{component:"li",variant:"body1",paragraph:!0,children:[T.jsx("strong",{children:"Self-hosted fonts:"})," @fontsource/roboto package for offline support and better caching control"]})]}),T.jsx(jt,{variant:"body2",color:"text.secondary",children:"The browser will automatically choose the best loading method based on availability."})]})]})};function s5(){const[n,r]=w.useState(0),[o,l]=w.useState("home"),u=d=>{l(d)},c=()=>{switch(o){case"board":return T.jsx(i5,{});case"typography":return T.jsx(l5,{});case"home":default:return T.jsx(u5,{count:n,setCount:r})}};return T.jsxs(Se,{sx:{flexGrow:1},children:[T.jsx(o5,{currentView:o,onViewChange:u}),c(),T.jsx(T4,{color:"primary","aria-label":"add",sx:{position:"fixed",bottom:16,right:16},children:T.jsx(lb,{})})]})}const u5=({count:n,setCount:r})=>T.jsx(zw,{maxWidth:"lg",sx:{mt:4,mb:4},children:T.jsxs(Tn,{container:!0,spacing:3,children:[T.jsx(Tn,{item:!0,xs:12,md:8,children:T.jsx(Oa,{children:T.jsxs(rr,{children:[T.jsx(jt,{variant:"h4",component:"h1",gutterBottom:!0,children:"Welcome to Your Trello Clone!"}),T.jsx(jt,{variant:"body1",paragraph:!0,children:"This is a modern React application built with Vite and Material-UI. The setup includes a custom theme with Trello-inspired colors and styling."}),T.jsxs(Se,{sx:{mt:2},children:[T.jsx(Nr,{label:"React 19",color:"primary",sx:{mr:1}}),T.jsx(Nr,{label:"Vite",color:"secondary",sx:{mr:1}}),T.jsx(Nr,{label:"Material-UI",color:"success",sx:{mr:1}}),T.jsx(Nr,{label:"Emotion",color:"info"})]})]})})}),T.jsx(Tn,{item:!0,xs:12,md:4,children:T.jsx(Oa,{children:T.jsxs(rr,{children:[T.jsx(jt,{variant:"h5",component:"h2",gutterBottom:!0,children:"Interactive Demo"}),T.jsx(jt,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Test the reactivity with this counter:"}),T.jsxs(Se,{sx:{textAlign:"center",mt:2},children:[T.jsx(jt,{variant:"h3",color:"primary",gutterBottom:!0,children:n}),T.jsx(or,{variant:"contained",onClick:()=>r(o=>o+1),sx:{mr:1},children:"Increment"}),T.jsx(or,{variant:"outlined",onClick:()=>r(0),children:"Reset"})]})]})})}),T.jsxs(Tn,{item:!0,xs:12,children:[T.jsx(jt,{variant:"h5",component:"h2",gutterBottom:!0,sx:{mt:2},children:"Features Included"}),T.jsxs(Tn,{container:!0,spacing:2,children:[T.jsx(Tn,{item:!0,xs:12,sm:6,md:3,children:T.jsx(Oa,{sx:{height:"100%"},children:T.jsxs(rr,{children:[T.jsx(jt,{variant:"h6",gutterBottom:!0,children:"🎨 Custom Theme"}),T.jsx(jt,{variant:"body2",children:"Trello-inspired color palette and typography"})]})})}),T.jsx(Tn,{item:!0,xs:12,sm:6,md:3,children:T.jsx(Oa,{sx:{height:"100%"},children:T.jsxs(rr,{children:[T.jsx(jt,{variant:"h6",gutterBottom:!0,children:"⚡ Vite Build Tool"}),T.jsx(jt,{variant:"body2",children:"Lightning fast development and build process"})]})})}),T.jsx(Tn,{item:!0,xs:12,sm:6,md:3,children:T.jsx(Oa,{sx:{height:"100%"},children:T.jsxs(rr,{children:[T.jsx(jt,{variant:"h6",gutterBottom:!0,children:"🧩 MUI Components"}),T.jsx(jt,{variant:"body2",children:"Complete Material-UI component library"})]})})}),T.jsx(Tn,{item:!0,xs:12,sm:6,md:3,children:T.jsx(Oa,{sx:{height:"100%"},children:T.jsxs(rr,{children:[T.jsx(jt,{variant:"h6",gutterBottom:!0,children:"🔧 Ready to Build"}),T.jsx(jt,{variant:"body2",children:"Perfect foundation for your Trello clone"})]})})})]})]})]})});MS.createRoot(document.getElementById("root")).render(T.jsx(w.StrictMode,{children:T.jsxs(mT,{theme:TT,children:[T.jsx(CT,{}),T.jsx(s5,{})]})}));
