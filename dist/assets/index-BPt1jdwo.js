var yS=Object.defineProperty;var vS=(n,r,i)=>r in n?yS(n,r,{enumerable:!0,configurable:!0,writable:!0,value:i}):n[r]=i;var Gi=(n,r,i)=>vS(n,typeof r!="symbol"?r+"":r,i);function bS(n,r){for(var i=0;i<r.length;i++){const l=r[i];if(typeof l!="string"&&!Array.isArray(l)){for(const u in l)if(u!=="default"&&!(u in n)){const c=Object.getOwnPropertyDescriptor(l,u);c&&Object.defineProperty(n,u,c.get?c:{enumerable:!0,get:()=>l[u]})}}}return Object.freeze(Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}))}(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const u of document.querySelectorAll('link[rel="modulepreload"]'))l(u);new MutationObserver(u=>{for(const c of u)if(c.type==="childList")for(const d of c.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&l(d)}).observe(document,{childList:!0,subtree:!0});function i(u){const c={};return u.integrity&&(c.integrity=u.integrity),u.referrerPolicy&&(c.referrerPolicy=u.referrerPolicy),u.crossOrigin==="use-credentials"?c.credentials="include":u.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function l(u){if(u.ep)return;u.ep=!0;const c=i(u);fetch(u.href,c)}})();function _v(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var If={exports:{}},Vi={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var iy;function SS(){if(iy)return Vi;iy=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function i(l,u,c){var d=null;if(c!==void 0&&(d=""+c),u.key!==void 0&&(d=""+u.key),"key"in u){c={};for(var p in u)p!=="key"&&(c[p]=u[p])}else c=u;return u=c.ref,{$$typeof:n,type:l,key:d,ref:u!==void 0?u:null,props:c}}return Vi.Fragment=r,Vi.jsx=i,Vi.jsxs=i,Vi}var ly;function xS(){return ly||(ly=1,If.exports=SS()),If.exports}var O=xS(),Kf={exports:{}},zt={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sy;function CS(){if(sy)return zt;sy=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),c=Symbol.for("react.consumer"),d=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),h=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),S=Symbol.iterator;function T(z){return z===null||typeof z!="object"?null:(z=S&&z[S]||z["@@iterator"],typeof z=="function"?z:null)}var E={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},x=Object.assign,C={};function D(z,X,et){this.props=z,this.context=X,this.refs=C,this.updater=et||E}D.prototype.isReactComponent={},D.prototype.setState=function(z,X){if(typeof z!="object"&&typeof z!="function"&&z!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,z,X,"setState")},D.prototype.forceUpdate=function(z){this.updater.enqueueForceUpdate(this,z,"forceUpdate")};function k(){}k.prototype=D.prototype;function U(z,X,et){this.props=z,this.context=X,this.refs=C,this.updater=et||E}var M=U.prototype=new k;M.constructor=U,x(M,D.prototype),M.isPureReactComponent=!0;var A=Array.isArray,w={H:null,A:null,T:null,S:null,V:null},j=Object.prototype.hasOwnProperty;function H(z,X,et,nt,lt,ut){return et=ut.ref,{$$typeof:n,type:z,key:X,ref:et!==void 0?et:null,props:ut}}function q(z,X){return H(z.type,X,void 0,void 0,void 0,z.props)}function Q(z){return typeof z=="object"&&z!==null&&z.$$typeof===n}function v(z){var X={"=":"=0",":":"=2"};return"$"+z.replace(/[=:]/g,function(et){return X[et]})}var _=/\/+/g;function K(z,X){return typeof z=="object"&&z!==null&&z.key!=null?v(""+z.key):X.toString(36)}function Z(){}function it(z){switch(z.status){case"fulfilled":return z.value;case"rejected":throw z.reason;default:switch(typeof z.status=="string"?z.then(Z,Z):(z.status="pending",z.then(function(X){z.status==="pending"&&(z.status="fulfilled",z.value=X)},function(X){z.status==="pending"&&(z.status="rejected",z.reason=X)})),z.status){case"fulfilled":return z.value;case"rejected":throw z.reason}}throw z}function F(z,X,et,nt,lt){var ut=typeof z;(ut==="undefined"||ut==="boolean")&&(z=null);var ft=!1;if(z===null)ft=!0;else switch(ut){case"bigint":case"string":case"number":ft=!0;break;case"object":switch(z.$$typeof){case n:case r:ft=!0;break;case y:return ft=z._init,F(ft(z._payload),X,et,nt,lt)}}if(ft)return lt=lt(z),ft=nt===""?"."+K(z,0):nt,A(lt)?(et="",ft!=null&&(et=ft.replace(_,"$&/")+"/"),F(lt,X,et,"",function(Ot){return Ot})):lt!=null&&(Q(lt)&&(lt=q(lt,et+(lt.key==null||z&&z.key===lt.key?"":(""+lt.key).replace(_,"$&/")+"/")+ft)),X.push(lt)),1;ft=0;var Tt=nt===""?".":nt+":";if(A(z))for(var bt=0;bt<z.length;bt++)nt=z[bt],ut=Tt+K(nt,bt),ft+=F(nt,X,et,ut,lt);else if(bt=T(z),typeof bt=="function")for(z=bt.call(z),bt=0;!(nt=z.next()).done;)nt=nt.value,ut=Tt+K(nt,bt++),ft+=F(nt,X,et,ut,lt);else if(ut==="object"){if(typeof z.then=="function")return F(it(z),X,et,nt,lt);throw X=String(z),Error("Objects are not valid as a React child (found: "+(X==="[object Object]"?"object with keys {"+Object.keys(z).join(", ")+"}":X)+"). If you meant to render a collection of children, use an array instead.")}return ft}function N(z,X,et){if(z==null)return z;var nt=[],lt=0;return F(z,nt,"","",function(ut){return X.call(et,ut,lt++)}),nt}function V(z){if(z._status===-1){var X=z._result;X=X(),X.then(function(et){(z._status===0||z._status===-1)&&(z._status=1,z._result=et)},function(et){(z._status===0||z._status===-1)&&(z._status=2,z._result=et)}),z._status===-1&&(z._status=0,z._result=X)}if(z._status===1)return z._result.default;throw z._result}var ot=typeof reportError=="function"?reportError:function(z){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var X=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof z=="object"&&z!==null&&typeof z.message=="string"?String(z.message):String(z),error:z});if(!window.dispatchEvent(X))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",z);return}console.error(z)};function W(){}return zt.Children={map:N,forEach:function(z,X,et){N(z,function(){X.apply(this,arguments)},et)},count:function(z){var X=0;return N(z,function(){X++}),X},toArray:function(z){return N(z,function(X){return X})||[]},only:function(z){if(!Q(z))throw Error("React.Children.only expected to receive a single React element child.");return z}},zt.Component=D,zt.Fragment=i,zt.Profiler=u,zt.PureComponent=U,zt.StrictMode=l,zt.Suspense=m,zt.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=w,zt.__COMPILER_RUNTIME={__proto__:null,c:function(z){return w.H.useMemoCache(z)}},zt.cache=function(z){return function(){return z.apply(null,arguments)}},zt.cloneElement=function(z,X,et){if(z==null)throw Error("The argument must be a React element, but you passed "+z+".");var nt=x({},z.props),lt=z.key,ut=void 0;if(X!=null)for(ft in X.ref!==void 0&&(ut=void 0),X.key!==void 0&&(lt=""+X.key),X)!j.call(X,ft)||ft==="key"||ft==="__self"||ft==="__source"||ft==="ref"&&X.ref===void 0||(nt[ft]=X[ft]);var ft=arguments.length-2;if(ft===1)nt.children=et;else if(1<ft){for(var Tt=Array(ft),bt=0;bt<ft;bt++)Tt[bt]=arguments[bt+2];nt.children=Tt}return H(z.type,lt,void 0,void 0,ut,nt)},zt.createContext=function(z){return z={$$typeof:d,_currentValue:z,_currentValue2:z,_threadCount:0,Provider:null,Consumer:null},z.Provider=z,z.Consumer={$$typeof:c,_context:z},z},zt.createElement=function(z,X,et){var nt,lt={},ut=null;if(X!=null)for(nt in X.key!==void 0&&(ut=""+X.key),X)j.call(X,nt)&&nt!=="key"&&nt!=="__self"&&nt!=="__source"&&(lt[nt]=X[nt]);var ft=arguments.length-2;if(ft===1)lt.children=et;else if(1<ft){for(var Tt=Array(ft),bt=0;bt<ft;bt++)Tt[bt]=arguments[bt+2];lt.children=Tt}if(z&&z.defaultProps)for(nt in ft=z.defaultProps,ft)lt[nt]===void 0&&(lt[nt]=ft[nt]);return H(z,ut,void 0,void 0,null,lt)},zt.createRef=function(){return{current:null}},zt.forwardRef=function(z){return{$$typeof:p,render:z}},zt.isValidElement=Q,zt.lazy=function(z){return{$$typeof:y,_payload:{_status:-1,_result:z},_init:V}},zt.memo=function(z,X){return{$$typeof:h,type:z,compare:X===void 0?null:X}},zt.startTransition=function(z){var X=w.T,et={};w.T=et;try{var nt=z(),lt=w.S;lt!==null&&lt(et,nt),typeof nt=="object"&&nt!==null&&typeof nt.then=="function"&&nt.then(W,ot)}catch(ut){ot(ut)}finally{w.T=X}},zt.unstable_useCacheRefresh=function(){return w.H.useCacheRefresh()},zt.use=function(z){return w.H.use(z)},zt.useActionState=function(z,X,et){return w.H.useActionState(z,X,et)},zt.useCallback=function(z,X){return w.H.useCallback(z,X)},zt.useContext=function(z){return w.H.useContext(z)},zt.useDebugValue=function(){},zt.useDeferredValue=function(z,X){return w.H.useDeferredValue(z,X)},zt.useEffect=function(z,X,et){var nt=w.H;if(typeof et=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return nt.useEffect(z,X)},zt.useId=function(){return w.H.useId()},zt.useImperativeHandle=function(z,X,et){return w.H.useImperativeHandle(z,X,et)},zt.useInsertionEffect=function(z,X){return w.H.useInsertionEffect(z,X)},zt.useLayoutEffect=function(z,X){return w.H.useLayoutEffect(z,X)},zt.useMemo=function(z,X){return w.H.useMemo(z,X)},zt.useOptimistic=function(z,X){return w.H.useOptimistic(z,X)},zt.useReducer=function(z,X,et){return w.H.useReducer(z,X,et)},zt.useRef=function(z){return w.H.useRef(z)},zt.useState=function(z){return w.H.useState(z)},zt.useSyncExternalStore=function(z,X,et){return w.H.useSyncExternalStore(z,X,et)},zt.useTransition=function(){return w.H.useTransition()},zt.version="19.1.0",zt}var uy;function jd(){return uy||(uy=1,Kf.exports=CS()),Kf.exports}var R=jd();const ra=_v(R),dd=bS({__proto__:null,default:ra},[R]);var Qf={exports:{}},Yi={},Wf={exports:{}},Zf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cy;function TS(){return cy||(cy=1,function(n){function r(N,V){var ot=N.length;N.push(V);t:for(;0<ot;){var W=ot-1>>>1,z=N[W];if(0<u(z,V))N[W]=V,N[ot]=z,ot=W;else break t}}function i(N){return N.length===0?null:N[0]}function l(N){if(N.length===0)return null;var V=N[0],ot=N.pop();if(ot!==V){N[0]=ot;t:for(var W=0,z=N.length,X=z>>>1;W<X;){var et=2*(W+1)-1,nt=N[et],lt=et+1,ut=N[lt];if(0>u(nt,ot))lt<z&&0>u(ut,nt)?(N[W]=ut,N[lt]=ot,W=lt):(N[W]=nt,N[et]=ot,W=et);else if(lt<z&&0>u(ut,ot))N[W]=ut,N[lt]=ot,W=lt;else break t}}return V}function u(N,V){var ot=N.sortIndex-V.sortIndex;return ot!==0?ot:N.id-V.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var c=performance;n.unstable_now=function(){return c.now()}}else{var d=Date,p=d.now();n.unstable_now=function(){return d.now()-p}}var m=[],h=[],y=1,S=null,T=3,E=!1,x=!1,C=!1,D=!1,k=typeof setTimeout=="function"?setTimeout:null,U=typeof clearTimeout=="function"?clearTimeout:null,M=typeof setImmediate<"u"?setImmediate:null;function A(N){for(var V=i(h);V!==null;){if(V.callback===null)l(h);else if(V.startTime<=N)l(h),V.sortIndex=V.expirationTime,r(m,V);else break;V=i(h)}}function w(N){if(C=!1,A(N),!x)if(i(m)!==null)x=!0,j||(j=!0,K());else{var V=i(h);V!==null&&F(w,V.startTime-N)}}var j=!1,H=-1,q=5,Q=-1;function v(){return D?!0:!(n.unstable_now()-Q<q)}function _(){if(D=!1,j){var N=n.unstable_now();Q=N;var V=!0;try{t:{x=!1,C&&(C=!1,U(H),H=-1),E=!0;var ot=T;try{e:{for(A(N),S=i(m);S!==null&&!(S.expirationTime>N&&v());){var W=S.callback;if(typeof W=="function"){S.callback=null,T=S.priorityLevel;var z=W(S.expirationTime<=N);if(N=n.unstable_now(),typeof z=="function"){S.callback=z,A(N),V=!0;break e}S===i(m)&&l(m),A(N)}else l(m);S=i(m)}if(S!==null)V=!0;else{var X=i(h);X!==null&&F(w,X.startTime-N),V=!1}}break t}finally{S=null,T=ot,E=!1}V=void 0}}finally{V?K():j=!1}}}var K;if(typeof M=="function")K=function(){M(_)};else if(typeof MessageChannel<"u"){var Z=new MessageChannel,it=Z.port2;Z.port1.onmessage=_,K=function(){it.postMessage(null)}}else K=function(){k(_,0)};function F(N,V){H=k(function(){N(n.unstable_now())},V)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(N){N.callback=null},n.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):q=0<N?Math.floor(1e3/N):5},n.unstable_getCurrentPriorityLevel=function(){return T},n.unstable_next=function(N){switch(T){case 1:case 2:case 3:var V=3;break;default:V=T}var ot=T;T=V;try{return N()}finally{T=ot}},n.unstable_requestPaint=function(){D=!0},n.unstable_runWithPriority=function(N,V){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var ot=T;T=N;try{return V()}finally{T=ot}},n.unstable_scheduleCallback=function(N,V,ot){var W=n.unstable_now();switch(typeof ot=="object"&&ot!==null?(ot=ot.delay,ot=typeof ot=="number"&&0<ot?W+ot:W):ot=W,N){case 1:var z=-1;break;case 2:z=250;break;case 5:z=1073741823;break;case 4:z=1e4;break;default:z=5e3}return z=ot+z,N={id:y++,callback:V,priorityLevel:N,startTime:ot,expirationTime:z,sortIndex:-1},ot>W?(N.sortIndex=ot,r(h,N),i(m)===null&&N===i(h)&&(C?(U(H),H=-1):C=!0,F(w,ot-W))):(N.sortIndex=z,r(m,N),x||E||(x=!0,j||(j=!0,K()))),N},n.unstable_shouldYield=v,n.unstable_wrapCallback=function(N){var V=T;return function(){var ot=T;T=V;try{return N.apply(this,arguments)}finally{T=ot}}}}(Zf)),Zf}var fy;function ES(){return fy||(fy=1,Wf.exports=TS()),Wf.exports}var Ff={exports:{}},tn={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dy;function RS(){if(dy)return tn;dy=1;var n=jd();function r(m){var h="https://react.dev/errors/"+m;if(1<arguments.length){h+="?args[]="+encodeURIComponent(arguments[1]);for(var y=2;y<arguments.length;y++)h+="&args[]="+encodeURIComponent(arguments[y])}return"Minified React error #"+m+"; visit "+h+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function i(){}var l={d:{f:i,r:function(){throw Error(r(522))},D:i,C:i,L:i,m:i,X:i,S:i,M:i},p:0,findDOMNode:null},u=Symbol.for("react.portal");function c(m,h,y){var S=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:u,key:S==null?null:""+S,children:m,containerInfo:h,implementation:y}}var d=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function p(m,h){if(m==="font")return"";if(typeof h=="string")return h==="use-credentials"?h:""}return tn.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=l,tn.createPortal=function(m,h){var y=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!h||h.nodeType!==1&&h.nodeType!==9&&h.nodeType!==11)throw Error(r(299));return c(m,h,null,y)},tn.flushSync=function(m){var h=d.T,y=l.p;try{if(d.T=null,l.p=2,m)return m()}finally{d.T=h,l.p=y,l.d.f()}},tn.preconnect=function(m,h){typeof m=="string"&&(h?(h=h.crossOrigin,h=typeof h=="string"?h==="use-credentials"?h:"":void 0):h=null,l.d.C(m,h))},tn.prefetchDNS=function(m){typeof m=="string"&&l.d.D(m)},tn.preinit=function(m,h){if(typeof m=="string"&&h&&typeof h.as=="string"){var y=h.as,S=p(y,h.crossOrigin),T=typeof h.integrity=="string"?h.integrity:void 0,E=typeof h.fetchPriority=="string"?h.fetchPriority:void 0;y==="style"?l.d.S(m,typeof h.precedence=="string"?h.precedence:void 0,{crossOrigin:S,integrity:T,fetchPriority:E}):y==="script"&&l.d.X(m,{crossOrigin:S,integrity:T,fetchPriority:E,nonce:typeof h.nonce=="string"?h.nonce:void 0})}},tn.preinitModule=function(m,h){if(typeof m=="string")if(typeof h=="object"&&h!==null){if(h.as==null||h.as==="script"){var y=p(h.as,h.crossOrigin);l.d.M(m,{crossOrigin:y,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0})}}else h==null&&l.d.M(m)},tn.preload=function(m,h){if(typeof m=="string"&&typeof h=="object"&&h!==null&&typeof h.as=="string"){var y=h.as,S=p(y,h.crossOrigin);l.d.L(m,y,{crossOrigin:S,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0,type:typeof h.type=="string"?h.type:void 0,fetchPriority:typeof h.fetchPriority=="string"?h.fetchPriority:void 0,referrerPolicy:typeof h.referrerPolicy=="string"?h.referrerPolicy:void 0,imageSrcSet:typeof h.imageSrcSet=="string"?h.imageSrcSet:void 0,imageSizes:typeof h.imageSizes=="string"?h.imageSizes:void 0,media:typeof h.media=="string"?h.media:void 0})}},tn.preloadModule=function(m,h){if(typeof m=="string")if(h){var y=p(h.as,h.crossOrigin);l.d.m(m,{as:typeof h.as=="string"&&h.as!=="script"?h.as:void 0,crossOrigin:y,integrity:typeof h.integrity=="string"?h.integrity:void 0})}else l.d.m(m)},tn.requestFormReset=function(m){l.d.r(m)},tn.unstable_batchedUpdates=function(m,h){return m(h)},tn.useFormState=function(m,h,y){return d.H.useFormState(m,h,y)},tn.useFormStatus=function(){return d.H.useHostTransitionStatus()},tn.version="19.1.0",tn}var py;function Uv(){if(py)return Ff.exports;py=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),Ff.exports=RS(),Ff.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var my;function wS(){if(my)return Yi;my=1;var n=ES(),r=jd(),i=Uv();function l(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)e+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function c(t){var e=t,a=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(a=e.return),t=e.return;while(t)}return e.tag===3?a:null}function d(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function p(t){if(c(t)!==t)throw Error(l(188))}function m(t){var e=t.alternate;if(!e){if(e=c(t),e===null)throw Error(l(188));return e!==t?null:t}for(var a=t,o=e;;){var s=a.return;if(s===null)break;var f=s.alternate;if(f===null){if(o=s.return,o!==null){a=o;continue}break}if(s.child===f.child){for(f=s.child;f;){if(f===a)return p(s),t;if(f===o)return p(s),e;f=f.sibling}throw Error(l(188))}if(a.return!==o.return)a=s,o=f;else{for(var g=!1,b=s.child;b;){if(b===a){g=!0,a=s,o=f;break}if(b===o){g=!0,o=s,a=f;break}b=b.sibling}if(!g){for(b=f.child;b;){if(b===a){g=!0,a=f,o=s;break}if(b===o){g=!0,o=f,a=s;break}b=b.sibling}if(!g)throw Error(l(189))}}if(a.alternate!==o)throw Error(l(190))}if(a.tag!==3)throw Error(l(188));return a.stateNode.current===a?t:e}function h(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=h(t),e!==null)return e;t=t.sibling}return null}var y=Object.assign,S=Symbol.for("react.element"),T=Symbol.for("react.transitional.element"),E=Symbol.for("react.portal"),x=Symbol.for("react.fragment"),C=Symbol.for("react.strict_mode"),D=Symbol.for("react.profiler"),k=Symbol.for("react.provider"),U=Symbol.for("react.consumer"),M=Symbol.for("react.context"),A=Symbol.for("react.forward_ref"),w=Symbol.for("react.suspense"),j=Symbol.for("react.suspense_list"),H=Symbol.for("react.memo"),q=Symbol.for("react.lazy"),Q=Symbol.for("react.activity"),v=Symbol.for("react.memo_cache_sentinel"),_=Symbol.iterator;function K(t){return t===null||typeof t!="object"?null:(t=_&&t[_]||t["@@iterator"],typeof t=="function"?t:null)}var Z=Symbol.for("react.client.reference");function it(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===Z?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case x:return"Fragment";case D:return"Profiler";case C:return"StrictMode";case w:return"Suspense";case j:return"SuspenseList";case Q:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case E:return"Portal";case M:return(t.displayName||"Context")+".Provider";case U:return(t._context.displayName||"Context")+".Consumer";case A:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case H:return e=t.displayName||null,e!==null?e:it(t.type)||"Memo";case q:e=t._payload,t=t._init;try{return it(t(e))}catch{}}return null}var F=Array.isArray,N=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,V=i.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ot={pending:!1,data:null,method:null,action:null},W=[],z=-1;function X(t){return{current:t}}function et(t){0>z||(t.current=W[z],W[z]=null,z--)}function nt(t,e){z++,W[z]=t.current,t.current=e}var lt=X(null),ut=X(null),ft=X(null),Tt=X(null);function bt(t,e){switch(nt(ft,e),nt(ut,t),nt(lt,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?kg(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=kg(e),t=$g(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}et(lt),nt(lt,t)}function Ot(){et(lt),et(ut),et(ft)}function yt(t){t.memoizedState!==null&&nt(Tt,t);var e=lt.current,a=$g(e,t.type);e!==a&&(nt(ut,t),nt(lt,a))}function wt(t){ut.current===t&&(et(lt),et(ut)),Tt.current===t&&(et(Tt),Ui._currentValue=ot)}var St=Object.prototype.hasOwnProperty,$t=n.unstable_scheduleCallback,Et=n.unstable_cancelCallback,qt=n.unstable_shouldYield,Ce=n.unstable_requestPaint,Ut=n.unstable_now,Kt=n.unstable_getCurrentPriorityLevel,Qt=n.unstable_ImmediatePriority,Vt=n.unstable_UserBlockingPriority,jt=n.unstable_NormalPriority,dt=n.unstable_LowPriority,_e=n.unstable_IdlePriority,ie=n.log,Ze=n.unstable_setDisableYieldValue,Ue=null,ee=null;function Te(t){if(typeof ie=="function"&&Ze(t),ee&&typeof ee.setStrictMode=="function")try{ee.setStrictMode(Ue,t)}catch{}}var ne=Math.clz32?Math.clz32:Re,le=Math.log,mt=Math.LN2;function Re(t){return t>>>=0,t===0?32:31-(le(t)/mt|0)|0}var ae=256,Wt=4194304;function ht(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Yt(t,e,a){var o=t.pendingLanes;if(o===0)return 0;var s=0,f=t.suspendedLanes,g=t.pingedLanes;t=t.warmLanes;var b=o&134217727;return b!==0?(o=b&~f,o!==0?s=ht(o):(g&=b,g!==0?s=ht(g):a||(a=b&~t,a!==0&&(s=ht(a))))):(b=o&~f,b!==0?s=ht(b):g!==0?s=ht(g):a||(a=o&~t,a!==0&&(s=ht(a)))),s===0?0:e!==0&&e!==s&&(e&f)===0&&(f=s&-s,a=e&-e,f>=a||f===32&&(a&4194048)!==0)?e:s}function be(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function hn(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ir(){var t=ae;return ae<<=1,(ae&4194048)===0&&(ae=256),t}function Ml(){var t=Wt;return Wt<<=1,(Wt&62914560)===0&&(Wt=4194304),t}function Io(t){for(var e=[],a=0;31>a;a++)e.push(t);return e}function lr(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function _u(t,e,a,o,s,f){var g=t.pendingLanes;t.pendingLanes=a,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=a,t.entangledLanes&=a,t.errorRecoveryDisabledLanes&=a,t.shellSuspendCounter=0;var b=t.entanglements,B=t.expirationTimes,G=t.hiddenUpdates;for(a=g&~a;0<a;){var tt=31-ne(a),rt=1<<tt;b[tt]=0,B[tt]=-1;var Y=G[tt];if(Y!==null)for(G[tt]=null,tt=0;tt<Y.length;tt++){var I=Y[tt];I!==null&&(I.lane&=-536870913)}a&=~rt}o!==0&&zl(t,o,0),f!==0&&s===0&&t.tag!==0&&(t.suspendedLanes|=f&~(g&~e))}function zl(t,e,a){t.pendingLanes|=e,t.suspendedLanes&=~e;var o=31-ne(e);t.entangledLanes|=e,t.entanglements[o]=t.entanglements[o]|1073741824|a&4194090}function Dl(t,e){var a=t.entangledLanes|=e;for(t=t.entanglements;a;){var o=31-ne(a),s=1<<o;s&e|t[o]&e&&(t[o]|=e),a&=~s}}function Ko(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function At(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function on(){var t=V.p;return t!==0?t:(t=window.event,t===void 0?32:ty(t.type))}function Nl(t,e){var a=V.p;try{return V.p=t,e()}finally{V.p=a}}var Ba=Math.random().toString(36).slice(2),Fe="__reactFiber$"+Ba,ln="__reactProps$"+Ba,$r="__reactContainer$"+Ba,Uu="__reactEvents$"+Ba,lb="__reactListeners$"+Ba,sb="__reactHandles$"+Ba,Cp="__reactResources$"+Ba,Qo="__reactMarker$"+Ba;function Lu(t){delete t[Fe],delete t[ln],delete t[Uu],delete t[lb],delete t[sb]}function jr(t){var e=t[Fe];if(e)return e;for(var a=t.parentNode;a;){if(e=a[$r]||a[Fe]){if(a=e.alternate,e.child!==null||a!==null&&a.child!==null)for(t=Lg(t);t!==null;){if(a=t[Fe])return a;t=Lg(t)}return e}t=a,a=t.parentNode}return null}function _r(t){if(t=t[Fe]||t[$r]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Wo(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(l(33))}function Ur(t){var e=t[Cp];return e||(e=t[Cp]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function qe(t){t[Qo]=!0}var Tp=new Set,Ep={};function sr(t,e){Lr(t,e),Lr(t+"Capture",e)}function Lr(t,e){for(Ep[t]=e,t=0;t<e.length;t++)Tp.add(e[t])}var ub=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Rp={},wp={};function cb(t){return St.call(wp,t)?!0:St.call(Rp,t)?!1:ub.test(t)?wp[t]=!0:(Rp[t]=!0,!1)}function Bl(t,e,a){if(cb(e))if(a===null)t.removeAttribute(e);else{switch(typeof a){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var o=e.toLowerCase().slice(0,5);if(o!=="data-"&&o!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+a)}}function kl(t,e,a){if(a===null)t.removeAttribute(e);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+a)}}function ca(t,e,a,o){if(o===null)t.removeAttribute(a);else{switch(typeof o){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(a);return}t.setAttributeNS(e,a,""+o)}}var Hu,Op;function Hr(t){if(Hu===void 0)try{throw Error()}catch(a){var e=a.stack.trim().match(/\n( *(at )?)/);Hu=e&&e[1]||"",Op=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Hu+t+Op}var Pu=!1;function qu(t,e){if(!t||Pu)return"";Pu=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var o={DetermineComponentFrameRoot:function(){try{if(e){var rt=function(){throw Error()};if(Object.defineProperty(rt.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(rt,[])}catch(I){var Y=I}Reflect.construct(t,[],rt)}else{try{rt.call()}catch(I){Y=I}t.call(rt.prototype)}}else{try{throw Error()}catch(I){Y=I}(rt=t())&&typeof rt.catch=="function"&&rt.catch(function(){})}}catch(I){if(I&&Y&&typeof I.stack=="string")return[I.stack,Y.stack]}return[null,null]}};o.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var s=Object.getOwnPropertyDescriptor(o.DetermineComponentFrameRoot,"name");s&&s.configurable&&Object.defineProperty(o.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var f=o.DetermineComponentFrameRoot(),g=f[0],b=f[1];if(g&&b){var B=g.split(`
`),G=b.split(`
`);for(s=o=0;o<B.length&&!B[o].includes("DetermineComponentFrameRoot");)o++;for(;s<G.length&&!G[s].includes("DetermineComponentFrameRoot");)s++;if(o===B.length||s===G.length)for(o=B.length-1,s=G.length-1;1<=o&&0<=s&&B[o]!==G[s];)s--;for(;1<=o&&0<=s;o--,s--)if(B[o]!==G[s]){if(o!==1||s!==1)do if(o--,s--,0>s||B[o]!==G[s]){var tt=`
`+B[o].replace(" at new "," at ");return t.displayName&&tt.includes("<anonymous>")&&(tt=tt.replace("<anonymous>",t.displayName)),tt}while(1<=o&&0<=s);break}}}finally{Pu=!1,Error.prepareStackTrace=a}return(a=t?t.displayName||t.name:"")?Hr(a):""}function fb(t){switch(t.tag){case 26:case 27:case 5:return Hr(t.type);case 16:return Hr("Lazy");case 13:return Hr("Suspense");case 19:return Hr("SuspenseList");case 0:case 15:return qu(t.type,!1);case 11:return qu(t.type.render,!1);case 1:return qu(t.type,!0);case 31:return Hr("Activity");default:return""}}function Ap(t){try{var e="";do e+=fb(t),t=t.return;while(t);return e}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function An(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Mp(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function db(t){var e=Mp(t)?"checked":"value",a=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),o=""+t[e];if(!t.hasOwnProperty(e)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var s=a.get,f=a.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return s.call(this)},set:function(g){o=""+g,f.call(this,g)}}),Object.defineProperty(t,e,{enumerable:a.enumerable}),{getValue:function(){return o},setValue:function(g){o=""+g},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function $l(t){t._valueTracker||(t._valueTracker=db(t))}function zp(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var a=e.getValue(),o="";return t&&(o=Mp(t)?t.checked?"true":"false":t.value),t=o,t!==a?(e.setValue(t),!0):!1}function jl(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var pb=/[\n"\\]/g;function Mn(t){return t.replace(pb,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Gu(t,e,a,o,s,f,g,b){t.name="",g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"?t.type=g:t.removeAttribute("type"),e!=null?g==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+An(e)):t.value!==""+An(e)&&(t.value=""+An(e)):g!=="submit"&&g!=="reset"||t.removeAttribute("value"),e!=null?Vu(t,g,An(e)):a!=null?Vu(t,g,An(a)):o!=null&&t.removeAttribute("value"),s==null&&f!=null&&(t.defaultChecked=!!f),s!=null&&(t.checked=s&&typeof s!="function"&&typeof s!="symbol"),b!=null&&typeof b!="function"&&typeof b!="symbol"&&typeof b!="boolean"?t.name=""+An(b):t.removeAttribute("name")}function Dp(t,e,a,o,s,f,g,b){if(f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(t.type=f),e!=null||a!=null){if(!(f!=="submit"&&f!=="reset"||e!=null))return;a=a!=null?""+An(a):"",e=e!=null?""+An(e):a,b||e===t.value||(t.value=e),t.defaultValue=e}o=o??s,o=typeof o!="function"&&typeof o!="symbol"&&!!o,t.checked=b?t.checked:!!o,t.defaultChecked=!!o,g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"&&(t.name=g)}function Vu(t,e,a){e==="number"&&jl(t.ownerDocument)===t||t.defaultValue===""+a||(t.defaultValue=""+a)}function Pr(t,e,a,o){if(t=t.options,e){e={};for(var s=0;s<a.length;s++)e["$"+a[s]]=!0;for(a=0;a<t.length;a++)s=e.hasOwnProperty("$"+t[a].value),t[a].selected!==s&&(t[a].selected=s),s&&o&&(t[a].defaultSelected=!0)}else{for(a=""+An(a),e=null,s=0;s<t.length;s++){if(t[s].value===a){t[s].selected=!0,o&&(t[s].defaultSelected=!0);return}e!==null||t[s].disabled||(e=t[s])}e!==null&&(e.selected=!0)}}function Np(t,e,a){if(e!=null&&(e=""+An(e),e!==t.value&&(t.value=e),a==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=a!=null?""+An(a):""}function Bp(t,e,a,o){if(e==null){if(o!=null){if(a!=null)throw Error(l(92));if(F(o)){if(1<o.length)throw Error(l(93));o=o[0]}a=o}a==null&&(a=""),e=a}a=An(e),t.defaultValue=a,o=t.textContent,o===a&&o!==""&&o!==null&&(t.value=o)}function qr(t,e){if(e){var a=t.firstChild;if(a&&a===t.lastChild&&a.nodeType===3){a.nodeValue=e;return}}t.textContent=e}var mb=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function kp(t,e,a){var o=e.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?o?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":o?t.setProperty(e,a):typeof a!="number"||a===0||mb.has(e)?e==="float"?t.cssFloat=a:t[e]=(""+a).trim():t[e]=a+"px"}function $p(t,e,a){if(e!=null&&typeof e!="object")throw Error(l(62));if(t=t.style,a!=null){for(var o in a)!a.hasOwnProperty(o)||e!=null&&e.hasOwnProperty(o)||(o.indexOf("--")===0?t.setProperty(o,""):o==="float"?t.cssFloat="":t[o]="");for(var s in e)o=e[s],e.hasOwnProperty(s)&&a[s]!==o&&kp(t,s,o)}else for(var f in e)e.hasOwnProperty(f)&&kp(t,f,e[f])}function Yu(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var hb=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),gb=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function _l(t){return gb.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Xu=null;function Iu(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Gr=null,Vr=null;function jp(t){var e=_r(t);if(e&&(t=e.stateNode)){var a=t[ln]||null;t:switch(t=e.stateNode,e.type){case"input":if(Gu(t,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),e=a.name,a.type==="radio"&&e!=null){for(a=t;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+Mn(""+e)+'"][type="radio"]'),e=0;e<a.length;e++){var o=a[e];if(o!==t&&o.form===t.form){var s=o[ln]||null;if(!s)throw Error(l(90));Gu(o,s.value,s.defaultValue,s.defaultValue,s.checked,s.defaultChecked,s.type,s.name)}}for(e=0;e<a.length;e++)o=a[e],o.form===t.form&&zp(o)}break t;case"textarea":Np(t,a.value,a.defaultValue);break t;case"select":e=a.value,e!=null&&Pr(t,!!a.multiple,e,!1)}}}var Ku=!1;function _p(t,e,a){if(Ku)return t(e,a);Ku=!0;try{var o=t(e);return o}finally{if(Ku=!1,(Gr!==null||Vr!==null)&&(Cs(),Gr&&(e=Gr,t=Vr,Vr=Gr=null,jp(e),t)))for(e=0;e<t.length;e++)jp(t[e])}}function Zo(t,e){var a=t.stateNode;if(a===null)return null;var o=a[ln]||null;if(o===null)return null;a=o[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(o=!o.disabled)||(t=t.type,o=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!o;break t;default:t=!1}if(t)return null;if(a&&typeof a!="function")throw Error(l(231,e,typeof a));return a}var fa=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Qu=!1;if(fa)try{var Fo={};Object.defineProperty(Fo,"passive",{get:function(){Qu=!0}}),window.addEventListener("test",Fo,Fo),window.removeEventListener("test",Fo,Fo)}catch{Qu=!1}var ka=null,Wu=null,Ul=null;function Up(){if(Ul)return Ul;var t,e=Wu,a=e.length,o,s="value"in ka?ka.value:ka.textContent,f=s.length;for(t=0;t<a&&e[t]===s[t];t++);var g=a-t;for(o=1;o<=g&&e[a-o]===s[f-o];o++);return Ul=s.slice(t,1<o?1-o:void 0)}function Ll(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Hl(){return!0}function Lp(){return!1}function sn(t){function e(a,o,s,f,g){this._reactName=a,this._targetInst=s,this.type=o,this.nativeEvent=f,this.target=g,this.currentTarget=null;for(var b in t)t.hasOwnProperty(b)&&(a=t[b],this[b]=a?a(f):f[b]);return this.isDefaultPrevented=(f.defaultPrevented!=null?f.defaultPrevented:f.returnValue===!1)?Hl:Lp,this.isPropagationStopped=Lp,this}return y(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=Hl)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=Hl)},persist:function(){},isPersistent:Hl}),e}var ur={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Pl=sn(ur),Jo=y({},ur,{view:0,detail:0}),yb=sn(Jo),Zu,Fu,ti,ql=y({},Jo,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:tc,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==ti&&(ti&&t.type==="mousemove"?(Zu=t.screenX-ti.screenX,Fu=t.screenY-ti.screenY):Fu=Zu=0,ti=t),Zu)},movementY:function(t){return"movementY"in t?t.movementY:Fu}}),Hp=sn(ql),vb=y({},ql,{dataTransfer:0}),bb=sn(vb),Sb=y({},Jo,{relatedTarget:0}),Ju=sn(Sb),xb=y({},ur,{animationName:0,elapsedTime:0,pseudoElement:0}),Cb=sn(xb),Tb=y({},ur,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Eb=sn(Tb),Rb=y({},ur,{data:0}),Pp=sn(Rb),wb={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Ob={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ab={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Mb(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Ab[t])?!!e[t]:!1}function tc(){return Mb}var zb=y({},Jo,{key:function(t){if(t.key){var e=wb[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Ll(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Ob[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:tc,charCode:function(t){return t.type==="keypress"?Ll(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Ll(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Db=sn(zb),Nb=y({},ql,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),qp=sn(Nb),Bb=y({},Jo,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:tc}),kb=sn(Bb),$b=y({},ur,{propertyName:0,elapsedTime:0,pseudoElement:0}),jb=sn($b),_b=y({},ql,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Ub=sn(_b),Lb=y({},ur,{newState:0,oldState:0}),Hb=sn(Lb),Pb=[9,13,27,32],ec=fa&&"CompositionEvent"in window,ei=null;fa&&"documentMode"in document&&(ei=document.documentMode);var qb=fa&&"TextEvent"in window&&!ei,Gp=fa&&(!ec||ei&&8<ei&&11>=ei),Vp=" ",Yp=!1;function Xp(t,e){switch(t){case"keyup":return Pb.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ip(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Yr=!1;function Gb(t,e){switch(t){case"compositionend":return Ip(e);case"keypress":return e.which!==32?null:(Yp=!0,Vp);case"textInput":return t=e.data,t===Vp&&Yp?null:t;default:return null}}function Vb(t,e){if(Yr)return t==="compositionend"||!ec&&Xp(t,e)?(t=Up(),Ul=Wu=ka=null,Yr=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Gp&&e.locale!=="ko"?null:e.data;default:return null}}var Yb={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Kp(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Yb[t.type]:e==="textarea"}function Qp(t,e,a,o){Gr?Vr?Vr.push(o):Vr=[o]:Gr=o,e=As(e,"onChange"),0<e.length&&(a=new Pl("onChange","change",null,a,o),t.push({event:a,listeners:e}))}var ni=null,ai=null;function Xb(t){Mg(t,0)}function Gl(t){var e=Wo(t);if(zp(e))return t}function Wp(t,e){if(t==="change")return e}var Zp=!1;if(fa){var nc;if(fa){var ac="oninput"in document;if(!ac){var Fp=document.createElement("div");Fp.setAttribute("oninput","return;"),ac=typeof Fp.oninput=="function"}nc=ac}else nc=!1;Zp=nc&&(!document.documentMode||9<document.documentMode)}function Jp(){ni&&(ni.detachEvent("onpropertychange",tm),ai=ni=null)}function tm(t){if(t.propertyName==="value"&&Gl(ai)){var e=[];Qp(e,ai,t,Iu(t)),_p(Xb,e)}}function Ib(t,e,a){t==="focusin"?(Jp(),ni=e,ai=a,ni.attachEvent("onpropertychange",tm)):t==="focusout"&&Jp()}function Kb(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Gl(ai)}function Qb(t,e){if(t==="click")return Gl(e)}function Wb(t,e){if(t==="input"||t==="change")return Gl(e)}function Zb(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var gn=typeof Object.is=="function"?Object.is:Zb;function ri(t,e){if(gn(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var a=Object.keys(t),o=Object.keys(e);if(a.length!==o.length)return!1;for(o=0;o<a.length;o++){var s=a[o];if(!St.call(e,s)||!gn(t[s],e[s]))return!1}return!0}function em(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function nm(t,e){var a=em(t);t=0;for(var o;a;){if(a.nodeType===3){if(o=t+a.textContent.length,t<=e&&o>=e)return{node:a,offset:e-t};t=o}t:{for(;a;){if(a.nextSibling){a=a.nextSibling;break t}a=a.parentNode}a=void 0}a=em(a)}}function am(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?am(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function rm(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=jl(t.document);e instanceof t.HTMLIFrameElement;){try{var a=typeof e.contentWindow.location.href=="string"}catch{a=!1}if(a)t=e.contentWindow;else break;e=jl(t.document)}return e}function rc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Fb=fa&&"documentMode"in document&&11>=document.documentMode,Xr=null,oc=null,oi=null,ic=!1;function om(t,e,a){var o=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;ic||Xr==null||Xr!==jl(o)||(o=Xr,"selectionStart"in o&&rc(o)?o={start:o.selectionStart,end:o.selectionEnd}:(o=(o.ownerDocument&&o.ownerDocument.defaultView||window).getSelection(),o={anchorNode:o.anchorNode,anchorOffset:o.anchorOffset,focusNode:o.focusNode,focusOffset:o.focusOffset}),oi&&ri(oi,o)||(oi=o,o=As(oc,"onSelect"),0<o.length&&(e=new Pl("onSelect","select",null,e,a),t.push({event:e,listeners:o}),e.target=Xr)))}function cr(t,e){var a={};return a[t.toLowerCase()]=e.toLowerCase(),a["Webkit"+t]="webkit"+e,a["Moz"+t]="moz"+e,a}var Ir={animationend:cr("Animation","AnimationEnd"),animationiteration:cr("Animation","AnimationIteration"),animationstart:cr("Animation","AnimationStart"),transitionrun:cr("Transition","TransitionRun"),transitionstart:cr("Transition","TransitionStart"),transitioncancel:cr("Transition","TransitionCancel"),transitionend:cr("Transition","TransitionEnd")},lc={},im={};fa&&(im=document.createElement("div").style,"AnimationEvent"in window||(delete Ir.animationend.animation,delete Ir.animationiteration.animation,delete Ir.animationstart.animation),"TransitionEvent"in window||delete Ir.transitionend.transition);function fr(t){if(lc[t])return lc[t];if(!Ir[t])return t;var e=Ir[t],a;for(a in e)if(e.hasOwnProperty(a)&&a in im)return lc[t]=e[a];return t}var lm=fr("animationend"),sm=fr("animationiteration"),um=fr("animationstart"),Jb=fr("transitionrun"),t1=fr("transitionstart"),e1=fr("transitioncancel"),cm=fr("transitionend"),fm=new Map,sc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");sc.push("scrollEnd");function Vn(t,e){fm.set(t,e),sr(e,[t])}var dm=new WeakMap;function zn(t,e){if(typeof t=="object"&&t!==null){var a=dm.get(t);return a!==void 0?a:(e={value:t,source:e,stack:Ap(e)},dm.set(t,e),e)}return{value:t,source:e,stack:Ap(e)}}var Dn=[],Kr=0,uc=0;function Vl(){for(var t=Kr,e=uc=Kr=0;e<t;){var a=Dn[e];Dn[e++]=null;var o=Dn[e];Dn[e++]=null;var s=Dn[e];Dn[e++]=null;var f=Dn[e];if(Dn[e++]=null,o!==null&&s!==null){var g=o.pending;g===null?s.next=s:(s.next=g.next,g.next=s),o.pending=s}f!==0&&pm(a,s,f)}}function Yl(t,e,a,o){Dn[Kr++]=t,Dn[Kr++]=e,Dn[Kr++]=a,Dn[Kr++]=o,uc|=o,t.lanes|=o,t=t.alternate,t!==null&&(t.lanes|=o)}function cc(t,e,a,o){return Yl(t,e,a,o),Xl(t)}function Qr(t,e){return Yl(t,null,null,e),Xl(t)}function pm(t,e,a){t.lanes|=a;var o=t.alternate;o!==null&&(o.lanes|=a);for(var s=!1,f=t.return;f!==null;)f.childLanes|=a,o=f.alternate,o!==null&&(o.childLanes|=a),f.tag===22&&(t=f.stateNode,t===null||t._visibility&1||(s=!0)),t=f,f=f.return;return t.tag===3?(f=t.stateNode,s&&e!==null&&(s=31-ne(a),t=f.hiddenUpdates,o=t[s],o===null?t[s]=[e]:o.push(e),e.lane=a|536870912),f):null}function Xl(t){if(50<zi)throw zi=0,yf=null,Error(l(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var Wr={};function n1(t,e,a,o){this.tag=t,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=o,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function yn(t,e,a,o){return new n1(t,e,a,o)}function fc(t){return t=t.prototype,!(!t||!t.isReactComponent)}function da(t,e){var a=t.alternate;return a===null?(a=yn(t.tag,e,t.key,t.mode),a.elementType=t.elementType,a.type=t.type,a.stateNode=t.stateNode,a.alternate=t,t.alternate=a):(a.pendingProps=e,a.type=t.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=t.flags&65011712,a.childLanes=t.childLanes,a.lanes=t.lanes,a.child=t.child,a.memoizedProps=t.memoizedProps,a.memoizedState=t.memoizedState,a.updateQueue=t.updateQueue,e=t.dependencies,a.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},a.sibling=t.sibling,a.index=t.index,a.ref=t.ref,a.refCleanup=t.refCleanup,a}function mm(t,e){t.flags&=65011714;var a=t.alternate;return a===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=a.childLanes,t.lanes=a.lanes,t.child=a.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=a.memoizedProps,t.memoizedState=a.memoizedState,t.updateQueue=a.updateQueue,t.type=a.type,e=a.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Il(t,e,a,o,s,f){var g=0;if(o=t,typeof t=="function")fc(t)&&(g=1);else if(typeof t=="string")g=rS(t,a,lt.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case Q:return t=yn(31,a,e,s),t.elementType=Q,t.lanes=f,t;case x:return dr(a.children,s,f,e);case C:g=8,s|=24;break;case D:return t=yn(12,a,e,s|2),t.elementType=D,t.lanes=f,t;case w:return t=yn(13,a,e,s),t.elementType=w,t.lanes=f,t;case j:return t=yn(19,a,e,s),t.elementType=j,t.lanes=f,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case k:case M:g=10;break t;case U:g=9;break t;case A:g=11;break t;case H:g=14;break t;case q:g=16,o=null;break t}g=29,a=Error(l(130,t===null?"null":typeof t,"")),o=null}return e=yn(g,a,e,s),e.elementType=t,e.type=o,e.lanes=f,e}function dr(t,e,a,o){return t=yn(7,t,o,e),t.lanes=a,t}function dc(t,e,a){return t=yn(6,t,null,e),t.lanes=a,t}function pc(t,e,a){return e=yn(4,t.children!==null?t.children:[],t.key,e),e.lanes=a,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Zr=[],Fr=0,Kl=null,Ql=0,Nn=[],Bn=0,pr=null,pa=1,ma="";function mr(t,e){Zr[Fr++]=Ql,Zr[Fr++]=Kl,Kl=t,Ql=e}function hm(t,e,a){Nn[Bn++]=pa,Nn[Bn++]=ma,Nn[Bn++]=pr,pr=t;var o=pa;t=ma;var s=32-ne(o)-1;o&=~(1<<s),a+=1;var f=32-ne(e)+s;if(30<f){var g=s-s%5;f=(o&(1<<g)-1).toString(32),o>>=g,s-=g,pa=1<<32-ne(e)+s|a<<s|o,ma=f+t}else pa=1<<f|a<<s|o,ma=t}function mc(t){t.return!==null&&(mr(t,1),hm(t,1,0))}function hc(t){for(;t===Kl;)Kl=Zr[--Fr],Zr[Fr]=null,Ql=Zr[--Fr],Zr[Fr]=null;for(;t===pr;)pr=Nn[--Bn],Nn[Bn]=null,ma=Nn[--Bn],Nn[Bn]=null,pa=Nn[--Bn],Nn[Bn]=null}var an=null,we=null,Zt=!1,hr=null,Kn=!1,gc=Error(l(519));function gr(t){var e=Error(l(418,""));throw si(zn(e,t)),gc}function gm(t){var e=t.stateNode,a=t.type,o=t.memoizedProps;switch(e[Fe]=t,e[ln]=o,a){case"dialog":Ht("cancel",e),Ht("close",e);break;case"iframe":case"object":case"embed":Ht("load",e);break;case"video":case"audio":for(a=0;a<Ni.length;a++)Ht(Ni[a],e);break;case"source":Ht("error",e);break;case"img":case"image":case"link":Ht("error",e),Ht("load",e);break;case"details":Ht("toggle",e);break;case"input":Ht("invalid",e),Dp(e,o.value,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name,!0),$l(e);break;case"select":Ht("invalid",e);break;case"textarea":Ht("invalid",e),Bp(e,o.value,o.defaultValue,o.children),$l(e)}a=o.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||e.textContent===""+a||o.suppressHydrationWarning===!0||Bg(e.textContent,a)?(o.popover!=null&&(Ht("beforetoggle",e),Ht("toggle",e)),o.onScroll!=null&&Ht("scroll",e),o.onScrollEnd!=null&&Ht("scrollend",e),o.onClick!=null&&(e.onclick=Ms),e=!0):e=!1,e||gr(t)}function ym(t){for(an=t.return;an;)switch(an.tag){case 5:case 13:Kn=!1;return;case 27:case 3:Kn=!0;return;default:an=an.return}}function ii(t){if(t!==an)return!1;if(!Zt)return ym(t),Zt=!0,!1;var e=t.tag,a;if((a=e!==3&&e!==27)&&((a=e===5)&&(a=t.type,a=!(a!=="form"&&a!=="button")||Bf(t.type,t.memoizedProps)),a=!a),a&&we&&gr(t),ym(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(l(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(a=t.data,a==="/$"){if(e===0){we=Xn(t.nextSibling);break t}e--}else a!=="$"&&a!=="$!"&&a!=="$?"||e++;t=t.nextSibling}we=null}}else e===27?(e=we,Wa(t.type)?(t=_f,_f=null,we=t):we=e):we=an?Xn(t.stateNode.nextSibling):null;return!0}function li(){we=an=null,Zt=!1}function vm(){var t=hr;return t!==null&&(fn===null?fn=t:fn.push.apply(fn,t),hr=null),t}function si(t){hr===null?hr=[t]:hr.push(t)}var yc=X(null),yr=null,ha=null;function $a(t,e,a){nt(yc,e._currentValue),e._currentValue=a}function ga(t){t._currentValue=yc.current,et(yc)}function vc(t,e,a){for(;t!==null;){var o=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,o!==null&&(o.childLanes|=e)):o!==null&&(o.childLanes&e)!==e&&(o.childLanes|=e),t===a)break;t=t.return}}function bc(t,e,a,o){var s=t.child;for(s!==null&&(s.return=t);s!==null;){var f=s.dependencies;if(f!==null){var g=s.child;f=f.firstContext;t:for(;f!==null;){var b=f;f=s;for(var B=0;B<e.length;B++)if(b.context===e[B]){f.lanes|=a,b=f.alternate,b!==null&&(b.lanes|=a),vc(f.return,a,t),o||(g=null);break t}f=b.next}}else if(s.tag===18){if(g=s.return,g===null)throw Error(l(341));g.lanes|=a,f=g.alternate,f!==null&&(f.lanes|=a),vc(g,a,t),g=null}else g=s.child;if(g!==null)g.return=s;else for(g=s;g!==null;){if(g===t){g=null;break}if(s=g.sibling,s!==null){s.return=g.return,g=s;break}g=g.return}s=g}}function ui(t,e,a,o){t=null;for(var s=e,f=!1;s!==null;){if(!f){if((s.flags&524288)!==0)f=!0;else if((s.flags&262144)!==0)break}if(s.tag===10){var g=s.alternate;if(g===null)throw Error(l(387));if(g=g.memoizedProps,g!==null){var b=s.type;gn(s.pendingProps.value,g.value)||(t!==null?t.push(b):t=[b])}}else if(s===Tt.current){if(g=s.alternate,g===null)throw Error(l(387));g.memoizedState.memoizedState!==s.memoizedState.memoizedState&&(t!==null?t.push(Ui):t=[Ui])}s=s.return}t!==null&&bc(e,t,a,o),e.flags|=262144}function Wl(t){for(t=t.firstContext;t!==null;){if(!gn(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function vr(t){yr=t,ha=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Je(t){return bm(yr,t)}function Zl(t,e){return yr===null&&vr(t),bm(t,e)}function bm(t,e){var a=e._currentValue;if(e={context:e,memoizedValue:a,next:null},ha===null){if(t===null)throw Error(l(308));ha=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else ha=ha.next=e;return a}var a1=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(a,o){t.push(o)}};this.abort=function(){e.aborted=!0,t.forEach(function(a){return a()})}},r1=n.unstable_scheduleCallback,o1=n.unstable_NormalPriority,Le={$$typeof:M,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Sc(){return{controller:new a1,data:new Map,refCount:0}}function ci(t){t.refCount--,t.refCount===0&&r1(o1,function(){t.controller.abort()})}var fi=null,xc=0,Jr=0,to=null;function i1(t,e){if(fi===null){var a=fi=[];xc=0,Jr=Ef(),to={status:"pending",value:void 0,then:function(o){a.push(o)}}}return xc++,e.then(Sm,Sm),e}function Sm(){if(--xc===0&&fi!==null){to!==null&&(to.status="fulfilled");var t=fi;fi=null,Jr=0,to=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function l1(t,e){var a=[],o={status:"pending",value:null,reason:null,then:function(s){a.push(s)}};return t.then(function(){o.status="fulfilled",o.value=e;for(var s=0;s<a.length;s++)(0,a[s])(e)},function(s){for(o.status="rejected",o.reason=s,s=0;s<a.length;s++)(0,a[s])(void 0)}),o}var xm=N.S;N.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&i1(t,e),xm!==null&&xm(t,e)};var br=X(null);function Cc(){var t=br.current;return t!==null?t:ge.pooledCache}function Fl(t,e){e===null?nt(br,br.current):nt(br,e.pool)}function Cm(){var t=Cc();return t===null?null:{parent:Le._currentValue,pool:t}}var di=Error(l(460)),Tm=Error(l(474)),Jl=Error(l(542)),Tc={then:function(){}};function Em(t){return t=t.status,t==="fulfilled"||t==="rejected"}function ts(){}function Rm(t,e,a){switch(a=t[a],a===void 0?t.push(e):a!==e&&(e.then(ts,ts),e=a),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Om(t),t;default:if(typeof e.status=="string")e.then(ts,ts);else{if(t=ge,t!==null&&100<t.shellSuspendCounter)throw Error(l(482));t=e,t.status="pending",t.then(function(o){if(e.status==="pending"){var s=e;s.status="fulfilled",s.value=o}},function(o){if(e.status==="pending"){var s=e;s.status="rejected",s.reason=o}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Om(t),t}throw pi=e,di}}var pi=null;function wm(){if(pi===null)throw Error(l(459));var t=pi;return pi=null,t}function Om(t){if(t===di||t===Jl)throw Error(l(483))}var ja=!1;function Ec(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Rc(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function _a(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Ua(t,e,a){var o=t.updateQueue;if(o===null)return null;if(o=o.shared,(re&2)!==0){var s=o.pending;return s===null?e.next=e:(e.next=s.next,s.next=e),o.pending=e,e=Xl(t),pm(t,null,a),e}return Yl(t,o,e,a),Xl(t)}function mi(t,e,a){if(e=e.updateQueue,e!==null&&(e=e.shared,(a&4194048)!==0)){var o=e.lanes;o&=t.pendingLanes,a|=o,e.lanes=a,Dl(t,a)}}function wc(t,e){var a=t.updateQueue,o=t.alternate;if(o!==null&&(o=o.updateQueue,a===o)){var s=null,f=null;if(a=a.firstBaseUpdate,a!==null){do{var g={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};f===null?s=f=g:f=f.next=g,a=a.next}while(a!==null);f===null?s=f=e:f=f.next=e}else s=f=e;a={baseState:o.baseState,firstBaseUpdate:s,lastBaseUpdate:f,shared:o.shared,callbacks:o.callbacks},t.updateQueue=a;return}t=a.lastBaseUpdate,t===null?a.firstBaseUpdate=e:t.next=e,a.lastBaseUpdate=e}var Oc=!1;function hi(){if(Oc){var t=to;if(t!==null)throw t}}function gi(t,e,a,o){Oc=!1;var s=t.updateQueue;ja=!1;var f=s.firstBaseUpdate,g=s.lastBaseUpdate,b=s.shared.pending;if(b!==null){s.shared.pending=null;var B=b,G=B.next;B.next=null,g===null?f=G:g.next=G,g=B;var tt=t.alternate;tt!==null&&(tt=tt.updateQueue,b=tt.lastBaseUpdate,b!==g&&(b===null?tt.firstBaseUpdate=G:b.next=G,tt.lastBaseUpdate=B))}if(f!==null){var rt=s.baseState;g=0,tt=G=B=null,b=f;do{var Y=b.lane&-536870913,I=Y!==b.lane;if(I?(Xt&Y)===Y:(o&Y)===Y){Y!==0&&Y===Jr&&(Oc=!0),tt!==null&&(tt=tt.next={lane:0,tag:b.tag,payload:b.payload,callback:null,next:null});t:{var Rt=t,xt=b;Y=e;var ce=a;switch(xt.tag){case 1:if(Rt=xt.payload,typeof Rt=="function"){rt=Rt.call(ce,rt,Y);break t}rt=Rt;break t;case 3:Rt.flags=Rt.flags&-65537|128;case 0:if(Rt=xt.payload,Y=typeof Rt=="function"?Rt.call(ce,rt,Y):Rt,Y==null)break t;rt=y({},rt,Y);break t;case 2:ja=!0}}Y=b.callback,Y!==null&&(t.flags|=64,I&&(t.flags|=8192),I=s.callbacks,I===null?s.callbacks=[Y]:I.push(Y))}else I={lane:Y,tag:b.tag,payload:b.payload,callback:b.callback,next:null},tt===null?(G=tt=I,B=rt):tt=tt.next=I,g|=Y;if(b=b.next,b===null){if(b=s.shared.pending,b===null)break;I=b,b=I.next,I.next=null,s.lastBaseUpdate=I,s.shared.pending=null}}while(!0);tt===null&&(B=rt),s.baseState=B,s.firstBaseUpdate=G,s.lastBaseUpdate=tt,f===null&&(s.shared.lanes=0),Xa|=g,t.lanes=g,t.memoizedState=rt}}function Am(t,e){if(typeof t!="function")throw Error(l(191,t));t.call(e)}function Mm(t,e){var a=t.callbacks;if(a!==null)for(t.callbacks=null,t=0;t<a.length;t++)Am(a[t],e)}var eo=X(null),es=X(0);function zm(t,e){t=Ta,nt(es,t),nt(eo,e),Ta=t|e.baseLanes}function Ac(){nt(es,Ta),nt(eo,eo.current)}function Mc(){Ta=es.current,et(eo),et(es)}var La=0,kt=null,se=null,ke=null,ns=!1,no=!1,Sr=!1,as=0,yi=0,ao=null,s1=0;function De(){throw Error(l(321))}function zc(t,e){if(e===null)return!1;for(var a=0;a<e.length&&a<t.length;a++)if(!gn(t[a],e[a]))return!1;return!0}function Dc(t,e,a,o,s,f){return La=f,kt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,N.H=t===null||t.memoizedState===null?ph:mh,Sr=!1,f=a(o,s),Sr=!1,no&&(f=Nm(e,a,o,s)),Dm(t),f}function Dm(t){N.H=us;var e=se!==null&&se.next!==null;if(La=0,ke=se=kt=null,ns=!1,yi=0,ao=null,e)throw Error(l(300));t===null||Ge||(t=t.dependencies,t!==null&&Wl(t)&&(Ge=!0))}function Nm(t,e,a,o){kt=t;var s=0;do{if(no&&(ao=null),yi=0,no=!1,25<=s)throw Error(l(301));if(s+=1,ke=se=null,t.updateQueue!=null){var f=t.updateQueue;f.lastEffect=null,f.events=null,f.stores=null,f.memoCache!=null&&(f.memoCache.index=0)}N.H=h1,f=e(a,o)}while(no);return f}function u1(){var t=N.H,e=t.useState()[0];return e=typeof e.then=="function"?vi(e):e,t=t.useState()[0],(se!==null?se.memoizedState:null)!==t&&(kt.flags|=1024),e}function Nc(){var t=as!==0;return as=0,t}function Bc(t,e,a){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~a}function kc(t){if(ns){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}ns=!1}La=0,ke=se=kt=null,no=!1,yi=as=0,ao=null}function un(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ke===null?kt.memoizedState=ke=t:ke=ke.next=t,ke}function $e(){if(se===null){var t=kt.alternate;t=t!==null?t.memoizedState:null}else t=se.next;var e=ke===null?kt.memoizedState:ke.next;if(e!==null)ke=e,se=t;else{if(t===null)throw kt.alternate===null?Error(l(467)):Error(l(310));se=t,t={memoizedState:se.memoizedState,baseState:se.baseState,baseQueue:se.baseQueue,queue:se.queue,next:null},ke===null?kt.memoizedState=ke=t:ke=ke.next=t}return ke}function $c(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function vi(t){var e=yi;return yi+=1,ao===null&&(ao=[]),t=Rm(ao,t,e),e=kt,(ke===null?e.memoizedState:ke.next)===null&&(e=e.alternate,N.H=e===null||e.memoizedState===null?ph:mh),t}function rs(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return vi(t);if(t.$$typeof===M)return Je(t)}throw Error(l(438,String(t)))}function jc(t){var e=null,a=kt.updateQueue;if(a!==null&&(e=a.memoCache),e==null){var o=kt.alternate;o!==null&&(o=o.updateQueue,o!==null&&(o=o.memoCache,o!=null&&(e={data:o.data.map(function(s){return s.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),a===null&&(a=$c(),kt.updateQueue=a),a.memoCache=e,a=e.data[e.index],a===void 0)for(a=e.data[e.index]=Array(t),o=0;o<t;o++)a[o]=v;return e.index++,a}function ya(t,e){return typeof e=="function"?e(t):e}function os(t){var e=$e();return _c(e,se,t)}function _c(t,e,a){var o=t.queue;if(o===null)throw Error(l(311));o.lastRenderedReducer=a;var s=t.baseQueue,f=o.pending;if(f!==null){if(s!==null){var g=s.next;s.next=f.next,f.next=g}e.baseQueue=s=f,o.pending=null}if(f=t.baseState,s===null)t.memoizedState=f;else{e=s.next;var b=g=null,B=null,G=e,tt=!1;do{var rt=G.lane&-536870913;if(rt!==G.lane?(Xt&rt)===rt:(La&rt)===rt){var Y=G.revertLane;if(Y===0)B!==null&&(B=B.next={lane:0,revertLane:0,action:G.action,hasEagerState:G.hasEagerState,eagerState:G.eagerState,next:null}),rt===Jr&&(tt=!0);else if((La&Y)===Y){G=G.next,Y===Jr&&(tt=!0);continue}else rt={lane:0,revertLane:G.revertLane,action:G.action,hasEagerState:G.hasEagerState,eagerState:G.eagerState,next:null},B===null?(b=B=rt,g=f):B=B.next=rt,kt.lanes|=Y,Xa|=Y;rt=G.action,Sr&&a(f,rt),f=G.hasEagerState?G.eagerState:a(f,rt)}else Y={lane:rt,revertLane:G.revertLane,action:G.action,hasEagerState:G.hasEagerState,eagerState:G.eagerState,next:null},B===null?(b=B=Y,g=f):B=B.next=Y,kt.lanes|=rt,Xa|=rt;G=G.next}while(G!==null&&G!==e);if(B===null?g=f:B.next=b,!gn(f,t.memoizedState)&&(Ge=!0,tt&&(a=to,a!==null)))throw a;t.memoizedState=f,t.baseState=g,t.baseQueue=B,o.lastRenderedState=f}return s===null&&(o.lanes=0),[t.memoizedState,o.dispatch]}function Uc(t){var e=$e(),a=e.queue;if(a===null)throw Error(l(311));a.lastRenderedReducer=t;var o=a.dispatch,s=a.pending,f=e.memoizedState;if(s!==null){a.pending=null;var g=s=s.next;do f=t(f,g.action),g=g.next;while(g!==s);gn(f,e.memoizedState)||(Ge=!0),e.memoizedState=f,e.baseQueue===null&&(e.baseState=f),a.lastRenderedState=f}return[f,o]}function Bm(t,e,a){var o=kt,s=$e(),f=Zt;if(f){if(a===void 0)throw Error(l(407));a=a()}else a=e();var g=!gn((se||s).memoizedState,a);g&&(s.memoizedState=a,Ge=!0),s=s.queue;var b=jm.bind(null,o,s,t);if(bi(2048,8,b,[t]),s.getSnapshot!==e||g||ke!==null&&ke.memoizedState.tag&1){if(o.flags|=2048,ro(9,is(),$m.bind(null,o,s,a,e),null),ge===null)throw Error(l(349));f||(La&124)!==0||km(o,e,a)}return a}function km(t,e,a){t.flags|=16384,t={getSnapshot:e,value:a},e=kt.updateQueue,e===null?(e=$c(),kt.updateQueue=e,e.stores=[t]):(a=e.stores,a===null?e.stores=[t]:a.push(t))}function $m(t,e,a,o){e.value=a,e.getSnapshot=o,_m(e)&&Um(t)}function jm(t,e,a){return a(function(){_m(e)&&Um(t)})}function _m(t){var e=t.getSnapshot;t=t.value;try{var a=e();return!gn(t,a)}catch{return!0}}function Um(t){var e=Qr(t,2);e!==null&&Cn(e,t,2)}function Lc(t){var e=un();if(typeof t=="function"){var a=t;if(t=a(),Sr){Te(!0);try{a()}finally{Te(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ya,lastRenderedState:t},e}function Lm(t,e,a,o){return t.baseState=a,_c(t,se,typeof o=="function"?o:ya)}function c1(t,e,a,o,s){if(ss(t))throw Error(l(485));if(t=e.action,t!==null){var f={payload:s,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(g){f.listeners.push(g)}};N.T!==null?a(!0):f.isTransition=!1,o(f),a=e.pending,a===null?(f.next=e.pending=f,Hm(e,f)):(f.next=a.next,e.pending=a.next=f)}}function Hm(t,e){var a=e.action,o=e.payload,s=t.state;if(e.isTransition){var f=N.T,g={};N.T=g;try{var b=a(s,o),B=N.S;B!==null&&B(g,b),Pm(t,e,b)}catch(G){Hc(t,e,G)}finally{N.T=f}}else try{f=a(s,o),Pm(t,e,f)}catch(G){Hc(t,e,G)}}function Pm(t,e,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(o){qm(t,e,o)},function(o){return Hc(t,e,o)}):qm(t,e,a)}function qm(t,e,a){e.status="fulfilled",e.value=a,Gm(e),t.state=a,e=t.pending,e!==null&&(a=e.next,a===e?t.pending=null:(a=a.next,e.next=a,Hm(t,a)))}function Hc(t,e,a){var o=t.pending;if(t.pending=null,o!==null){o=o.next;do e.status="rejected",e.reason=a,Gm(e),e=e.next;while(e!==o)}t.action=null}function Gm(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Vm(t,e){return e}function Ym(t,e){if(Zt){var a=ge.formState;if(a!==null){t:{var o=kt;if(Zt){if(we){e:{for(var s=we,f=Kn;s.nodeType!==8;){if(!f){s=null;break e}if(s=Xn(s.nextSibling),s===null){s=null;break e}}f=s.data,s=f==="F!"||f==="F"?s:null}if(s){we=Xn(s.nextSibling),o=s.data==="F!";break t}}gr(o)}o=!1}o&&(e=a[0])}}return a=un(),a.memoizedState=a.baseState=e,o={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vm,lastRenderedState:e},a.queue=o,a=ch.bind(null,kt,o),o.dispatch=a,o=Lc(!1),f=Yc.bind(null,kt,!1,o.queue),o=un(),s={state:e,dispatch:null,action:t,pending:null},o.queue=s,a=c1.bind(null,kt,s,f,a),s.dispatch=a,o.memoizedState=t,[e,a,!1]}function Xm(t){var e=$e();return Im(e,se,t)}function Im(t,e,a){if(e=_c(t,e,Vm)[0],t=os(ya)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var o=vi(e)}catch(g){throw g===di?Jl:g}else o=e;e=$e();var s=e.queue,f=s.dispatch;return a!==e.memoizedState&&(kt.flags|=2048,ro(9,is(),f1.bind(null,s,a),null)),[o,f,t]}function f1(t,e){t.action=e}function Km(t){var e=$e(),a=se;if(a!==null)return Im(e,a,t);$e(),e=e.memoizedState,a=$e();var o=a.queue.dispatch;return a.memoizedState=t,[e,o,!1]}function ro(t,e,a,o){return t={tag:t,create:a,deps:o,inst:e,next:null},e=kt.updateQueue,e===null&&(e=$c(),kt.updateQueue=e),a=e.lastEffect,a===null?e.lastEffect=t.next=t:(o=a.next,a.next=t,t.next=o,e.lastEffect=t),t}function is(){return{destroy:void 0,resource:void 0}}function Qm(){return $e().memoizedState}function ls(t,e,a,o){var s=un();o=o===void 0?null:o,kt.flags|=t,s.memoizedState=ro(1|e,is(),a,o)}function bi(t,e,a,o){var s=$e();o=o===void 0?null:o;var f=s.memoizedState.inst;se!==null&&o!==null&&zc(o,se.memoizedState.deps)?s.memoizedState=ro(e,f,a,o):(kt.flags|=t,s.memoizedState=ro(1|e,f,a,o))}function Wm(t,e){ls(8390656,8,t,e)}function Zm(t,e){bi(2048,8,t,e)}function Fm(t,e){return bi(4,2,t,e)}function Jm(t,e){return bi(4,4,t,e)}function th(t,e){if(typeof e=="function"){t=t();var a=e(t);return function(){typeof a=="function"?a():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function eh(t,e,a){a=a!=null?a.concat([t]):null,bi(4,4,th.bind(null,e,t),a)}function Pc(){}function nh(t,e){var a=$e();e=e===void 0?null:e;var o=a.memoizedState;return e!==null&&zc(e,o[1])?o[0]:(a.memoizedState=[t,e],t)}function ah(t,e){var a=$e();e=e===void 0?null:e;var o=a.memoizedState;if(e!==null&&zc(e,o[1]))return o[0];if(o=t(),Sr){Te(!0);try{t()}finally{Te(!1)}}return a.memoizedState=[o,e],o}function qc(t,e,a){return a===void 0||(La&1073741824)!==0?t.memoizedState=e:(t.memoizedState=a,t=ig(),kt.lanes|=t,Xa|=t,a)}function rh(t,e,a,o){return gn(a,e)?a:eo.current!==null?(t=qc(t,a,o),gn(t,e)||(Ge=!0),t):(La&42)===0?(Ge=!0,t.memoizedState=a):(t=ig(),kt.lanes|=t,Xa|=t,e)}function oh(t,e,a,o,s){var f=V.p;V.p=f!==0&&8>f?f:8;var g=N.T,b={};N.T=b,Yc(t,!1,e,a);try{var B=s(),G=N.S;if(G!==null&&G(b,B),B!==null&&typeof B=="object"&&typeof B.then=="function"){var tt=l1(B,o);Si(t,e,tt,xn(t))}else Si(t,e,o,xn(t))}catch(rt){Si(t,e,{then:function(){},status:"rejected",reason:rt},xn())}finally{V.p=f,N.T=g}}function d1(){}function Gc(t,e,a,o){if(t.tag!==5)throw Error(l(476));var s=ih(t).queue;oh(t,s,e,ot,a===null?d1:function(){return lh(t),a(o)})}function ih(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:ot,baseState:ot,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ya,lastRenderedState:ot},next:null};var a={};return e.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ya,lastRenderedState:a},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function lh(t){var e=ih(t).next.queue;Si(t,e,{},xn())}function Vc(){return Je(Ui)}function sh(){return $e().memoizedState}function uh(){return $e().memoizedState}function p1(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var a=xn();t=_a(a);var o=Ua(e,t,a);o!==null&&(Cn(o,e,a),mi(o,e,a)),e={cache:Sc()},t.payload=e;return}e=e.return}}function m1(t,e,a){var o=xn();a={lane:o,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},ss(t)?fh(e,a):(a=cc(t,e,a,o),a!==null&&(Cn(a,t,o),dh(a,e,o)))}function ch(t,e,a){var o=xn();Si(t,e,a,o)}function Si(t,e,a,o){var s={lane:o,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(ss(t))fh(e,s);else{var f=t.alternate;if(t.lanes===0&&(f===null||f.lanes===0)&&(f=e.lastRenderedReducer,f!==null))try{var g=e.lastRenderedState,b=f(g,a);if(s.hasEagerState=!0,s.eagerState=b,gn(b,g))return Yl(t,e,s,0),ge===null&&Vl(),!1}catch{}finally{}if(a=cc(t,e,s,o),a!==null)return Cn(a,t,o),dh(a,e,o),!0}return!1}function Yc(t,e,a,o){if(o={lane:2,revertLane:Ef(),action:o,hasEagerState:!1,eagerState:null,next:null},ss(t)){if(e)throw Error(l(479))}else e=cc(t,a,o,2),e!==null&&Cn(e,t,2)}function ss(t){var e=t.alternate;return t===kt||e!==null&&e===kt}function fh(t,e){no=ns=!0;var a=t.pending;a===null?e.next=e:(e.next=a.next,a.next=e),t.pending=e}function dh(t,e,a){if((a&4194048)!==0){var o=e.lanes;o&=t.pendingLanes,a|=o,e.lanes=a,Dl(t,a)}}var us={readContext:Je,use:rs,useCallback:De,useContext:De,useEffect:De,useImperativeHandle:De,useLayoutEffect:De,useInsertionEffect:De,useMemo:De,useReducer:De,useRef:De,useState:De,useDebugValue:De,useDeferredValue:De,useTransition:De,useSyncExternalStore:De,useId:De,useHostTransitionStatus:De,useFormState:De,useActionState:De,useOptimistic:De,useMemoCache:De,useCacheRefresh:De},ph={readContext:Je,use:rs,useCallback:function(t,e){return un().memoizedState=[t,e===void 0?null:e],t},useContext:Je,useEffect:Wm,useImperativeHandle:function(t,e,a){a=a!=null?a.concat([t]):null,ls(4194308,4,th.bind(null,e,t),a)},useLayoutEffect:function(t,e){return ls(4194308,4,t,e)},useInsertionEffect:function(t,e){ls(4,2,t,e)},useMemo:function(t,e){var a=un();e=e===void 0?null:e;var o=t();if(Sr){Te(!0);try{t()}finally{Te(!1)}}return a.memoizedState=[o,e],o},useReducer:function(t,e,a){var o=un();if(a!==void 0){var s=a(e);if(Sr){Te(!0);try{a(e)}finally{Te(!1)}}}else s=e;return o.memoizedState=o.baseState=s,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:s},o.queue=t,t=t.dispatch=m1.bind(null,kt,t),[o.memoizedState,t]},useRef:function(t){var e=un();return t={current:t},e.memoizedState=t},useState:function(t){t=Lc(t);var e=t.queue,a=ch.bind(null,kt,e);return e.dispatch=a,[t.memoizedState,a]},useDebugValue:Pc,useDeferredValue:function(t,e){var a=un();return qc(a,t,e)},useTransition:function(){var t=Lc(!1);return t=oh.bind(null,kt,t.queue,!0,!1),un().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,a){var o=kt,s=un();if(Zt){if(a===void 0)throw Error(l(407));a=a()}else{if(a=e(),ge===null)throw Error(l(349));(Xt&124)!==0||km(o,e,a)}s.memoizedState=a;var f={value:a,getSnapshot:e};return s.queue=f,Wm(jm.bind(null,o,f,t),[t]),o.flags|=2048,ro(9,is(),$m.bind(null,o,f,a,e),null),a},useId:function(){var t=un(),e=ge.identifierPrefix;if(Zt){var a=ma,o=pa;a=(o&~(1<<32-ne(o)-1)).toString(32)+a,e="«"+e+"R"+a,a=as++,0<a&&(e+="H"+a.toString(32)),e+="»"}else a=s1++,e="«"+e+"r"+a.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:Vc,useFormState:Ym,useActionState:Ym,useOptimistic:function(t){var e=un();e.memoizedState=e.baseState=t;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=a,e=Yc.bind(null,kt,!0,a),a.dispatch=e,[t,e]},useMemoCache:jc,useCacheRefresh:function(){return un().memoizedState=p1.bind(null,kt)}},mh={readContext:Je,use:rs,useCallback:nh,useContext:Je,useEffect:Zm,useImperativeHandle:eh,useInsertionEffect:Fm,useLayoutEffect:Jm,useMemo:ah,useReducer:os,useRef:Qm,useState:function(){return os(ya)},useDebugValue:Pc,useDeferredValue:function(t,e){var a=$e();return rh(a,se.memoizedState,t,e)},useTransition:function(){var t=os(ya)[0],e=$e().memoizedState;return[typeof t=="boolean"?t:vi(t),e]},useSyncExternalStore:Bm,useId:sh,useHostTransitionStatus:Vc,useFormState:Xm,useActionState:Xm,useOptimistic:function(t,e){var a=$e();return Lm(a,se,t,e)},useMemoCache:jc,useCacheRefresh:uh},h1={readContext:Je,use:rs,useCallback:nh,useContext:Je,useEffect:Zm,useImperativeHandle:eh,useInsertionEffect:Fm,useLayoutEffect:Jm,useMemo:ah,useReducer:Uc,useRef:Qm,useState:function(){return Uc(ya)},useDebugValue:Pc,useDeferredValue:function(t,e){var a=$e();return se===null?qc(a,t,e):rh(a,se.memoizedState,t,e)},useTransition:function(){var t=Uc(ya)[0],e=$e().memoizedState;return[typeof t=="boolean"?t:vi(t),e]},useSyncExternalStore:Bm,useId:sh,useHostTransitionStatus:Vc,useFormState:Km,useActionState:Km,useOptimistic:function(t,e){var a=$e();return se!==null?Lm(a,se,t,e):(a.baseState=t,[t,a.queue.dispatch])},useMemoCache:jc,useCacheRefresh:uh},oo=null,xi=0;function cs(t){var e=xi;return xi+=1,oo===null&&(oo=[]),Rm(oo,t,e)}function Ci(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function fs(t,e){throw e.$$typeof===S?Error(l(525)):(t=Object.prototype.toString.call(e),Error(l(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function hh(t){var e=t._init;return e(t._payload)}function gh(t){function e(L,$){if(t){var P=L.deletions;P===null?(L.deletions=[$],L.flags|=16):P.push($)}}function a(L,$){if(!t)return null;for(;$!==null;)e(L,$),$=$.sibling;return null}function o(L){for(var $=new Map;L!==null;)L.key!==null?$.set(L.key,L):$.set(L.index,L),L=L.sibling;return $}function s(L,$){return L=da(L,$),L.index=0,L.sibling=null,L}function f(L,$,P){return L.index=P,t?(P=L.alternate,P!==null?(P=P.index,P<$?(L.flags|=67108866,$):P):(L.flags|=67108866,$)):(L.flags|=1048576,$)}function g(L){return t&&L.alternate===null&&(L.flags|=67108866),L}function b(L,$,P,at){return $===null||$.tag!==6?($=dc(P,L.mode,at),$.return=L,$):($=s($,P),$.return=L,$)}function B(L,$,P,at){var pt=P.type;return pt===x?tt(L,$,P.props.children,at,P.key):$!==null&&($.elementType===pt||typeof pt=="object"&&pt!==null&&pt.$$typeof===q&&hh(pt)===$.type)?($=s($,P.props),Ci($,P),$.return=L,$):($=Il(P.type,P.key,P.props,null,L.mode,at),Ci($,P),$.return=L,$)}function G(L,$,P,at){return $===null||$.tag!==4||$.stateNode.containerInfo!==P.containerInfo||$.stateNode.implementation!==P.implementation?($=pc(P,L.mode,at),$.return=L,$):($=s($,P.children||[]),$.return=L,$)}function tt(L,$,P,at,pt){return $===null||$.tag!==7?($=dr(P,L.mode,at,pt),$.return=L,$):($=s($,P),$.return=L,$)}function rt(L,$,P){if(typeof $=="string"&&$!==""||typeof $=="number"||typeof $=="bigint")return $=dc(""+$,L.mode,P),$.return=L,$;if(typeof $=="object"&&$!==null){switch($.$$typeof){case T:return P=Il($.type,$.key,$.props,null,L.mode,P),Ci(P,$),P.return=L,P;case E:return $=pc($,L.mode,P),$.return=L,$;case q:var at=$._init;return $=at($._payload),rt(L,$,P)}if(F($)||K($))return $=dr($,L.mode,P,null),$.return=L,$;if(typeof $.then=="function")return rt(L,cs($),P);if($.$$typeof===M)return rt(L,Zl(L,$),P);fs(L,$)}return null}function Y(L,$,P,at){var pt=$!==null?$.key:null;if(typeof P=="string"&&P!==""||typeof P=="number"||typeof P=="bigint")return pt!==null?null:b(L,$,""+P,at);if(typeof P=="object"&&P!==null){switch(P.$$typeof){case T:return P.key===pt?B(L,$,P,at):null;case E:return P.key===pt?G(L,$,P,at):null;case q:return pt=P._init,P=pt(P._payload),Y(L,$,P,at)}if(F(P)||K(P))return pt!==null?null:tt(L,$,P,at,null);if(typeof P.then=="function")return Y(L,$,cs(P),at);if(P.$$typeof===M)return Y(L,$,Zl(L,P),at);fs(L,P)}return null}function I(L,$,P,at,pt){if(typeof at=="string"&&at!==""||typeof at=="number"||typeof at=="bigint")return L=L.get(P)||null,b($,L,""+at,pt);if(typeof at=="object"&&at!==null){switch(at.$$typeof){case T:return L=L.get(at.key===null?P:at.key)||null,B($,L,at,pt);case E:return L=L.get(at.key===null?P:at.key)||null,G($,L,at,pt);case q:var _t=at._init;return at=_t(at._payload),I(L,$,P,at,pt)}if(F(at)||K(at))return L=L.get(P)||null,tt($,L,at,pt,null);if(typeof at.then=="function")return I(L,$,P,cs(at),pt);if(at.$$typeof===M)return I(L,$,P,Zl($,at),pt);fs($,at)}return null}function Rt(L,$,P,at){for(var pt=null,_t=null,vt=$,Ct=$=0,Ye=null;vt!==null&&Ct<P.length;Ct++){vt.index>Ct?(Ye=vt,vt=null):Ye=vt.sibling;var It=Y(L,vt,P[Ct],at);if(It===null){vt===null&&(vt=Ye);break}t&&vt&&It.alternate===null&&e(L,vt),$=f(It,$,Ct),_t===null?pt=It:_t.sibling=It,_t=It,vt=Ye}if(Ct===P.length)return a(L,vt),Zt&&mr(L,Ct),pt;if(vt===null){for(;Ct<P.length;Ct++)vt=rt(L,P[Ct],at),vt!==null&&($=f(vt,$,Ct),_t===null?pt=vt:_t.sibling=vt,_t=vt);return Zt&&mr(L,Ct),pt}for(vt=o(vt);Ct<P.length;Ct++)Ye=I(vt,L,Ct,P[Ct],at),Ye!==null&&(t&&Ye.alternate!==null&&vt.delete(Ye.key===null?Ct:Ye.key),$=f(Ye,$,Ct),_t===null?pt=Ye:_t.sibling=Ye,_t=Ye);return t&&vt.forEach(function(er){return e(L,er)}),Zt&&mr(L,Ct),pt}function xt(L,$,P,at){if(P==null)throw Error(l(151));for(var pt=null,_t=null,vt=$,Ct=$=0,Ye=null,It=P.next();vt!==null&&!It.done;Ct++,It=P.next()){vt.index>Ct?(Ye=vt,vt=null):Ye=vt.sibling;var er=Y(L,vt,It.value,at);if(er===null){vt===null&&(vt=Ye);break}t&&vt&&er.alternate===null&&e(L,vt),$=f(er,$,Ct),_t===null?pt=er:_t.sibling=er,_t=er,vt=Ye}if(It.done)return a(L,vt),Zt&&mr(L,Ct),pt;if(vt===null){for(;!It.done;Ct++,It=P.next())It=rt(L,It.value,at),It!==null&&($=f(It,$,Ct),_t===null?pt=It:_t.sibling=It,_t=It);return Zt&&mr(L,Ct),pt}for(vt=o(vt);!It.done;Ct++,It=P.next())It=I(vt,L,Ct,It.value,at),It!==null&&(t&&It.alternate!==null&&vt.delete(It.key===null?Ct:It.key),$=f(It,$,Ct),_t===null?pt=It:_t.sibling=It,_t=It);return t&&vt.forEach(function(gS){return e(L,gS)}),Zt&&mr(L,Ct),pt}function ce(L,$,P,at){if(typeof P=="object"&&P!==null&&P.type===x&&P.key===null&&(P=P.props.children),typeof P=="object"&&P!==null){switch(P.$$typeof){case T:t:{for(var pt=P.key;$!==null;){if($.key===pt){if(pt=P.type,pt===x){if($.tag===7){a(L,$.sibling),at=s($,P.props.children),at.return=L,L=at;break t}}else if($.elementType===pt||typeof pt=="object"&&pt!==null&&pt.$$typeof===q&&hh(pt)===$.type){a(L,$.sibling),at=s($,P.props),Ci(at,P),at.return=L,L=at;break t}a(L,$);break}else e(L,$);$=$.sibling}P.type===x?(at=dr(P.props.children,L.mode,at,P.key),at.return=L,L=at):(at=Il(P.type,P.key,P.props,null,L.mode,at),Ci(at,P),at.return=L,L=at)}return g(L);case E:t:{for(pt=P.key;$!==null;){if($.key===pt)if($.tag===4&&$.stateNode.containerInfo===P.containerInfo&&$.stateNode.implementation===P.implementation){a(L,$.sibling),at=s($,P.children||[]),at.return=L,L=at;break t}else{a(L,$);break}else e(L,$);$=$.sibling}at=pc(P,L.mode,at),at.return=L,L=at}return g(L);case q:return pt=P._init,P=pt(P._payload),ce(L,$,P,at)}if(F(P))return Rt(L,$,P,at);if(K(P)){if(pt=K(P),typeof pt!="function")throw Error(l(150));return P=pt.call(P),xt(L,$,P,at)}if(typeof P.then=="function")return ce(L,$,cs(P),at);if(P.$$typeof===M)return ce(L,$,Zl(L,P),at);fs(L,P)}return typeof P=="string"&&P!==""||typeof P=="number"||typeof P=="bigint"?(P=""+P,$!==null&&$.tag===6?(a(L,$.sibling),at=s($,P),at.return=L,L=at):(a(L,$),at=dc(P,L.mode,at),at.return=L,L=at),g(L)):a(L,$)}return function(L,$,P,at){try{xi=0;var pt=ce(L,$,P,at);return oo=null,pt}catch(vt){if(vt===di||vt===Jl)throw vt;var _t=yn(29,vt,null,L.mode);return _t.lanes=at,_t.return=L,_t}finally{}}}var io=gh(!0),yh=gh(!1),kn=X(null),Qn=null;function Ha(t){var e=t.alternate;nt(He,He.current&1),nt(kn,t),Qn===null&&(e===null||eo.current!==null||e.memoizedState!==null)&&(Qn=t)}function vh(t){if(t.tag===22){if(nt(He,He.current),nt(kn,t),Qn===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Qn=t)}}else Pa()}function Pa(){nt(He,He.current),nt(kn,kn.current)}function va(t){et(kn),Qn===t&&(Qn=null),et(He)}var He=X(0);function ds(t){for(var e=t;e!==null;){if(e.tag===13){var a=e.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||jf(a)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Xc(t,e,a,o){e=t.memoizedState,a=a(o,e),a=a==null?e:y({},e,a),t.memoizedState=a,t.lanes===0&&(t.updateQueue.baseState=a)}var Ic={enqueueSetState:function(t,e,a){t=t._reactInternals;var o=xn(),s=_a(o);s.payload=e,a!=null&&(s.callback=a),e=Ua(t,s,o),e!==null&&(Cn(e,t,o),mi(e,t,o))},enqueueReplaceState:function(t,e,a){t=t._reactInternals;var o=xn(),s=_a(o);s.tag=1,s.payload=e,a!=null&&(s.callback=a),e=Ua(t,s,o),e!==null&&(Cn(e,t,o),mi(e,t,o))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var a=xn(),o=_a(a);o.tag=2,e!=null&&(o.callback=e),e=Ua(t,o,a),e!==null&&(Cn(e,t,a),mi(e,t,a))}};function bh(t,e,a,o,s,f,g){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(o,f,g):e.prototype&&e.prototype.isPureReactComponent?!ri(a,o)||!ri(s,f):!0}function Sh(t,e,a,o){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(a,o),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(a,o),e.state!==t&&Ic.enqueueReplaceState(e,e.state,null)}function xr(t,e){var a=e;if("ref"in e){a={};for(var o in e)o!=="ref"&&(a[o]=e[o])}if(t=t.defaultProps){a===e&&(a=y({},a));for(var s in t)a[s]===void 0&&(a[s]=t[s])}return a}var ps=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function xh(t){ps(t)}function Ch(t){console.error(t)}function Th(t){ps(t)}function ms(t,e){try{var a=t.onUncaughtError;a(e.value,{componentStack:e.stack})}catch(o){setTimeout(function(){throw o})}}function Eh(t,e,a){try{var o=t.onCaughtError;o(a.value,{componentStack:a.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(s){setTimeout(function(){throw s})}}function Kc(t,e,a){return a=_a(a),a.tag=3,a.payload={element:null},a.callback=function(){ms(t,e)},a}function Rh(t){return t=_a(t),t.tag=3,t}function wh(t,e,a,o){var s=a.type.getDerivedStateFromError;if(typeof s=="function"){var f=o.value;t.payload=function(){return s(f)},t.callback=function(){Eh(e,a,o)}}var g=a.stateNode;g!==null&&typeof g.componentDidCatch=="function"&&(t.callback=function(){Eh(e,a,o),typeof s!="function"&&(Ia===null?Ia=new Set([this]):Ia.add(this));var b=o.stack;this.componentDidCatch(o.value,{componentStack:b!==null?b:""})})}function g1(t,e,a,o,s){if(a.flags|=32768,o!==null&&typeof o=="object"&&typeof o.then=="function"){if(e=a.alternate,e!==null&&ui(e,a,s,!0),a=kn.current,a!==null){switch(a.tag){case 13:return Qn===null?bf():a.alternate===null&&Oe===0&&(Oe=3),a.flags&=-257,a.flags|=65536,a.lanes=s,o===Tc?a.flags|=16384:(e=a.updateQueue,e===null?a.updateQueue=new Set([o]):e.add(o),xf(t,o,s)),!1;case 22:return a.flags|=65536,o===Tc?a.flags|=16384:(e=a.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([o])},a.updateQueue=e):(a=e.retryQueue,a===null?e.retryQueue=new Set([o]):a.add(o)),xf(t,o,s)),!1}throw Error(l(435,a.tag))}return xf(t,o,s),bf(),!1}if(Zt)return e=kn.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=s,o!==gc&&(t=Error(l(422),{cause:o}),si(zn(t,a)))):(o!==gc&&(e=Error(l(423),{cause:o}),si(zn(e,a))),t=t.current.alternate,t.flags|=65536,s&=-s,t.lanes|=s,o=zn(o,a),s=Kc(t.stateNode,o,s),wc(t,s),Oe!==4&&(Oe=2)),!1;var f=Error(l(520),{cause:o});if(f=zn(f,a),Mi===null?Mi=[f]:Mi.push(f),Oe!==4&&(Oe=2),e===null)return!0;o=zn(o,a),a=e;do{switch(a.tag){case 3:return a.flags|=65536,t=s&-s,a.lanes|=t,t=Kc(a.stateNode,o,t),wc(a,t),!1;case 1:if(e=a.type,f=a.stateNode,(a.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(Ia===null||!Ia.has(f))))return a.flags|=65536,s&=-s,a.lanes|=s,s=Rh(s),wh(s,t,a,o),wc(a,s),!1}a=a.return}while(a!==null);return!1}var Oh=Error(l(461)),Ge=!1;function Xe(t,e,a,o){e.child=t===null?yh(e,null,a,o):io(e,t.child,a,o)}function Ah(t,e,a,o,s){a=a.render;var f=e.ref;if("ref"in o){var g={};for(var b in o)b!=="ref"&&(g[b]=o[b])}else g=o;return vr(e),o=Dc(t,e,a,g,f,s),b=Nc(),t!==null&&!Ge?(Bc(t,e,s),ba(t,e,s)):(Zt&&b&&mc(e),e.flags|=1,Xe(t,e,o,s),e.child)}function Mh(t,e,a,o,s){if(t===null){var f=a.type;return typeof f=="function"&&!fc(f)&&f.defaultProps===void 0&&a.compare===null?(e.tag=15,e.type=f,zh(t,e,f,o,s)):(t=Il(a.type,null,o,e,e.mode,s),t.ref=e.ref,t.return=e,e.child=t)}if(f=t.child,!nf(t,s)){var g=f.memoizedProps;if(a=a.compare,a=a!==null?a:ri,a(g,o)&&t.ref===e.ref)return ba(t,e,s)}return e.flags|=1,t=da(f,o),t.ref=e.ref,t.return=e,e.child=t}function zh(t,e,a,o,s){if(t!==null){var f=t.memoizedProps;if(ri(f,o)&&t.ref===e.ref)if(Ge=!1,e.pendingProps=o=f,nf(t,s))(t.flags&131072)!==0&&(Ge=!0);else return e.lanes=t.lanes,ba(t,e,s)}return Qc(t,e,a,o,s)}function Dh(t,e,a){var o=e.pendingProps,s=o.children,f=t!==null?t.memoizedState:null;if(o.mode==="hidden"){if((e.flags&128)!==0){if(o=f!==null?f.baseLanes|a:a,t!==null){for(s=e.child=t.child,f=0;s!==null;)f=f|s.lanes|s.childLanes,s=s.sibling;e.childLanes=f&~o}else e.childLanes=0,e.child=null;return Nh(t,e,o,a)}if((a&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Fl(e,f!==null?f.cachePool:null),f!==null?zm(e,f):Ac(),vh(e);else return e.lanes=e.childLanes=536870912,Nh(t,e,f!==null?f.baseLanes|a:a,a)}else f!==null?(Fl(e,f.cachePool),zm(e,f),Pa(),e.memoizedState=null):(t!==null&&Fl(e,null),Ac(),Pa());return Xe(t,e,s,a),e.child}function Nh(t,e,a,o){var s=Cc();return s=s===null?null:{parent:Le._currentValue,pool:s},e.memoizedState={baseLanes:a,cachePool:s},t!==null&&Fl(e,null),Ac(),vh(e),t!==null&&ui(t,e,o,!0),null}function hs(t,e){var a=e.ref;if(a===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(l(284));(t===null||t.ref!==a)&&(e.flags|=4194816)}}function Qc(t,e,a,o,s){return vr(e),a=Dc(t,e,a,o,void 0,s),o=Nc(),t!==null&&!Ge?(Bc(t,e,s),ba(t,e,s)):(Zt&&o&&mc(e),e.flags|=1,Xe(t,e,a,s),e.child)}function Bh(t,e,a,o,s,f){return vr(e),e.updateQueue=null,a=Nm(e,o,a,s),Dm(t),o=Nc(),t!==null&&!Ge?(Bc(t,e,f),ba(t,e,f)):(Zt&&o&&mc(e),e.flags|=1,Xe(t,e,a,f),e.child)}function kh(t,e,a,o,s){if(vr(e),e.stateNode===null){var f=Wr,g=a.contextType;typeof g=="object"&&g!==null&&(f=Je(g)),f=new a(o,f),e.memoizedState=f.state!==null&&f.state!==void 0?f.state:null,f.updater=Ic,e.stateNode=f,f._reactInternals=e,f=e.stateNode,f.props=o,f.state=e.memoizedState,f.refs={},Ec(e),g=a.contextType,f.context=typeof g=="object"&&g!==null?Je(g):Wr,f.state=e.memoizedState,g=a.getDerivedStateFromProps,typeof g=="function"&&(Xc(e,a,g,o),f.state=e.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof f.getSnapshotBeforeUpdate=="function"||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(g=f.state,typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount(),g!==f.state&&Ic.enqueueReplaceState(f,f.state,null),gi(e,o,f,s),hi(),f.state=e.memoizedState),typeof f.componentDidMount=="function"&&(e.flags|=4194308),o=!0}else if(t===null){f=e.stateNode;var b=e.memoizedProps,B=xr(a,b);f.props=B;var G=f.context,tt=a.contextType;g=Wr,typeof tt=="object"&&tt!==null&&(g=Je(tt));var rt=a.getDerivedStateFromProps;tt=typeof rt=="function"||typeof f.getSnapshotBeforeUpdate=="function",b=e.pendingProps!==b,tt||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(b||G!==g)&&Sh(e,f,o,g),ja=!1;var Y=e.memoizedState;f.state=Y,gi(e,o,f,s),hi(),G=e.memoizedState,b||Y!==G||ja?(typeof rt=="function"&&(Xc(e,a,rt,o),G=e.memoizedState),(B=ja||bh(e,a,B,o,Y,G,g))?(tt||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount()),typeof f.componentDidMount=="function"&&(e.flags|=4194308)):(typeof f.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=o,e.memoizedState=G),f.props=o,f.state=G,f.context=g,o=B):(typeof f.componentDidMount=="function"&&(e.flags|=4194308),o=!1)}else{f=e.stateNode,Rc(t,e),g=e.memoizedProps,tt=xr(a,g),f.props=tt,rt=e.pendingProps,Y=f.context,G=a.contextType,B=Wr,typeof G=="object"&&G!==null&&(B=Je(G)),b=a.getDerivedStateFromProps,(G=typeof b=="function"||typeof f.getSnapshotBeforeUpdate=="function")||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(g!==rt||Y!==B)&&Sh(e,f,o,B),ja=!1,Y=e.memoizedState,f.state=Y,gi(e,o,f,s),hi();var I=e.memoizedState;g!==rt||Y!==I||ja||t!==null&&t.dependencies!==null&&Wl(t.dependencies)?(typeof b=="function"&&(Xc(e,a,b,o),I=e.memoizedState),(tt=ja||bh(e,a,tt,o,Y,I,B)||t!==null&&t.dependencies!==null&&Wl(t.dependencies))?(G||typeof f.UNSAFE_componentWillUpdate!="function"&&typeof f.componentWillUpdate!="function"||(typeof f.componentWillUpdate=="function"&&f.componentWillUpdate(o,I,B),typeof f.UNSAFE_componentWillUpdate=="function"&&f.UNSAFE_componentWillUpdate(o,I,B)),typeof f.componentDidUpdate=="function"&&(e.flags|=4),typeof f.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof f.componentDidUpdate!="function"||g===t.memoizedProps&&Y===t.memoizedState||(e.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||g===t.memoizedProps&&Y===t.memoizedState||(e.flags|=1024),e.memoizedProps=o,e.memoizedState=I),f.props=o,f.state=I,f.context=B,o=tt):(typeof f.componentDidUpdate!="function"||g===t.memoizedProps&&Y===t.memoizedState||(e.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||g===t.memoizedProps&&Y===t.memoizedState||(e.flags|=1024),o=!1)}return f=o,hs(t,e),o=(e.flags&128)!==0,f||o?(f=e.stateNode,a=o&&typeof a.getDerivedStateFromError!="function"?null:f.render(),e.flags|=1,t!==null&&o?(e.child=io(e,t.child,null,s),e.child=io(e,null,a,s)):Xe(t,e,a,s),e.memoizedState=f.state,t=e.child):t=ba(t,e,s),t}function $h(t,e,a,o){return li(),e.flags|=256,Xe(t,e,a,o),e.child}var Wc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Zc(t){return{baseLanes:t,cachePool:Cm()}}function Fc(t,e,a){return t=t!==null?t.childLanes&~a:0,e&&(t|=$n),t}function jh(t,e,a){var o=e.pendingProps,s=!1,f=(e.flags&128)!==0,g;if((g=f)||(g=t!==null&&t.memoizedState===null?!1:(He.current&2)!==0),g&&(s=!0,e.flags&=-129),g=(e.flags&32)!==0,e.flags&=-33,t===null){if(Zt){if(s?Ha(e):Pa(),Zt){var b=we,B;if(B=b){t:{for(B=b,b=Kn;B.nodeType!==8;){if(!b){b=null;break t}if(B=Xn(B.nextSibling),B===null){b=null;break t}}b=B}b!==null?(e.memoizedState={dehydrated:b,treeContext:pr!==null?{id:pa,overflow:ma}:null,retryLane:536870912,hydrationErrors:null},B=yn(18,null,null,0),B.stateNode=b,B.return=e,e.child=B,an=e,we=null,B=!0):B=!1}B||gr(e)}if(b=e.memoizedState,b!==null&&(b=b.dehydrated,b!==null))return jf(b)?e.lanes=32:e.lanes=536870912,null;va(e)}return b=o.children,o=o.fallback,s?(Pa(),s=e.mode,b=gs({mode:"hidden",children:b},s),o=dr(o,s,a,null),b.return=e,o.return=e,b.sibling=o,e.child=b,s=e.child,s.memoizedState=Zc(a),s.childLanes=Fc(t,g,a),e.memoizedState=Wc,o):(Ha(e),Jc(e,b))}if(B=t.memoizedState,B!==null&&(b=B.dehydrated,b!==null)){if(f)e.flags&256?(Ha(e),e.flags&=-257,e=tf(t,e,a)):e.memoizedState!==null?(Pa(),e.child=t.child,e.flags|=128,e=null):(Pa(),s=o.fallback,b=e.mode,o=gs({mode:"visible",children:o.children},b),s=dr(s,b,a,null),s.flags|=2,o.return=e,s.return=e,o.sibling=s,e.child=o,io(e,t.child,null,a),o=e.child,o.memoizedState=Zc(a),o.childLanes=Fc(t,g,a),e.memoizedState=Wc,e=s);else if(Ha(e),jf(b)){if(g=b.nextSibling&&b.nextSibling.dataset,g)var G=g.dgst;g=G,o=Error(l(419)),o.stack="",o.digest=g,si({value:o,source:null,stack:null}),e=tf(t,e,a)}else if(Ge||ui(t,e,a,!1),g=(a&t.childLanes)!==0,Ge||g){if(g=ge,g!==null&&(o=a&-a,o=(o&42)!==0?1:Ko(o),o=(o&(g.suspendedLanes|a))!==0?0:o,o!==0&&o!==B.retryLane))throw B.retryLane=o,Qr(t,o),Cn(g,t,o),Oh;b.data==="$?"||bf(),e=tf(t,e,a)}else b.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=B.treeContext,we=Xn(b.nextSibling),an=e,Zt=!0,hr=null,Kn=!1,t!==null&&(Nn[Bn++]=pa,Nn[Bn++]=ma,Nn[Bn++]=pr,pa=t.id,ma=t.overflow,pr=e),e=Jc(e,o.children),e.flags|=4096);return e}return s?(Pa(),s=o.fallback,b=e.mode,B=t.child,G=B.sibling,o=da(B,{mode:"hidden",children:o.children}),o.subtreeFlags=B.subtreeFlags&65011712,G!==null?s=da(G,s):(s=dr(s,b,a,null),s.flags|=2),s.return=e,o.return=e,o.sibling=s,e.child=o,o=s,s=e.child,b=t.child.memoizedState,b===null?b=Zc(a):(B=b.cachePool,B!==null?(G=Le._currentValue,B=B.parent!==G?{parent:G,pool:G}:B):B=Cm(),b={baseLanes:b.baseLanes|a,cachePool:B}),s.memoizedState=b,s.childLanes=Fc(t,g,a),e.memoizedState=Wc,o):(Ha(e),a=t.child,t=a.sibling,a=da(a,{mode:"visible",children:o.children}),a.return=e,a.sibling=null,t!==null&&(g=e.deletions,g===null?(e.deletions=[t],e.flags|=16):g.push(t)),e.child=a,e.memoizedState=null,a)}function Jc(t,e){return e=gs({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function gs(t,e){return t=yn(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function tf(t,e,a){return io(e,t.child,null,a),t=Jc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function _h(t,e,a){t.lanes|=e;var o=t.alternate;o!==null&&(o.lanes|=e),vc(t.return,e,a)}function ef(t,e,a,o,s){var f=t.memoizedState;f===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:o,tail:a,tailMode:s}:(f.isBackwards=e,f.rendering=null,f.renderingStartTime=0,f.last=o,f.tail=a,f.tailMode=s)}function Uh(t,e,a){var o=e.pendingProps,s=o.revealOrder,f=o.tail;if(Xe(t,e,o.children,a),o=He.current,(o&2)!==0)o=o&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&_h(t,a,e);else if(t.tag===19)_h(t,a,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}o&=1}switch(nt(He,o),s){case"forwards":for(a=e.child,s=null;a!==null;)t=a.alternate,t!==null&&ds(t)===null&&(s=a),a=a.sibling;a=s,a===null?(s=e.child,e.child=null):(s=a.sibling,a.sibling=null),ef(e,!1,s,a,f);break;case"backwards":for(a=null,s=e.child,e.child=null;s!==null;){if(t=s.alternate,t!==null&&ds(t)===null){e.child=s;break}t=s.sibling,s.sibling=a,a=s,s=t}ef(e,!0,a,null,f);break;case"together":ef(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function ba(t,e,a){if(t!==null&&(e.dependencies=t.dependencies),Xa|=e.lanes,(a&e.childLanes)===0)if(t!==null){if(ui(t,e,a,!1),(a&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(l(153));if(e.child!==null){for(t=e.child,a=da(t,t.pendingProps),e.child=a,a.return=e;t.sibling!==null;)t=t.sibling,a=a.sibling=da(t,t.pendingProps),a.return=e;a.sibling=null}return e.child}function nf(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Wl(t)))}function y1(t,e,a){switch(e.tag){case 3:bt(e,e.stateNode.containerInfo),$a(e,Le,t.memoizedState.cache),li();break;case 27:case 5:yt(e);break;case 4:bt(e,e.stateNode.containerInfo);break;case 10:$a(e,e.type,e.memoizedProps.value);break;case 13:var o=e.memoizedState;if(o!==null)return o.dehydrated!==null?(Ha(e),e.flags|=128,null):(a&e.child.childLanes)!==0?jh(t,e,a):(Ha(e),t=ba(t,e,a),t!==null?t.sibling:null);Ha(e);break;case 19:var s=(t.flags&128)!==0;if(o=(a&e.childLanes)!==0,o||(ui(t,e,a,!1),o=(a&e.childLanes)!==0),s){if(o)return Uh(t,e,a);e.flags|=128}if(s=e.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),nt(He,He.current),o)break;return null;case 22:case 23:return e.lanes=0,Dh(t,e,a);case 24:$a(e,Le,t.memoizedState.cache)}return ba(t,e,a)}function Lh(t,e,a){if(t!==null)if(t.memoizedProps!==e.pendingProps)Ge=!0;else{if(!nf(t,a)&&(e.flags&128)===0)return Ge=!1,y1(t,e,a);Ge=(t.flags&131072)!==0}else Ge=!1,Zt&&(e.flags&1048576)!==0&&hm(e,Ql,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var o=e.elementType,s=o._init;if(o=s(o._payload),e.type=o,typeof o=="function")fc(o)?(t=xr(o,t),e.tag=1,e=kh(null,e,o,t,a)):(e.tag=0,e=Qc(null,e,o,t,a));else{if(o!=null){if(s=o.$$typeof,s===A){e.tag=11,e=Ah(null,e,o,t,a);break t}else if(s===H){e.tag=14,e=Mh(null,e,o,t,a);break t}}throw e=it(o)||o,Error(l(306,e,""))}}return e;case 0:return Qc(t,e,e.type,e.pendingProps,a);case 1:return o=e.type,s=xr(o,e.pendingProps),kh(t,e,o,s,a);case 3:t:{if(bt(e,e.stateNode.containerInfo),t===null)throw Error(l(387));o=e.pendingProps;var f=e.memoizedState;s=f.element,Rc(t,e),gi(e,o,null,a);var g=e.memoizedState;if(o=g.cache,$a(e,Le,o),o!==f.cache&&bc(e,[Le],a,!0),hi(),o=g.element,f.isDehydrated)if(f={element:o,isDehydrated:!1,cache:g.cache},e.updateQueue.baseState=f,e.memoizedState=f,e.flags&256){e=$h(t,e,o,a);break t}else if(o!==s){s=zn(Error(l(424)),e),si(s),e=$h(t,e,o,a);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(we=Xn(t.firstChild),an=e,Zt=!0,hr=null,Kn=!0,a=yh(e,null,o,a),e.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(li(),o===s){e=ba(t,e,a);break t}Xe(t,e,o,a)}e=e.child}return e;case 26:return hs(t,e),t===null?(a=Gg(e.type,null,e.pendingProps,null))?e.memoizedState=a:Zt||(a=e.type,t=e.pendingProps,o=zs(ft.current).createElement(a),o[Fe]=e,o[ln]=t,Ke(o,a,t),qe(o),e.stateNode=o):e.memoizedState=Gg(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return yt(e),t===null&&Zt&&(o=e.stateNode=Hg(e.type,e.pendingProps,ft.current),an=e,Kn=!0,s=we,Wa(e.type)?(_f=s,we=Xn(o.firstChild)):we=s),Xe(t,e,e.pendingProps.children,a),hs(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&Zt&&((s=o=we)&&(o=Y1(o,e.type,e.pendingProps,Kn),o!==null?(e.stateNode=o,an=e,we=Xn(o.firstChild),Kn=!1,s=!0):s=!1),s||gr(e)),yt(e),s=e.type,f=e.pendingProps,g=t!==null?t.memoizedProps:null,o=f.children,Bf(s,f)?o=null:g!==null&&Bf(s,g)&&(e.flags|=32),e.memoizedState!==null&&(s=Dc(t,e,u1,null,null,a),Ui._currentValue=s),hs(t,e),Xe(t,e,o,a),e.child;case 6:return t===null&&Zt&&((t=a=we)&&(a=X1(a,e.pendingProps,Kn),a!==null?(e.stateNode=a,an=e,we=null,t=!0):t=!1),t||gr(e)),null;case 13:return jh(t,e,a);case 4:return bt(e,e.stateNode.containerInfo),o=e.pendingProps,t===null?e.child=io(e,null,o,a):Xe(t,e,o,a),e.child;case 11:return Ah(t,e,e.type,e.pendingProps,a);case 7:return Xe(t,e,e.pendingProps,a),e.child;case 8:return Xe(t,e,e.pendingProps.children,a),e.child;case 12:return Xe(t,e,e.pendingProps.children,a),e.child;case 10:return o=e.pendingProps,$a(e,e.type,o.value),Xe(t,e,o.children,a),e.child;case 9:return s=e.type._context,o=e.pendingProps.children,vr(e),s=Je(s),o=o(s),e.flags|=1,Xe(t,e,o,a),e.child;case 14:return Mh(t,e,e.type,e.pendingProps,a);case 15:return zh(t,e,e.type,e.pendingProps,a);case 19:return Uh(t,e,a);case 31:return o=e.pendingProps,a=e.mode,o={mode:o.mode,children:o.children},t===null?(a=gs(o,a),a.ref=e.ref,e.child=a,a.return=e,e=a):(a=da(t.child,o),a.ref=e.ref,e.child=a,a.return=e,e=a),e;case 22:return Dh(t,e,a);case 24:return vr(e),o=Je(Le),t===null?(s=Cc(),s===null&&(s=ge,f=Sc(),s.pooledCache=f,f.refCount++,f!==null&&(s.pooledCacheLanes|=a),s=f),e.memoizedState={parent:o,cache:s},Ec(e),$a(e,Le,s)):((t.lanes&a)!==0&&(Rc(t,e),gi(e,null,null,a),hi()),s=t.memoizedState,f=e.memoizedState,s.parent!==o?(s={parent:o,cache:o},e.memoizedState=s,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=s),$a(e,Le,o)):(o=f.cache,$a(e,Le,o),o!==s.cache&&bc(e,[Le],a,!0))),Xe(t,e,e.pendingProps.children,a),e.child;case 29:throw e.pendingProps}throw Error(l(156,e.tag))}function Sa(t){t.flags|=4}function Hh(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Kg(e)){if(e=kn.current,e!==null&&((Xt&4194048)===Xt?Qn!==null:(Xt&62914560)!==Xt&&(Xt&536870912)===0||e!==Qn))throw pi=Tc,Tm;t.flags|=8192}}function ys(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Ml():536870912,t.lanes|=e,co|=e)}function Ti(t,e){if(!Zt)switch(t.tailMode){case"hidden":e=t.tail;for(var a=null;e!==null;)e.alternate!==null&&(a=e),e=e.sibling;a===null?t.tail=null:a.sibling=null;break;case"collapsed":a=t.tail;for(var o=null;a!==null;)a.alternate!==null&&(o=a),a=a.sibling;o===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:o.sibling=null}}function Ee(t){var e=t.alternate!==null&&t.alternate.child===t.child,a=0,o=0;if(e)for(var s=t.child;s!==null;)a|=s.lanes|s.childLanes,o|=s.subtreeFlags&65011712,o|=s.flags&65011712,s.return=t,s=s.sibling;else for(s=t.child;s!==null;)a|=s.lanes|s.childLanes,o|=s.subtreeFlags,o|=s.flags,s.return=t,s=s.sibling;return t.subtreeFlags|=o,t.childLanes=a,e}function v1(t,e,a){var o=e.pendingProps;switch(hc(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ee(e),null;case 1:return Ee(e),null;case 3:return a=e.stateNode,o=null,t!==null&&(o=t.memoizedState.cache),e.memoizedState.cache!==o&&(e.flags|=2048),ga(Le),Ot(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(t===null||t.child===null)&&(ii(e)?Sa(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,vm())),Ee(e),null;case 26:return a=e.memoizedState,t===null?(Sa(e),a!==null?(Ee(e),Hh(e,a)):(Ee(e),e.flags&=-16777217)):a?a!==t.memoizedState?(Sa(e),Ee(e),Hh(e,a)):(Ee(e),e.flags&=-16777217):(t.memoizedProps!==o&&Sa(e),Ee(e),e.flags&=-16777217),null;case 27:wt(e),a=ft.current;var s=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==o&&Sa(e);else{if(!o){if(e.stateNode===null)throw Error(l(166));return Ee(e),null}t=lt.current,ii(e)?gm(e):(t=Hg(s,o,a),e.stateNode=t,Sa(e))}return Ee(e),null;case 5:if(wt(e),a=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==o&&Sa(e);else{if(!o){if(e.stateNode===null)throw Error(l(166));return Ee(e),null}if(t=lt.current,ii(e))gm(e);else{switch(s=zs(ft.current),t){case 1:t=s.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:t=s.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":t=s.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":t=s.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":t=s.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof o.is=="string"?s.createElement("select",{is:o.is}):s.createElement("select"),o.multiple?t.multiple=!0:o.size&&(t.size=o.size);break;default:t=typeof o.is=="string"?s.createElement(a,{is:o.is}):s.createElement(a)}}t[Fe]=e,t[ln]=o;t:for(s=e.child;s!==null;){if(s.tag===5||s.tag===6)t.appendChild(s.stateNode);else if(s.tag!==4&&s.tag!==27&&s.child!==null){s.child.return=s,s=s.child;continue}if(s===e)break t;for(;s.sibling===null;){if(s.return===null||s.return===e)break t;s=s.return}s.sibling.return=s.return,s=s.sibling}e.stateNode=t;t:switch(Ke(t,a,o),a){case"button":case"input":case"select":case"textarea":t=!!o.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&Sa(e)}}return Ee(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==o&&Sa(e);else{if(typeof o!="string"&&e.stateNode===null)throw Error(l(166));if(t=ft.current,ii(e)){if(t=e.stateNode,a=e.memoizedProps,o=null,s=an,s!==null)switch(s.tag){case 27:case 5:o=s.memoizedProps}t[Fe]=e,t=!!(t.nodeValue===a||o!==null&&o.suppressHydrationWarning===!0||Bg(t.nodeValue,a)),t||gr(e)}else t=zs(t).createTextNode(o),t[Fe]=e,e.stateNode=t}return Ee(e),null;case 13:if(o=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(s=ii(e),o!==null&&o.dehydrated!==null){if(t===null){if(!s)throw Error(l(318));if(s=e.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(l(317));s[Fe]=e}else li(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Ee(e),s=!1}else s=vm(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=s),s=!0;if(!s)return e.flags&256?(va(e),e):(va(e),null)}if(va(e),(e.flags&128)!==0)return e.lanes=a,e;if(a=o!==null,t=t!==null&&t.memoizedState!==null,a){o=e.child,s=null,o.alternate!==null&&o.alternate.memoizedState!==null&&o.alternate.memoizedState.cachePool!==null&&(s=o.alternate.memoizedState.cachePool.pool);var f=null;o.memoizedState!==null&&o.memoizedState.cachePool!==null&&(f=o.memoizedState.cachePool.pool),f!==s&&(o.flags|=2048)}return a!==t&&a&&(e.child.flags|=8192),ys(e,e.updateQueue),Ee(e),null;case 4:return Ot(),t===null&&Af(e.stateNode.containerInfo),Ee(e),null;case 10:return ga(e.type),Ee(e),null;case 19:if(et(He),s=e.memoizedState,s===null)return Ee(e),null;if(o=(e.flags&128)!==0,f=s.rendering,f===null)if(o)Ti(s,!1);else{if(Oe!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(f=ds(t),f!==null){for(e.flags|=128,Ti(s,!1),t=f.updateQueue,e.updateQueue=t,ys(e,t),e.subtreeFlags=0,t=a,a=e.child;a!==null;)mm(a,t),a=a.sibling;return nt(He,He.current&1|2),e.child}t=t.sibling}s.tail!==null&&Ut()>Ss&&(e.flags|=128,o=!0,Ti(s,!1),e.lanes=4194304)}else{if(!o)if(t=ds(f),t!==null){if(e.flags|=128,o=!0,t=t.updateQueue,e.updateQueue=t,ys(e,t),Ti(s,!0),s.tail===null&&s.tailMode==="hidden"&&!f.alternate&&!Zt)return Ee(e),null}else 2*Ut()-s.renderingStartTime>Ss&&a!==536870912&&(e.flags|=128,o=!0,Ti(s,!1),e.lanes=4194304);s.isBackwards?(f.sibling=e.child,e.child=f):(t=s.last,t!==null?t.sibling=f:e.child=f,s.last=f)}return s.tail!==null?(e=s.tail,s.rendering=e,s.tail=e.sibling,s.renderingStartTime=Ut(),e.sibling=null,t=He.current,nt(He,o?t&1|2:t&1),e):(Ee(e),null);case 22:case 23:return va(e),Mc(),o=e.memoizedState!==null,t!==null?t.memoizedState!==null!==o&&(e.flags|=8192):o&&(e.flags|=8192),o?(a&536870912)!==0&&(e.flags&128)===0&&(Ee(e),e.subtreeFlags&6&&(e.flags|=8192)):Ee(e),a=e.updateQueue,a!==null&&ys(e,a.retryQueue),a=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),o=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(o=e.memoizedState.cachePool.pool),o!==a&&(e.flags|=2048),t!==null&&et(br),null;case 24:return a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),ga(Le),Ee(e),null;case 25:return null;case 30:return null}throw Error(l(156,e.tag))}function b1(t,e){switch(hc(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return ga(Le),Ot(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return wt(e),null;case 13:if(va(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(l(340));li()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return et(He),null;case 4:return Ot(),null;case 10:return ga(e.type),null;case 22:case 23:return va(e),Mc(),t!==null&&et(br),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return ga(Le),null;case 25:return null;default:return null}}function Ph(t,e){switch(hc(e),e.tag){case 3:ga(Le),Ot();break;case 26:case 27:case 5:wt(e);break;case 4:Ot();break;case 13:va(e);break;case 19:et(He);break;case 10:ga(e.type);break;case 22:case 23:va(e),Mc(),t!==null&&et(br);break;case 24:ga(Le)}}function Ei(t,e){try{var a=e.updateQueue,o=a!==null?a.lastEffect:null;if(o!==null){var s=o.next;a=s;do{if((a.tag&t)===t){o=void 0;var f=a.create,g=a.inst;o=f(),g.destroy=o}a=a.next}while(a!==s)}}catch(b){pe(e,e.return,b)}}function qa(t,e,a){try{var o=e.updateQueue,s=o!==null?o.lastEffect:null;if(s!==null){var f=s.next;o=f;do{if((o.tag&t)===t){var g=o.inst,b=g.destroy;if(b!==void 0){g.destroy=void 0,s=e;var B=a,G=b;try{G()}catch(tt){pe(s,B,tt)}}}o=o.next}while(o!==f)}}catch(tt){pe(e,e.return,tt)}}function qh(t){var e=t.updateQueue;if(e!==null){var a=t.stateNode;try{Mm(e,a)}catch(o){pe(t,t.return,o)}}}function Gh(t,e,a){a.props=xr(t.type,t.memoizedProps),a.state=t.memoizedState;try{a.componentWillUnmount()}catch(o){pe(t,e,o)}}function Ri(t,e){try{var a=t.ref;if(a!==null){switch(t.tag){case 26:case 27:case 5:var o=t.stateNode;break;case 30:o=t.stateNode;break;default:o=t.stateNode}typeof a=="function"?t.refCleanup=a(o):a.current=o}}catch(s){pe(t,e,s)}}function Wn(t,e){var a=t.ref,o=t.refCleanup;if(a!==null)if(typeof o=="function")try{o()}catch(s){pe(t,e,s)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(s){pe(t,e,s)}else a.current=null}function Vh(t){var e=t.type,a=t.memoizedProps,o=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":a.autoFocus&&o.focus();break t;case"img":a.src?o.src=a.src:a.srcSet&&(o.srcset=a.srcSet)}}catch(s){pe(t,t.return,s)}}function af(t,e,a){try{var o=t.stateNode;H1(o,t.type,a,e),o[ln]=e}catch(s){pe(t,t.return,s)}}function Yh(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Wa(t.type)||t.tag===4}function rf(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Yh(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Wa(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function of(t,e,a){var o=t.tag;if(o===5||o===6)t=t.stateNode,e?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(t,e):(e=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,e.appendChild(t),a=a._reactRootContainer,a!=null||e.onclick!==null||(e.onclick=Ms));else if(o!==4&&(o===27&&Wa(t.type)&&(a=t.stateNode,e=null),t=t.child,t!==null))for(of(t,e,a),t=t.sibling;t!==null;)of(t,e,a),t=t.sibling}function vs(t,e,a){var o=t.tag;if(o===5||o===6)t=t.stateNode,e?a.insertBefore(t,e):a.appendChild(t);else if(o!==4&&(o===27&&Wa(t.type)&&(a=t.stateNode),t=t.child,t!==null))for(vs(t,e,a),t=t.sibling;t!==null;)vs(t,e,a),t=t.sibling}function Xh(t){var e=t.stateNode,a=t.memoizedProps;try{for(var o=t.type,s=e.attributes;s.length;)e.removeAttributeNode(s[0]);Ke(e,o,a),e[Fe]=t,e[ln]=a}catch(f){pe(t,t.return,f)}}var xa=!1,Ne=!1,lf=!1,Ih=typeof WeakSet=="function"?WeakSet:Set,Ve=null;function S1(t,e){if(t=t.containerInfo,Df=js,t=rm(t),rc(t)){if("selectionStart"in t)var a={start:t.selectionStart,end:t.selectionEnd};else t:{a=(a=t.ownerDocument)&&a.defaultView||window;var o=a.getSelection&&a.getSelection();if(o&&o.rangeCount!==0){a=o.anchorNode;var s=o.anchorOffset,f=o.focusNode;o=o.focusOffset;try{a.nodeType,f.nodeType}catch{a=null;break t}var g=0,b=-1,B=-1,G=0,tt=0,rt=t,Y=null;e:for(;;){for(var I;rt!==a||s!==0&&rt.nodeType!==3||(b=g+s),rt!==f||o!==0&&rt.nodeType!==3||(B=g+o),rt.nodeType===3&&(g+=rt.nodeValue.length),(I=rt.firstChild)!==null;)Y=rt,rt=I;for(;;){if(rt===t)break e;if(Y===a&&++G===s&&(b=g),Y===f&&++tt===o&&(B=g),(I=rt.nextSibling)!==null)break;rt=Y,Y=rt.parentNode}rt=I}a=b===-1||B===-1?null:{start:b,end:B}}else a=null}a=a||{start:0,end:0}}else a=null;for(Nf={focusedElem:t,selectionRange:a},js=!1,Ve=e;Ve!==null;)if(e=Ve,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Ve=t;else for(;Ve!==null;){switch(e=Ve,f=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&f!==null){t=void 0,a=e,s=f.memoizedProps,f=f.memoizedState,o=a.stateNode;try{var Rt=xr(a.type,s,a.elementType===a.type);t=o.getSnapshotBeforeUpdate(Rt,f),o.__reactInternalSnapshotBeforeUpdate=t}catch(xt){pe(a,a.return,xt)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,a=t.nodeType,a===9)$f(t);else if(a===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":$f(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(l(163))}if(t=e.sibling,t!==null){t.return=e.return,Ve=t;break}Ve=e.return}}function Kh(t,e,a){var o=a.flags;switch(a.tag){case 0:case 11:case 15:Ga(t,a),o&4&&Ei(5,a);break;case 1:if(Ga(t,a),o&4)if(t=a.stateNode,e===null)try{t.componentDidMount()}catch(g){pe(a,a.return,g)}else{var s=xr(a.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(s,e,t.__reactInternalSnapshotBeforeUpdate)}catch(g){pe(a,a.return,g)}}o&64&&qh(a),o&512&&Ri(a,a.return);break;case 3:if(Ga(t,a),o&64&&(t=a.updateQueue,t!==null)){if(e=null,a.child!==null)switch(a.child.tag){case 27:case 5:e=a.child.stateNode;break;case 1:e=a.child.stateNode}try{Mm(t,e)}catch(g){pe(a,a.return,g)}}break;case 27:e===null&&o&4&&Xh(a);case 26:case 5:Ga(t,a),e===null&&o&4&&Vh(a),o&512&&Ri(a,a.return);break;case 12:Ga(t,a);break;case 13:Ga(t,a),o&4&&Zh(t,a),o&64&&(t=a.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(a=M1.bind(null,a),I1(t,a))));break;case 22:if(o=a.memoizedState!==null||xa,!o){e=e!==null&&e.memoizedState!==null||Ne,s=xa;var f=Ne;xa=o,(Ne=e)&&!f?Va(t,a,(a.subtreeFlags&8772)!==0):Ga(t,a),xa=s,Ne=f}break;case 30:break;default:Ga(t,a)}}function Qh(t){var e=t.alternate;e!==null&&(t.alternate=null,Qh(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Lu(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Se=null,cn=!1;function Ca(t,e,a){for(a=a.child;a!==null;)Wh(t,e,a),a=a.sibling}function Wh(t,e,a){if(ee&&typeof ee.onCommitFiberUnmount=="function")try{ee.onCommitFiberUnmount(Ue,a)}catch{}switch(a.tag){case 26:Ne||Wn(a,e),Ca(t,e,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Ne||Wn(a,e);var o=Se,s=cn;Wa(a.type)&&(Se=a.stateNode,cn=!1),Ca(t,e,a),ki(a.stateNode),Se=o,cn=s;break;case 5:Ne||Wn(a,e);case 6:if(o=Se,s=cn,Se=null,Ca(t,e,a),Se=o,cn=s,Se!==null)if(cn)try{(Se.nodeType===9?Se.body:Se.nodeName==="HTML"?Se.ownerDocument.body:Se).removeChild(a.stateNode)}catch(f){pe(a,e,f)}else try{Se.removeChild(a.stateNode)}catch(f){pe(a,e,f)}break;case 18:Se!==null&&(cn?(t=Se,Ug(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,a.stateNode),qi(t)):Ug(Se,a.stateNode));break;case 4:o=Se,s=cn,Se=a.stateNode.containerInfo,cn=!0,Ca(t,e,a),Se=o,cn=s;break;case 0:case 11:case 14:case 15:Ne||qa(2,a,e),Ne||qa(4,a,e),Ca(t,e,a);break;case 1:Ne||(Wn(a,e),o=a.stateNode,typeof o.componentWillUnmount=="function"&&Gh(a,e,o)),Ca(t,e,a);break;case 21:Ca(t,e,a);break;case 22:Ne=(o=Ne)||a.memoizedState!==null,Ca(t,e,a),Ne=o;break;default:Ca(t,e,a)}}function Zh(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{qi(t)}catch(a){pe(e,e.return,a)}}function x1(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Ih),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Ih),e;default:throw Error(l(435,t.tag))}}function sf(t,e){var a=x1(t);e.forEach(function(o){var s=z1.bind(null,t,o);a.has(o)||(a.add(o),o.then(s,s))})}function vn(t,e){var a=e.deletions;if(a!==null)for(var o=0;o<a.length;o++){var s=a[o],f=t,g=e,b=g;t:for(;b!==null;){switch(b.tag){case 27:if(Wa(b.type)){Se=b.stateNode,cn=!1;break t}break;case 5:Se=b.stateNode,cn=!1;break t;case 3:case 4:Se=b.stateNode.containerInfo,cn=!0;break t}b=b.return}if(Se===null)throw Error(l(160));Wh(f,g,s),Se=null,cn=!1,f=s.alternate,f!==null&&(f.return=null),s.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Fh(e,t),e=e.sibling}var Yn=null;function Fh(t,e){var a=t.alternate,o=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:vn(e,t),bn(t),o&4&&(qa(3,t,t.return),Ei(3,t),qa(5,t,t.return));break;case 1:vn(e,t),bn(t),o&512&&(Ne||a===null||Wn(a,a.return)),o&64&&xa&&(t=t.updateQueue,t!==null&&(o=t.callbacks,o!==null&&(a=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=a===null?o:a.concat(o))));break;case 26:var s=Yn;if(vn(e,t),bn(t),o&512&&(Ne||a===null||Wn(a,a.return)),o&4){var f=a!==null?a.memoizedState:null;if(o=t.memoizedState,a===null)if(o===null)if(t.stateNode===null){t:{o=t.type,a=t.memoizedProps,s=s.ownerDocument||s;e:switch(o){case"title":f=s.getElementsByTagName("title")[0],(!f||f[Qo]||f[Fe]||f.namespaceURI==="http://www.w3.org/2000/svg"||f.hasAttribute("itemprop"))&&(f=s.createElement(o),s.head.insertBefore(f,s.querySelector("head > title"))),Ke(f,o,a),f[Fe]=t,qe(f),o=f;break t;case"link":var g=Xg("link","href",s).get(o+(a.href||""));if(g){for(var b=0;b<g.length;b++)if(f=g[b],f.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&f.getAttribute("rel")===(a.rel==null?null:a.rel)&&f.getAttribute("title")===(a.title==null?null:a.title)&&f.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){g.splice(b,1);break e}}f=s.createElement(o),Ke(f,o,a),s.head.appendChild(f);break;case"meta":if(g=Xg("meta","content",s).get(o+(a.content||""))){for(b=0;b<g.length;b++)if(f=g[b],f.getAttribute("content")===(a.content==null?null:""+a.content)&&f.getAttribute("name")===(a.name==null?null:a.name)&&f.getAttribute("property")===(a.property==null?null:a.property)&&f.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&f.getAttribute("charset")===(a.charSet==null?null:a.charSet)){g.splice(b,1);break e}}f=s.createElement(o),Ke(f,o,a),s.head.appendChild(f);break;default:throw Error(l(468,o))}f[Fe]=t,qe(f),o=f}t.stateNode=o}else Ig(s,t.type,t.stateNode);else t.stateNode=Yg(s,o,t.memoizedProps);else f!==o?(f===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):f.count--,o===null?Ig(s,t.type,t.stateNode):Yg(s,o,t.memoizedProps)):o===null&&t.stateNode!==null&&af(t,t.memoizedProps,a.memoizedProps)}break;case 27:vn(e,t),bn(t),o&512&&(Ne||a===null||Wn(a,a.return)),a!==null&&o&4&&af(t,t.memoizedProps,a.memoizedProps);break;case 5:if(vn(e,t),bn(t),o&512&&(Ne||a===null||Wn(a,a.return)),t.flags&32){s=t.stateNode;try{qr(s,"")}catch(I){pe(t,t.return,I)}}o&4&&t.stateNode!=null&&(s=t.memoizedProps,af(t,s,a!==null?a.memoizedProps:s)),o&1024&&(lf=!0);break;case 6:if(vn(e,t),bn(t),o&4){if(t.stateNode===null)throw Error(l(162));o=t.memoizedProps,a=t.stateNode;try{a.nodeValue=o}catch(I){pe(t,t.return,I)}}break;case 3:if(Bs=null,s=Yn,Yn=Ds(e.containerInfo),vn(e,t),Yn=s,bn(t),o&4&&a!==null&&a.memoizedState.isDehydrated)try{qi(e.containerInfo)}catch(I){pe(t,t.return,I)}lf&&(lf=!1,Jh(t));break;case 4:o=Yn,Yn=Ds(t.stateNode.containerInfo),vn(e,t),bn(t),Yn=o;break;case 12:vn(e,t),bn(t);break;case 13:vn(e,t),bn(t),t.child.flags&8192&&t.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(mf=Ut()),o&4&&(o=t.updateQueue,o!==null&&(t.updateQueue=null,sf(t,o)));break;case 22:s=t.memoizedState!==null;var B=a!==null&&a.memoizedState!==null,G=xa,tt=Ne;if(xa=G||s,Ne=tt||B,vn(e,t),Ne=tt,xa=G,bn(t),o&8192)t:for(e=t.stateNode,e._visibility=s?e._visibility&-2:e._visibility|1,s&&(a===null||B||xa||Ne||Cr(t)),a=null,e=t;;){if(e.tag===5||e.tag===26){if(a===null){B=a=e;try{if(f=B.stateNode,s)g=f.style,typeof g.setProperty=="function"?g.setProperty("display","none","important"):g.display="none";else{b=B.stateNode;var rt=B.memoizedProps.style,Y=rt!=null&&rt.hasOwnProperty("display")?rt.display:null;b.style.display=Y==null||typeof Y=="boolean"?"":(""+Y).trim()}}catch(I){pe(B,B.return,I)}}}else if(e.tag===6){if(a===null){B=e;try{B.stateNode.nodeValue=s?"":B.memoizedProps}catch(I){pe(B,B.return,I)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;a===e&&(a=null),e=e.return}a===e&&(a=null),e.sibling.return=e.return,e=e.sibling}o&4&&(o=t.updateQueue,o!==null&&(a=o.retryQueue,a!==null&&(o.retryQueue=null,sf(t,a))));break;case 19:vn(e,t),bn(t),o&4&&(o=t.updateQueue,o!==null&&(t.updateQueue=null,sf(t,o)));break;case 30:break;case 21:break;default:vn(e,t),bn(t)}}function bn(t){var e=t.flags;if(e&2){try{for(var a,o=t.return;o!==null;){if(Yh(o)){a=o;break}o=o.return}if(a==null)throw Error(l(160));switch(a.tag){case 27:var s=a.stateNode,f=rf(t);vs(t,f,s);break;case 5:var g=a.stateNode;a.flags&32&&(qr(g,""),a.flags&=-33);var b=rf(t);vs(t,b,g);break;case 3:case 4:var B=a.stateNode.containerInfo,G=rf(t);of(t,G,B);break;default:throw Error(l(161))}}catch(tt){pe(t,t.return,tt)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Jh(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Jh(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function Ga(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Kh(t,e.alternate,e),e=e.sibling}function Cr(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:qa(4,e,e.return),Cr(e);break;case 1:Wn(e,e.return);var a=e.stateNode;typeof a.componentWillUnmount=="function"&&Gh(e,e.return,a),Cr(e);break;case 27:ki(e.stateNode);case 26:case 5:Wn(e,e.return),Cr(e);break;case 22:e.memoizedState===null&&Cr(e);break;case 30:Cr(e);break;default:Cr(e)}t=t.sibling}}function Va(t,e,a){for(a=a&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var o=e.alternate,s=t,f=e,g=f.flags;switch(f.tag){case 0:case 11:case 15:Va(s,f,a),Ei(4,f);break;case 1:if(Va(s,f,a),o=f,s=o.stateNode,typeof s.componentDidMount=="function")try{s.componentDidMount()}catch(G){pe(o,o.return,G)}if(o=f,s=o.updateQueue,s!==null){var b=o.stateNode;try{var B=s.shared.hiddenCallbacks;if(B!==null)for(s.shared.hiddenCallbacks=null,s=0;s<B.length;s++)Am(B[s],b)}catch(G){pe(o,o.return,G)}}a&&g&64&&qh(f),Ri(f,f.return);break;case 27:Xh(f);case 26:case 5:Va(s,f,a),a&&o===null&&g&4&&Vh(f),Ri(f,f.return);break;case 12:Va(s,f,a);break;case 13:Va(s,f,a),a&&g&4&&Zh(s,f);break;case 22:f.memoizedState===null&&Va(s,f,a),Ri(f,f.return);break;case 30:break;default:Va(s,f,a)}e=e.sibling}}function uf(t,e){var a=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==a&&(t!=null&&t.refCount++,a!=null&&ci(a))}function cf(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&ci(t))}function Zn(t,e,a,o){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)tg(t,e,a,o),e=e.sibling}function tg(t,e,a,o){var s=e.flags;switch(e.tag){case 0:case 11:case 15:Zn(t,e,a,o),s&2048&&Ei(9,e);break;case 1:Zn(t,e,a,o);break;case 3:Zn(t,e,a,o),s&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&ci(t)));break;case 12:if(s&2048){Zn(t,e,a,o),t=e.stateNode;try{var f=e.memoizedProps,g=f.id,b=f.onPostCommit;typeof b=="function"&&b(g,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(B){pe(e,e.return,B)}}else Zn(t,e,a,o);break;case 13:Zn(t,e,a,o);break;case 23:break;case 22:f=e.stateNode,g=e.alternate,e.memoizedState!==null?f._visibility&2?Zn(t,e,a,o):wi(t,e):f._visibility&2?Zn(t,e,a,o):(f._visibility|=2,lo(t,e,a,o,(e.subtreeFlags&10256)!==0)),s&2048&&uf(g,e);break;case 24:Zn(t,e,a,o),s&2048&&cf(e.alternate,e);break;default:Zn(t,e,a,o)}}function lo(t,e,a,o,s){for(s=s&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var f=t,g=e,b=a,B=o,G=g.flags;switch(g.tag){case 0:case 11:case 15:lo(f,g,b,B,s),Ei(8,g);break;case 23:break;case 22:var tt=g.stateNode;g.memoizedState!==null?tt._visibility&2?lo(f,g,b,B,s):wi(f,g):(tt._visibility|=2,lo(f,g,b,B,s)),s&&G&2048&&uf(g.alternate,g);break;case 24:lo(f,g,b,B,s),s&&G&2048&&cf(g.alternate,g);break;default:lo(f,g,b,B,s)}e=e.sibling}}function wi(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var a=t,o=e,s=o.flags;switch(o.tag){case 22:wi(a,o),s&2048&&uf(o.alternate,o);break;case 24:wi(a,o),s&2048&&cf(o.alternate,o);break;default:wi(a,o)}e=e.sibling}}var Oi=8192;function so(t){if(t.subtreeFlags&Oi)for(t=t.child;t!==null;)eg(t),t=t.sibling}function eg(t){switch(t.tag){case 26:so(t),t.flags&Oi&&t.memoizedState!==null&&iS(Yn,t.memoizedState,t.memoizedProps);break;case 5:so(t);break;case 3:case 4:var e=Yn;Yn=Ds(t.stateNode.containerInfo),so(t),Yn=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Oi,Oi=16777216,so(t),Oi=e):so(t));break;default:so(t)}}function ng(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Ai(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var a=0;a<e.length;a++){var o=e[a];Ve=o,rg(o,t)}ng(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)ag(t),t=t.sibling}function ag(t){switch(t.tag){case 0:case 11:case 15:Ai(t),t.flags&2048&&qa(9,t,t.return);break;case 3:Ai(t);break;case 12:Ai(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,bs(t)):Ai(t);break;default:Ai(t)}}function bs(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var a=0;a<e.length;a++){var o=e[a];Ve=o,rg(o,t)}ng(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:qa(8,e,e.return),bs(e);break;case 22:a=e.stateNode,a._visibility&2&&(a._visibility&=-3,bs(e));break;default:bs(e)}t=t.sibling}}function rg(t,e){for(;Ve!==null;){var a=Ve;switch(a.tag){case 0:case 11:case 15:qa(8,a,e);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var o=a.memoizedState.cachePool.pool;o!=null&&o.refCount++}break;case 24:ci(a.memoizedState.cache)}if(o=a.child,o!==null)o.return=a,Ve=o;else t:for(a=t;Ve!==null;){o=Ve;var s=o.sibling,f=o.return;if(Qh(o),o===a){Ve=null;break t}if(s!==null){s.return=f,Ve=s;break t}Ve=f}}}var C1={getCacheForType:function(t){var e=Je(Le),a=e.data.get(t);return a===void 0&&(a=t(),e.data.set(t,a)),a}},T1=typeof WeakMap=="function"?WeakMap:Map,re=0,ge=null,Lt=null,Xt=0,oe=0,Sn=null,Ya=!1,uo=!1,ff=!1,Ta=0,Oe=0,Xa=0,Tr=0,df=0,$n=0,co=0,Mi=null,fn=null,pf=!1,mf=0,Ss=1/0,xs=null,Ia=null,Ie=0,Ka=null,fo=null,po=0,hf=0,gf=null,og=null,zi=0,yf=null;function xn(){if((re&2)!==0&&Xt!==0)return Xt&-Xt;if(N.T!==null){var t=Jr;return t!==0?t:Ef()}return on()}function ig(){$n===0&&($n=(Xt&536870912)===0||Zt?ir():536870912);var t=kn.current;return t!==null&&(t.flags|=32),$n}function Cn(t,e,a){(t===ge&&(oe===2||oe===9)||t.cancelPendingCommit!==null)&&(mo(t,0),Qa(t,Xt,$n,!1)),lr(t,a),((re&2)===0||t!==ge)&&(t===ge&&((re&2)===0&&(Tr|=a),Oe===4&&Qa(t,Xt,$n,!1)),Fn(t))}function lg(t,e,a){if((re&6)!==0)throw Error(l(327));var o=!a&&(e&124)===0&&(e&t.expiredLanes)===0||be(t,e),s=o?w1(t,e):Sf(t,e,!0),f=o;do{if(s===0){uo&&!o&&Qa(t,e,0,!1);break}else{if(a=t.current.alternate,f&&!E1(a)){s=Sf(t,e,!1),f=!1;continue}if(s===2){if(f=e,t.errorRecoveryDisabledLanes&f)var g=0;else g=t.pendingLanes&-536870913,g=g!==0?g:g&536870912?536870912:0;if(g!==0){e=g;t:{var b=t;s=Mi;var B=b.current.memoizedState.isDehydrated;if(B&&(mo(b,g).flags|=256),g=Sf(b,g,!1),g!==2){if(ff&&!B){b.errorRecoveryDisabledLanes|=f,Tr|=f,s=4;break t}f=fn,fn=s,f!==null&&(fn===null?fn=f:fn.push.apply(fn,f))}s=g}if(f=!1,s!==2)continue}}if(s===1){mo(t,0),Qa(t,e,0,!0);break}t:{switch(o=t,f=s,f){case 0:case 1:throw Error(l(345));case 4:if((e&4194048)!==e)break;case 6:Qa(o,e,$n,!Ya);break t;case 2:fn=null;break;case 3:case 5:break;default:throw Error(l(329))}if((e&62914560)===e&&(s=mf+300-Ut(),10<s)){if(Qa(o,e,$n,!Ya),Yt(o,0,!0)!==0)break t;o.timeoutHandle=jg(sg.bind(null,o,a,fn,xs,pf,e,$n,Tr,co,Ya,f,2,-0,0),s);break t}sg(o,a,fn,xs,pf,e,$n,Tr,co,Ya,f,0,-0,0)}}break}while(!0);Fn(t)}function sg(t,e,a,o,s,f,g,b,B,G,tt,rt,Y,I){if(t.timeoutHandle=-1,rt=e.subtreeFlags,(rt&8192||(rt&16785408)===16785408)&&(_i={stylesheets:null,count:0,unsuspend:oS},eg(e),rt=lS(),rt!==null)){t.cancelPendingCommit=rt(hg.bind(null,t,e,f,a,o,s,g,b,B,tt,1,Y,I)),Qa(t,f,g,!G);return}hg(t,e,f,a,o,s,g,b,B)}function E1(t){for(var e=t;;){var a=e.tag;if((a===0||a===11||a===15)&&e.flags&16384&&(a=e.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var o=0;o<a.length;o++){var s=a[o],f=s.getSnapshot;s=s.value;try{if(!gn(f(),s))return!1}catch{return!1}}if(a=e.child,e.subtreeFlags&16384&&a!==null)a.return=e,e=a;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Qa(t,e,a,o){e&=~df,e&=~Tr,t.suspendedLanes|=e,t.pingedLanes&=~e,o&&(t.warmLanes|=e),o=t.expirationTimes;for(var s=e;0<s;){var f=31-ne(s),g=1<<f;o[f]=-1,s&=~g}a!==0&&zl(t,a,e)}function Cs(){return(re&6)===0?(Di(0),!1):!0}function vf(){if(Lt!==null){if(oe===0)var t=Lt.return;else t=Lt,ha=yr=null,kc(t),oo=null,xi=0,t=Lt;for(;t!==null;)Ph(t.alternate,t),t=t.return;Lt=null}}function mo(t,e){var a=t.timeoutHandle;a!==-1&&(t.timeoutHandle=-1,q1(a)),a=t.cancelPendingCommit,a!==null&&(t.cancelPendingCommit=null,a()),vf(),ge=t,Lt=a=da(t.current,null),Xt=e,oe=0,Sn=null,Ya=!1,uo=be(t,e),ff=!1,co=$n=df=Tr=Xa=Oe=0,fn=Mi=null,pf=!1,(e&8)!==0&&(e|=e&32);var o=t.entangledLanes;if(o!==0)for(t=t.entanglements,o&=e;0<o;){var s=31-ne(o),f=1<<s;e|=t[s],o&=~f}return Ta=e,Vl(),a}function ug(t,e){kt=null,N.H=us,e===di||e===Jl?(e=wm(),oe=3):e===Tm?(e=wm(),oe=4):oe=e===Oh?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,Sn=e,Lt===null&&(Oe=1,ms(t,zn(e,t.current)))}function cg(){var t=N.H;return N.H=us,t===null?us:t}function fg(){var t=N.A;return N.A=C1,t}function bf(){Oe=4,Ya||(Xt&4194048)!==Xt&&kn.current!==null||(uo=!0),(Xa&134217727)===0&&(Tr&134217727)===0||ge===null||Qa(ge,Xt,$n,!1)}function Sf(t,e,a){var o=re;re|=2;var s=cg(),f=fg();(ge!==t||Xt!==e)&&(xs=null,mo(t,e)),e=!1;var g=Oe;t:do try{if(oe!==0&&Lt!==null){var b=Lt,B=Sn;switch(oe){case 8:vf(),g=6;break t;case 3:case 2:case 9:case 6:kn.current===null&&(e=!0);var G=oe;if(oe=0,Sn=null,ho(t,b,B,G),a&&uo){g=0;break t}break;default:G=oe,oe=0,Sn=null,ho(t,b,B,G)}}R1(),g=Oe;break}catch(tt){ug(t,tt)}while(!0);return e&&t.shellSuspendCounter++,ha=yr=null,re=o,N.H=s,N.A=f,Lt===null&&(ge=null,Xt=0,Vl()),g}function R1(){for(;Lt!==null;)dg(Lt)}function w1(t,e){var a=re;re|=2;var o=cg(),s=fg();ge!==t||Xt!==e?(xs=null,Ss=Ut()+500,mo(t,e)):uo=be(t,e);t:do try{if(oe!==0&&Lt!==null){e=Lt;var f=Sn;e:switch(oe){case 1:oe=0,Sn=null,ho(t,e,f,1);break;case 2:case 9:if(Em(f)){oe=0,Sn=null,pg(e);break}e=function(){oe!==2&&oe!==9||ge!==t||(oe=7),Fn(t)},f.then(e,e);break t;case 3:oe=7;break t;case 4:oe=5;break t;case 7:Em(f)?(oe=0,Sn=null,pg(e)):(oe=0,Sn=null,ho(t,e,f,7));break;case 5:var g=null;switch(Lt.tag){case 26:g=Lt.memoizedState;case 5:case 27:var b=Lt;if(!g||Kg(g)){oe=0,Sn=null;var B=b.sibling;if(B!==null)Lt=B;else{var G=b.return;G!==null?(Lt=G,Ts(G)):Lt=null}break e}}oe=0,Sn=null,ho(t,e,f,5);break;case 6:oe=0,Sn=null,ho(t,e,f,6);break;case 8:vf(),Oe=6;break t;default:throw Error(l(462))}}O1();break}catch(tt){ug(t,tt)}while(!0);return ha=yr=null,N.H=o,N.A=s,re=a,Lt!==null?0:(ge=null,Xt=0,Vl(),Oe)}function O1(){for(;Lt!==null&&!qt();)dg(Lt)}function dg(t){var e=Lh(t.alternate,t,Ta);t.memoizedProps=t.pendingProps,e===null?Ts(t):Lt=e}function pg(t){var e=t,a=e.alternate;switch(e.tag){case 15:case 0:e=Bh(a,e,e.pendingProps,e.type,void 0,Xt);break;case 11:e=Bh(a,e,e.pendingProps,e.type.render,e.ref,Xt);break;case 5:kc(e);default:Ph(a,e),e=Lt=mm(e,Ta),e=Lh(a,e,Ta)}t.memoizedProps=t.pendingProps,e===null?Ts(t):Lt=e}function ho(t,e,a,o){ha=yr=null,kc(e),oo=null,xi=0;var s=e.return;try{if(g1(t,s,e,a,Xt)){Oe=1,ms(t,zn(a,t.current)),Lt=null;return}}catch(f){if(s!==null)throw Lt=s,f;Oe=1,ms(t,zn(a,t.current)),Lt=null;return}e.flags&32768?(Zt||o===1?t=!0:uo||(Xt&536870912)!==0?t=!1:(Ya=t=!0,(o===2||o===9||o===3||o===6)&&(o=kn.current,o!==null&&o.tag===13&&(o.flags|=16384))),mg(e,t)):Ts(e)}function Ts(t){var e=t;do{if((e.flags&32768)!==0){mg(e,Ya);return}t=e.return;var a=v1(e.alternate,e,Ta);if(a!==null){Lt=a;return}if(e=e.sibling,e!==null){Lt=e;return}Lt=e=t}while(e!==null);Oe===0&&(Oe=5)}function mg(t,e){do{var a=b1(t.alternate,t);if(a!==null){a.flags&=32767,Lt=a;return}if(a=t.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!e&&(t=t.sibling,t!==null)){Lt=t;return}Lt=t=a}while(t!==null);Oe=6,Lt=null}function hg(t,e,a,o,s,f,g,b,B){t.cancelPendingCommit=null;do Es();while(Ie!==0);if((re&6)!==0)throw Error(l(327));if(e!==null){if(e===t.current)throw Error(l(177));if(f=e.lanes|e.childLanes,f|=uc,_u(t,a,f,g,b,B),t===ge&&(Lt=ge=null,Xt=0),fo=e,Ka=t,po=a,hf=f,gf=s,og=o,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,D1(jt,function(){return Sg(),null})):(t.callbackNode=null,t.callbackPriority=0),o=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||o){o=N.T,N.T=null,s=V.p,V.p=2,g=re,re|=4;try{S1(t,e,a)}finally{re=g,V.p=s,N.T=o}}Ie=1,gg(),yg(),vg()}}function gg(){if(Ie===1){Ie=0;var t=Ka,e=fo,a=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||a){a=N.T,N.T=null;var o=V.p;V.p=2;var s=re;re|=4;try{Fh(e,t);var f=Nf,g=rm(t.containerInfo),b=f.focusedElem,B=f.selectionRange;if(g!==b&&b&&b.ownerDocument&&am(b.ownerDocument.documentElement,b)){if(B!==null&&rc(b)){var G=B.start,tt=B.end;if(tt===void 0&&(tt=G),"selectionStart"in b)b.selectionStart=G,b.selectionEnd=Math.min(tt,b.value.length);else{var rt=b.ownerDocument||document,Y=rt&&rt.defaultView||window;if(Y.getSelection){var I=Y.getSelection(),Rt=b.textContent.length,xt=Math.min(B.start,Rt),ce=B.end===void 0?xt:Math.min(B.end,Rt);!I.extend&&xt>ce&&(g=ce,ce=xt,xt=g);var L=nm(b,xt),$=nm(b,ce);if(L&&$&&(I.rangeCount!==1||I.anchorNode!==L.node||I.anchorOffset!==L.offset||I.focusNode!==$.node||I.focusOffset!==$.offset)){var P=rt.createRange();P.setStart(L.node,L.offset),I.removeAllRanges(),xt>ce?(I.addRange(P),I.extend($.node,$.offset)):(P.setEnd($.node,$.offset),I.addRange(P))}}}}for(rt=[],I=b;I=I.parentNode;)I.nodeType===1&&rt.push({element:I,left:I.scrollLeft,top:I.scrollTop});for(typeof b.focus=="function"&&b.focus(),b=0;b<rt.length;b++){var at=rt[b];at.element.scrollLeft=at.left,at.element.scrollTop=at.top}}js=!!Df,Nf=Df=null}finally{re=s,V.p=o,N.T=a}}t.current=e,Ie=2}}function yg(){if(Ie===2){Ie=0;var t=Ka,e=fo,a=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||a){a=N.T,N.T=null;var o=V.p;V.p=2;var s=re;re|=4;try{Kh(t,e.alternate,e)}finally{re=s,V.p=o,N.T=a}}Ie=3}}function vg(){if(Ie===4||Ie===3){Ie=0,Ce();var t=Ka,e=fo,a=po,o=og;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Ie=5:(Ie=0,fo=Ka=null,bg(t,t.pendingLanes));var s=t.pendingLanes;if(s===0&&(Ia=null),At(a),e=e.stateNode,ee&&typeof ee.onCommitFiberRoot=="function")try{ee.onCommitFiberRoot(Ue,e,void 0,(e.current.flags&128)===128)}catch{}if(o!==null){e=N.T,s=V.p,V.p=2,N.T=null;try{for(var f=t.onRecoverableError,g=0;g<o.length;g++){var b=o[g];f(b.value,{componentStack:b.stack})}}finally{N.T=e,V.p=s}}(po&3)!==0&&Es(),Fn(t),s=t.pendingLanes,(a&4194090)!==0&&(s&42)!==0?t===yf?zi++:(zi=0,yf=t):zi=0,Di(0)}}function bg(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,ci(e)))}function Es(t){return gg(),yg(),vg(),Sg()}function Sg(){if(Ie!==5)return!1;var t=Ka,e=hf;hf=0;var a=At(po),o=N.T,s=V.p;try{V.p=32>a?32:a,N.T=null,a=gf,gf=null;var f=Ka,g=po;if(Ie=0,fo=Ka=null,po=0,(re&6)!==0)throw Error(l(331));var b=re;if(re|=4,ag(f.current),tg(f,f.current,g,a),re=b,Di(0,!1),ee&&typeof ee.onPostCommitFiberRoot=="function")try{ee.onPostCommitFiberRoot(Ue,f)}catch{}return!0}finally{V.p=s,N.T=o,bg(t,e)}}function xg(t,e,a){e=zn(a,e),e=Kc(t.stateNode,e,2),t=Ua(t,e,2),t!==null&&(lr(t,2),Fn(t))}function pe(t,e,a){if(t.tag===3)xg(t,t,a);else for(;e!==null;){if(e.tag===3){xg(e,t,a);break}else if(e.tag===1){var o=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof o.componentDidCatch=="function"&&(Ia===null||!Ia.has(o))){t=zn(a,t),a=Rh(2),o=Ua(e,a,2),o!==null&&(wh(a,o,e,t),lr(o,2),Fn(o));break}}e=e.return}}function xf(t,e,a){var o=t.pingCache;if(o===null){o=t.pingCache=new T1;var s=new Set;o.set(e,s)}else s=o.get(e),s===void 0&&(s=new Set,o.set(e,s));s.has(a)||(ff=!0,s.add(a),t=A1.bind(null,t,e,a),e.then(t,t))}function A1(t,e,a){var o=t.pingCache;o!==null&&o.delete(e),t.pingedLanes|=t.suspendedLanes&a,t.warmLanes&=~a,ge===t&&(Xt&a)===a&&(Oe===4||Oe===3&&(Xt&62914560)===Xt&&300>Ut()-mf?(re&2)===0&&mo(t,0):df|=a,co===Xt&&(co=0)),Fn(t)}function Cg(t,e){e===0&&(e=Ml()),t=Qr(t,e),t!==null&&(lr(t,e),Fn(t))}function M1(t){var e=t.memoizedState,a=0;e!==null&&(a=e.retryLane),Cg(t,a)}function z1(t,e){var a=0;switch(t.tag){case 13:var o=t.stateNode,s=t.memoizedState;s!==null&&(a=s.retryLane);break;case 19:o=t.stateNode;break;case 22:o=t.stateNode._retryCache;break;default:throw Error(l(314))}o!==null&&o.delete(e),Cg(t,a)}function D1(t,e){return $t(t,e)}var Rs=null,go=null,Cf=!1,ws=!1,Tf=!1,Er=0;function Fn(t){t!==go&&t.next===null&&(go===null?Rs=go=t:go=go.next=t),ws=!0,Cf||(Cf=!0,B1())}function Di(t,e){if(!Tf&&ws){Tf=!0;do for(var a=!1,o=Rs;o!==null;){if(t!==0){var s=o.pendingLanes;if(s===0)var f=0;else{var g=o.suspendedLanes,b=o.pingedLanes;f=(1<<31-ne(42|t)+1)-1,f&=s&~(g&~b),f=f&201326741?f&201326741|1:f?f|2:0}f!==0&&(a=!0,wg(o,f))}else f=Xt,f=Yt(o,o===ge?f:0,o.cancelPendingCommit!==null||o.timeoutHandle!==-1),(f&3)===0||be(o,f)||(a=!0,wg(o,f));o=o.next}while(a);Tf=!1}}function N1(){Tg()}function Tg(){ws=Cf=!1;var t=0;Er!==0&&(P1()&&(t=Er),Er=0);for(var e=Ut(),a=null,o=Rs;o!==null;){var s=o.next,f=Eg(o,e);f===0?(o.next=null,a===null?Rs=s:a.next=s,s===null&&(go=a)):(a=o,(t!==0||(f&3)!==0)&&(ws=!0)),o=s}Di(t)}function Eg(t,e){for(var a=t.suspendedLanes,o=t.pingedLanes,s=t.expirationTimes,f=t.pendingLanes&-62914561;0<f;){var g=31-ne(f),b=1<<g,B=s[g];B===-1?((b&a)===0||(b&o)!==0)&&(s[g]=hn(b,e)):B<=e&&(t.expiredLanes|=b),f&=~b}if(e=ge,a=Xt,a=Yt(t,t===e?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),o=t.callbackNode,a===0||t===e&&(oe===2||oe===9)||t.cancelPendingCommit!==null)return o!==null&&o!==null&&Et(o),t.callbackNode=null,t.callbackPriority=0;if((a&3)===0||be(t,a)){if(e=a&-a,e===t.callbackPriority)return e;switch(o!==null&&Et(o),At(a)){case 2:case 8:a=Vt;break;case 32:a=jt;break;case 268435456:a=_e;break;default:a=jt}return o=Rg.bind(null,t),a=$t(a,o),t.callbackPriority=e,t.callbackNode=a,e}return o!==null&&o!==null&&Et(o),t.callbackPriority=2,t.callbackNode=null,2}function Rg(t,e){if(Ie!==0&&Ie!==5)return t.callbackNode=null,t.callbackPriority=0,null;var a=t.callbackNode;if(Es()&&t.callbackNode!==a)return null;var o=Xt;return o=Yt(t,t===ge?o:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),o===0?null:(lg(t,o,e),Eg(t,Ut()),t.callbackNode!=null&&t.callbackNode===a?Rg.bind(null,t):null)}function wg(t,e){if(Es())return null;lg(t,e,!0)}function B1(){G1(function(){(re&6)!==0?$t(Qt,N1):Tg()})}function Ef(){return Er===0&&(Er=ir()),Er}function Og(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:_l(""+t)}function Ag(t,e){var a=e.ownerDocument.createElement("input");return a.name=e.name,a.value=e.value,t.id&&a.setAttribute("form",t.id),e.parentNode.insertBefore(a,e),t=new FormData(t),a.parentNode.removeChild(a),t}function k1(t,e,a,o,s){if(e==="submit"&&a&&a.stateNode===s){var f=Og((s[ln]||null).action),g=o.submitter;g&&(e=(e=g[ln]||null)?Og(e.formAction):g.getAttribute("formAction"),e!==null&&(f=e,g=null));var b=new Pl("action","action",null,o,s);t.push({event:b,listeners:[{instance:null,listener:function(){if(o.defaultPrevented){if(Er!==0){var B=g?Ag(s,g):new FormData(s);Gc(a,{pending:!0,data:B,method:s.method,action:f},null,B)}}else typeof f=="function"&&(b.preventDefault(),B=g?Ag(s,g):new FormData(s),Gc(a,{pending:!0,data:B,method:s.method,action:f},f,B))},currentTarget:s}]})}}for(var Rf=0;Rf<sc.length;Rf++){var wf=sc[Rf],$1=wf.toLowerCase(),j1=wf[0].toUpperCase()+wf.slice(1);Vn($1,"on"+j1)}Vn(lm,"onAnimationEnd"),Vn(sm,"onAnimationIteration"),Vn(um,"onAnimationStart"),Vn("dblclick","onDoubleClick"),Vn("focusin","onFocus"),Vn("focusout","onBlur"),Vn(Jb,"onTransitionRun"),Vn(t1,"onTransitionStart"),Vn(e1,"onTransitionCancel"),Vn(cm,"onTransitionEnd"),Lr("onMouseEnter",["mouseout","mouseover"]),Lr("onMouseLeave",["mouseout","mouseover"]),Lr("onPointerEnter",["pointerout","pointerover"]),Lr("onPointerLeave",["pointerout","pointerover"]),sr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),sr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),sr("onBeforeInput",["compositionend","keypress","textInput","paste"]),sr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),sr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),sr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ni="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),_1=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ni));function Mg(t,e){e=(e&4)!==0;for(var a=0;a<t.length;a++){var o=t[a],s=o.event;o=o.listeners;t:{var f=void 0;if(e)for(var g=o.length-1;0<=g;g--){var b=o[g],B=b.instance,G=b.currentTarget;if(b=b.listener,B!==f&&s.isPropagationStopped())break t;f=b,s.currentTarget=G;try{f(s)}catch(tt){ps(tt)}s.currentTarget=null,f=B}else for(g=0;g<o.length;g++){if(b=o[g],B=b.instance,G=b.currentTarget,b=b.listener,B!==f&&s.isPropagationStopped())break t;f=b,s.currentTarget=G;try{f(s)}catch(tt){ps(tt)}s.currentTarget=null,f=B}}}}function Ht(t,e){var a=e[Uu];a===void 0&&(a=e[Uu]=new Set);var o=t+"__bubble";a.has(o)||(zg(e,t,2,!1),a.add(o))}function Of(t,e,a){var o=0;e&&(o|=4),zg(a,t,o,e)}var Os="_reactListening"+Math.random().toString(36).slice(2);function Af(t){if(!t[Os]){t[Os]=!0,Tp.forEach(function(a){a!=="selectionchange"&&(_1.has(a)||Of(a,!1,t),Of(a,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Os]||(e[Os]=!0,Of("selectionchange",!1,e))}}function zg(t,e,a,o){switch(ty(e)){case 2:var s=cS;break;case 8:s=fS;break;default:s=qf}a=s.bind(null,e,a,t),s=void 0,!Qu||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(s=!0),o?s!==void 0?t.addEventListener(e,a,{capture:!0,passive:s}):t.addEventListener(e,a,!0):s!==void 0?t.addEventListener(e,a,{passive:s}):t.addEventListener(e,a,!1)}function Mf(t,e,a,o,s){var f=o;if((e&1)===0&&(e&2)===0&&o!==null)t:for(;;){if(o===null)return;var g=o.tag;if(g===3||g===4){var b=o.stateNode.containerInfo;if(b===s)break;if(g===4)for(g=o.return;g!==null;){var B=g.tag;if((B===3||B===4)&&g.stateNode.containerInfo===s)return;g=g.return}for(;b!==null;){if(g=jr(b),g===null)return;if(B=g.tag,B===5||B===6||B===26||B===27){o=f=g;continue t}b=b.parentNode}}o=o.return}_p(function(){var G=f,tt=Iu(a),rt=[];t:{var Y=fm.get(t);if(Y!==void 0){var I=Pl,Rt=t;switch(t){case"keypress":if(Ll(a)===0)break t;case"keydown":case"keyup":I=Db;break;case"focusin":Rt="focus",I=Ju;break;case"focusout":Rt="blur",I=Ju;break;case"beforeblur":case"afterblur":I=Ju;break;case"click":if(a.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":I=Hp;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":I=bb;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":I=kb;break;case lm:case sm:case um:I=Cb;break;case cm:I=jb;break;case"scroll":case"scrollend":I=yb;break;case"wheel":I=Ub;break;case"copy":case"cut":case"paste":I=Eb;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":I=qp;break;case"toggle":case"beforetoggle":I=Hb}var xt=(e&4)!==0,ce=!xt&&(t==="scroll"||t==="scrollend"),L=xt?Y!==null?Y+"Capture":null:Y;xt=[];for(var $=G,P;$!==null;){var at=$;if(P=at.stateNode,at=at.tag,at!==5&&at!==26&&at!==27||P===null||L===null||(at=Zo($,L),at!=null&&xt.push(Bi($,at,P))),ce)break;$=$.return}0<xt.length&&(Y=new I(Y,Rt,null,a,tt),rt.push({event:Y,listeners:xt}))}}if((e&7)===0){t:{if(Y=t==="mouseover"||t==="pointerover",I=t==="mouseout"||t==="pointerout",Y&&a!==Xu&&(Rt=a.relatedTarget||a.fromElement)&&(jr(Rt)||Rt[$r]))break t;if((I||Y)&&(Y=tt.window===tt?tt:(Y=tt.ownerDocument)?Y.defaultView||Y.parentWindow:window,I?(Rt=a.relatedTarget||a.toElement,I=G,Rt=Rt?jr(Rt):null,Rt!==null&&(ce=c(Rt),xt=Rt.tag,Rt!==ce||xt!==5&&xt!==27&&xt!==6)&&(Rt=null)):(I=null,Rt=G),I!==Rt)){if(xt=Hp,at="onMouseLeave",L="onMouseEnter",$="mouse",(t==="pointerout"||t==="pointerover")&&(xt=qp,at="onPointerLeave",L="onPointerEnter",$="pointer"),ce=I==null?Y:Wo(I),P=Rt==null?Y:Wo(Rt),Y=new xt(at,$+"leave",I,a,tt),Y.target=ce,Y.relatedTarget=P,at=null,jr(tt)===G&&(xt=new xt(L,$+"enter",Rt,a,tt),xt.target=P,xt.relatedTarget=ce,at=xt),ce=at,I&&Rt)e:{for(xt=I,L=Rt,$=0,P=xt;P;P=yo(P))$++;for(P=0,at=L;at;at=yo(at))P++;for(;0<$-P;)xt=yo(xt),$--;for(;0<P-$;)L=yo(L),P--;for(;$--;){if(xt===L||L!==null&&xt===L.alternate)break e;xt=yo(xt),L=yo(L)}xt=null}else xt=null;I!==null&&Dg(rt,Y,I,xt,!1),Rt!==null&&ce!==null&&Dg(rt,ce,Rt,xt,!0)}}t:{if(Y=G?Wo(G):window,I=Y.nodeName&&Y.nodeName.toLowerCase(),I==="select"||I==="input"&&Y.type==="file")var pt=Wp;else if(Kp(Y))if(Zp)pt=Wb;else{pt=Kb;var _t=Ib}else I=Y.nodeName,!I||I.toLowerCase()!=="input"||Y.type!=="checkbox"&&Y.type!=="radio"?G&&Yu(G.elementType)&&(pt=Wp):pt=Qb;if(pt&&(pt=pt(t,G))){Qp(rt,pt,a,tt);break t}_t&&_t(t,Y,G),t==="focusout"&&G&&Y.type==="number"&&G.memoizedProps.value!=null&&Vu(Y,"number",Y.value)}switch(_t=G?Wo(G):window,t){case"focusin":(Kp(_t)||_t.contentEditable==="true")&&(Xr=_t,oc=G,oi=null);break;case"focusout":oi=oc=Xr=null;break;case"mousedown":ic=!0;break;case"contextmenu":case"mouseup":case"dragend":ic=!1,om(rt,a,tt);break;case"selectionchange":if(Fb)break;case"keydown":case"keyup":om(rt,a,tt)}var vt;if(ec)t:{switch(t){case"compositionstart":var Ct="onCompositionStart";break t;case"compositionend":Ct="onCompositionEnd";break t;case"compositionupdate":Ct="onCompositionUpdate";break t}Ct=void 0}else Yr?Xp(t,a)&&(Ct="onCompositionEnd"):t==="keydown"&&a.keyCode===229&&(Ct="onCompositionStart");Ct&&(Gp&&a.locale!=="ko"&&(Yr||Ct!=="onCompositionStart"?Ct==="onCompositionEnd"&&Yr&&(vt=Up()):(ka=tt,Wu="value"in ka?ka.value:ka.textContent,Yr=!0)),_t=As(G,Ct),0<_t.length&&(Ct=new Pp(Ct,t,null,a,tt),rt.push({event:Ct,listeners:_t}),vt?Ct.data=vt:(vt=Ip(a),vt!==null&&(Ct.data=vt)))),(vt=qb?Gb(t,a):Vb(t,a))&&(Ct=As(G,"onBeforeInput"),0<Ct.length&&(_t=new Pp("onBeforeInput","beforeinput",null,a,tt),rt.push({event:_t,listeners:Ct}),_t.data=vt)),k1(rt,t,G,a,tt)}Mg(rt,e)})}function Bi(t,e,a){return{instance:t,listener:e,currentTarget:a}}function As(t,e){for(var a=e+"Capture",o=[];t!==null;){var s=t,f=s.stateNode;if(s=s.tag,s!==5&&s!==26&&s!==27||f===null||(s=Zo(t,a),s!=null&&o.unshift(Bi(t,s,f)),s=Zo(t,e),s!=null&&o.push(Bi(t,s,f))),t.tag===3)return o;t=t.return}return[]}function yo(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Dg(t,e,a,o,s){for(var f=e._reactName,g=[];a!==null&&a!==o;){var b=a,B=b.alternate,G=b.stateNode;if(b=b.tag,B!==null&&B===o)break;b!==5&&b!==26&&b!==27||G===null||(B=G,s?(G=Zo(a,f),G!=null&&g.unshift(Bi(a,G,B))):s||(G=Zo(a,f),G!=null&&g.push(Bi(a,G,B)))),a=a.return}g.length!==0&&t.push({event:e,listeners:g})}var U1=/\r\n?/g,L1=/\u0000|\uFFFD/g;function Ng(t){return(typeof t=="string"?t:""+t).replace(U1,`
`).replace(L1,"")}function Bg(t,e){return e=Ng(e),Ng(t)===e}function Ms(){}function ue(t,e,a,o,s,f){switch(a){case"children":typeof o=="string"?e==="body"||e==="textarea"&&o===""||qr(t,o):(typeof o=="number"||typeof o=="bigint")&&e!=="body"&&qr(t,""+o);break;case"className":kl(t,"class",o);break;case"tabIndex":kl(t,"tabindex",o);break;case"dir":case"role":case"viewBox":case"width":case"height":kl(t,a,o);break;case"style":$p(t,o,f);break;case"data":if(e!=="object"){kl(t,"data",o);break}case"src":case"href":if(o===""&&(e!=="a"||a!=="href")){t.removeAttribute(a);break}if(o==null||typeof o=="function"||typeof o=="symbol"||typeof o=="boolean"){t.removeAttribute(a);break}o=_l(""+o),t.setAttribute(a,o);break;case"action":case"formAction":if(typeof o=="function"){t.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof f=="function"&&(a==="formAction"?(e!=="input"&&ue(t,e,"name",s.name,s,null),ue(t,e,"formEncType",s.formEncType,s,null),ue(t,e,"formMethod",s.formMethod,s,null),ue(t,e,"formTarget",s.formTarget,s,null)):(ue(t,e,"encType",s.encType,s,null),ue(t,e,"method",s.method,s,null),ue(t,e,"target",s.target,s,null)));if(o==null||typeof o=="symbol"||typeof o=="boolean"){t.removeAttribute(a);break}o=_l(""+o),t.setAttribute(a,o);break;case"onClick":o!=null&&(t.onclick=Ms);break;case"onScroll":o!=null&&Ht("scroll",t);break;case"onScrollEnd":o!=null&&Ht("scrollend",t);break;case"dangerouslySetInnerHTML":if(o!=null){if(typeof o!="object"||!("__html"in o))throw Error(l(61));if(a=o.__html,a!=null){if(s.children!=null)throw Error(l(60));t.innerHTML=a}}break;case"multiple":t.multiple=o&&typeof o!="function"&&typeof o!="symbol";break;case"muted":t.muted=o&&typeof o!="function"&&typeof o!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(o==null||typeof o=="function"||typeof o=="boolean"||typeof o=="symbol"){t.removeAttribute("xlink:href");break}a=_l(""+o),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":o!=null&&typeof o!="function"&&typeof o!="symbol"?t.setAttribute(a,""+o):t.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":o&&typeof o!="function"&&typeof o!="symbol"?t.setAttribute(a,""):t.removeAttribute(a);break;case"capture":case"download":o===!0?t.setAttribute(a,""):o!==!1&&o!=null&&typeof o!="function"&&typeof o!="symbol"?t.setAttribute(a,o):t.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":o!=null&&typeof o!="function"&&typeof o!="symbol"&&!isNaN(o)&&1<=o?t.setAttribute(a,o):t.removeAttribute(a);break;case"rowSpan":case"start":o==null||typeof o=="function"||typeof o=="symbol"||isNaN(o)?t.removeAttribute(a):t.setAttribute(a,o);break;case"popover":Ht("beforetoggle",t),Ht("toggle",t),Bl(t,"popover",o);break;case"xlinkActuate":ca(t,"http://www.w3.org/1999/xlink","xlink:actuate",o);break;case"xlinkArcrole":ca(t,"http://www.w3.org/1999/xlink","xlink:arcrole",o);break;case"xlinkRole":ca(t,"http://www.w3.org/1999/xlink","xlink:role",o);break;case"xlinkShow":ca(t,"http://www.w3.org/1999/xlink","xlink:show",o);break;case"xlinkTitle":ca(t,"http://www.w3.org/1999/xlink","xlink:title",o);break;case"xlinkType":ca(t,"http://www.w3.org/1999/xlink","xlink:type",o);break;case"xmlBase":ca(t,"http://www.w3.org/XML/1998/namespace","xml:base",o);break;case"xmlLang":ca(t,"http://www.w3.org/XML/1998/namespace","xml:lang",o);break;case"xmlSpace":ca(t,"http://www.w3.org/XML/1998/namespace","xml:space",o);break;case"is":Bl(t,"is",o);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=hb.get(a)||a,Bl(t,a,o))}}function zf(t,e,a,o,s,f){switch(a){case"style":$p(t,o,f);break;case"dangerouslySetInnerHTML":if(o!=null){if(typeof o!="object"||!("__html"in o))throw Error(l(61));if(a=o.__html,a!=null){if(s.children!=null)throw Error(l(60));t.innerHTML=a}}break;case"children":typeof o=="string"?qr(t,o):(typeof o=="number"||typeof o=="bigint")&&qr(t,""+o);break;case"onScroll":o!=null&&Ht("scroll",t);break;case"onScrollEnd":o!=null&&Ht("scrollend",t);break;case"onClick":o!=null&&(t.onclick=Ms);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Ep.hasOwnProperty(a))t:{if(a[0]==="o"&&a[1]==="n"&&(s=a.endsWith("Capture"),e=a.slice(2,s?a.length-7:void 0),f=t[ln]||null,f=f!=null?f[a]:null,typeof f=="function"&&t.removeEventListener(e,f,s),typeof o=="function")){typeof f!="function"&&f!==null&&(a in t?t[a]=null:t.hasAttribute(a)&&t.removeAttribute(a)),t.addEventListener(e,o,s);break t}a in t?t[a]=o:o===!0?t.setAttribute(a,""):Bl(t,a,o)}}}function Ke(t,e,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Ht("error",t),Ht("load",t);var o=!1,s=!1,f;for(f in a)if(a.hasOwnProperty(f)){var g=a[f];if(g!=null)switch(f){case"src":o=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(l(137,e));default:ue(t,e,f,g,a,null)}}s&&ue(t,e,"srcSet",a.srcSet,a,null),o&&ue(t,e,"src",a.src,a,null);return;case"input":Ht("invalid",t);var b=f=g=s=null,B=null,G=null;for(o in a)if(a.hasOwnProperty(o)){var tt=a[o];if(tt!=null)switch(o){case"name":s=tt;break;case"type":g=tt;break;case"checked":B=tt;break;case"defaultChecked":G=tt;break;case"value":f=tt;break;case"defaultValue":b=tt;break;case"children":case"dangerouslySetInnerHTML":if(tt!=null)throw Error(l(137,e));break;default:ue(t,e,o,tt,a,null)}}Dp(t,f,b,B,G,g,s,!1),$l(t);return;case"select":Ht("invalid",t),o=g=f=null;for(s in a)if(a.hasOwnProperty(s)&&(b=a[s],b!=null))switch(s){case"value":f=b;break;case"defaultValue":g=b;break;case"multiple":o=b;default:ue(t,e,s,b,a,null)}e=f,a=g,t.multiple=!!o,e!=null?Pr(t,!!o,e,!1):a!=null&&Pr(t,!!o,a,!0);return;case"textarea":Ht("invalid",t),f=s=o=null;for(g in a)if(a.hasOwnProperty(g)&&(b=a[g],b!=null))switch(g){case"value":o=b;break;case"defaultValue":s=b;break;case"children":f=b;break;case"dangerouslySetInnerHTML":if(b!=null)throw Error(l(91));break;default:ue(t,e,g,b,a,null)}Bp(t,o,s,f),$l(t);return;case"option":for(B in a)if(a.hasOwnProperty(B)&&(o=a[B],o!=null))switch(B){case"selected":t.selected=o&&typeof o!="function"&&typeof o!="symbol";break;default:ue(t,e,B,o,a,null)}return;case"dialog":Ht("beforetoggle",t),Ht("toggle",t),Ht("cancel",t),Ht("close",t);break;case"iframe":case"object":Ht("load",t);break;case"video":case"audio":for(o=0;o<Ni.length;o++)Ht(Ni[o],t);break;case"image":Ht("error",t),Ht("load",t);break;case"details":Ht("toggle",t);break;case"embed":case"source":case"link":Ht("error",t),Ht("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(G in a)if(a.hasOwnProperty(G)&&(o=a[G],o!=null))switch(G){case"children":case"dangerouslySetInnerHTML":throw Error(l(137,e));default:ue(t,e,G,o,a,null)}return;default:if(Yu(e)){for(tt in a)a.hasOwnProperty(tt)&&(o=a[tt],o!==void 0&&zf(t,e,tt,o,a,void 0));return}}for(b in a)a.hasOwnProperty(b)&&(o=a[b],o!=null&&ue(t,e,b,o,a,null))}function H1(t,e,a,o){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var s=null,f=null,g=null,b=null,B=null,G=null,tt=null;for(I in a){var rt=a[I];if(a.hasOwnProperty(I)&&rt!=null)switch(I){case"checked":break;case"value":break;case"defaultValue":B=rt;default:o.hasOwnProperty(I)||ue(t,e,I,null,o,rt)}}for(var Y in o){var I=o[Y];if(rt=a[Y],o.hasOwnProperty(Y)&&(I!=null||rt!=null))switch(Y){case"type":f=I;break;case"name":s=I;break;case"checked":G=I;break;case"defaultChecked":tt=I;break;case"value":g=I;break;case"defaultValue":b=I;break;case"children":case"dangerouslySetInnerHTML":if(I!=null)throw Error(l(137,e));break;default:I!==rt&&ue(t,e,Y,I,o,rt)}}Gu(t,g,b,B,G,tt,f,s);return;case"select":I=g=b=Y=null;for(f in a)if(B=a[f],a.hasOwnProperty(f)&&B!=null)switch(f){case"value":break;case"multiple":I=B;default:o.hasOwnProperty(f)||ue(t,e,f,null,o,B)}for(s in o)if(f=o[s],B=a[s],o.hasOwnProperty(s)&&(f!=null||B!=null))switch(s){case"value":Y=f;break;case"defaultValue":b=f;break;case"multiple":g=f;default:f!==B&&ue(t,e,s,f,o,B)}e=b,a=g,o=I,Y!=null?Pr(t,!!a,Y,!1):!!o!=!!a&&(e!=null?Pr(t,!!a,e,!0):Pr(t,!!a,a?[]:"",!1));return;case"textarea":I=Y=null;for(b in a)if(s=a[b],a.hasOwnProperty(b)&&s!=null&&!o.hasOwnProperty(b))switch(b){case"value":break;case"children":break;default:ue(t,e,b,null,o,s)}for(g in o)if(s=o[g],f=a[g],o.hasOwnProperty(g)&&(s!=null||f!=null))switch(g){case"value":Y=s;break;case"defaultValue":I=s;break;case"children":break;case"dangerouslySetInnerHTML":if(s!=null)throw Error(l(91));break;default:s!==f&&ue(t,e,g,s,o,f)}Np(t,Y,I);return;case"option":for(var Rt in a)if(Y=a[Rt],a.hasOwnProperty(Rt)&&Y!=null&&!o.hasOwnProperty(Rt))switch(Rt){case"selected":t.selected=!1;break;default:ue(t,e,Rt,null,o,Y)}for(B in o)if(Y=o[B],I=a[B],o.hasOwnProperty(B)&&Y!==I&&(Y!=null||I!=null))switch(B){case"selected":t.selected=Y&&typeof Y!="function"&&typeof Y!="symbol";break;default:ue(t,e,B,Y,o,I)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var xt in a)Y=a[xt],a.hasOwnProperty(xt)&&Y!=null&&!o.hasOwnProperty(xt)&&ue(t,e,xt,null,o,Y);for(G in o)if(Y=o[G],I=a[G],o.hasOwnProperty(G)&&Y!==I&&(Y!=null||I!=null))switch(G){case"children":case"dangerouslySetInnerHTML":if(Y!=null)throw Error(l(137,e));break;default:ue(t,e,G,Y,o,I)}return;default:if(Yu(e)){for(var ce in a)Y=a[ce],a.hasOwnProperty(ce)&&Y!==void 0&&!o.hasOwnProperty(ce)&&zf(t,e,ce,void 0,o,Y);for(tt in o)Y=o[tt],I=a[tt],!o.hasOwnProperty(tt)||Y===I||Y===void 0&&I===void 0||zf(t,e,tt,Y,o,I);return}}for(var L in a)Y=a[L],a.hasOwnProperty(L)&&Y!=null&&!o.hasOwnProperty(L)&&ue(t,e,L,null,o,Y);for(rt in o)Y=o[rt],I=a[rt],!o.hasOwnProperty(rt)||Y===I||Y==null&&I==null||ue(t,e,rt,Y,o,I)}var Df=null,Nf=null;function zs(t){return t.nodeType===9?t:t.ownerDocument}function kg(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function $g(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function Bf(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var kf=null;function P1(){var t=window.event;return t&&t.type==="popstate"?t===kf?!1:(kf=t,!0):(kf=null,!1)}var jg=typeof setTimeout=="function"?setTimeout:void 0,q1=typeof clearTimeout=="function"?clearTimeout:void 0,_g=typeof Promise=="function"?Promise:void 0,G1=typeof queueMicrotask=="function"?queueMicrotask:typeof _g<"u"?function(t){return _g.resolve(null).then(t).catch(V1)}:jg;function V1(t){setTimeout(function(){throw t})}function Wa(t){return t==="head"}function Ug(t,e){var a=e,o=0,s=0;do{var f=a.nextSibling;if(t.removeChild(a),f&&f.nodeType===8)if(a=f.data,a==="/$"){if(0<o&&8>o){a=o;var g=t.ownerDocument;if(a&1&&ki(g.documentElement),a&2&&ki(g.body),a&4)for(a=g.head,ki(a),g=a.firstChild;g;){var b=g.nextSibling,B=g.nodeName;g[Qo]||B==="SCRIPT"||B==="STYLE"||B==="LINK"&&g.rel.toLowerCase()==="stylesheet"||a.removeChild(g),g=b}}if(s===0){t.removeChild(f),qi(e);return}s--}else a==="$"||a==="$?"||a==="$!"?s++:o=a.charCodeAt(0)-48;else o=0;a=f}while(a);qi(e)}function $f(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var a=e;switch(e=e.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":$f(a),Lu(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}t.removeChild(a)}}function Y1(t,e,a,o){for(;t.nodeType===1;){var s=a;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!o&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(o){if(!t[Qo])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(f=t.getAttribute("rel"),f==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(f!==s.rel||t.getAttribute("href")!==(s.href==null||s.href===""?null:s.href)||t.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin)||t.getAttribute("title")!==(s.title==null?null:s.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(f=t.getAttribute("src"),(f!==(s.src==null?null:s.src)||t.getAttribute("type")!==(s.type==null?null:s.type)||t.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin))&&f&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var f=s.name==null?null:""+s.name;if(s.type==="hidden"&&t.getAttribute("name")===f)return t}else return t;if(t=Xn(t.nextSibling),t===null)break}return null}function X1(t,e,a){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!a||(t=Xn(t.nextSibling),t===null))return null;return t}function jf(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function I1(t,e){var a=t.ownerDocument;if(t.data!=="$?"||a.readyState==="complete")e();else{var o=function(){e(),a.removeEventListener("DOMContentLoaded",o)};a.addEventListener("DOMContentLoaded",o),t._reactRetry=o}}function Xn(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var _f=null;function Lg(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var a=t.data;if(a==="$"||a==="$!"||a==="$?"){if(e===0)return t;e--}else a==="/$"&&e++}t=t.previousSibling}return null}function Hg(t,e,a){switch(e=zs(a),t){case"html":if(t=e.documentElement,!t)throw Error(l(452));return t;case"head":if(t=e.head,!t)throw Error(l(453));return t;case"body":if(t=e.body,!t)throw Error(l(454));return t;default:throw Error(l(451))}}function ki(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);Lu(t)}var jn=new Map,Pg=new Set;function Ds(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Ea=V.d;V.d={f:K1,r:Q1,D:W1,C:Z1,L:F1,m:J1,X:eS,S:tS,M:nS};function K1(){var t=Ea.f(),e=Cs();return t||e}function Q1(t){var e=_r(t);e!==null&&e.tag===5&&e.type==="form"?lh(e):Ea.r(t)}var vo=typeof document>"u"?null:document;function qg(t,e,a){var o=vo;if(o&&typeof e=="string"&&e){var s=Mn(e);s='link[rel="'+t+'"][href="'+s+'"]',typeof a=="string"&&(s+='[crossorigin="'+a+'"]'),Pg.has(s)||(Pg.add(s),t={rel:t,crossOrigin:a,href:e},o.querySelector(s)===null&&(e=o.createElement("link"),Ke(e,"link",t),qe(e),o.head.appendChild(e)))}}function W1(t){Ea.D(t),qg("dns-prefetch",t,null)}function Z1(t,e){Ea.C(t,e),qg("preconnect",t,e)}function F1(t,e,a){Ea.L(t,e,a);var o=vo;if(o&&t&&e){var s='link[rel="preload"][as="'+Mn(e)+'"]';e==="image"&&a&&a.imageSrcSet?(s+='[imagesrcset="'+Mn(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(s+='[imagesizes="'+Mn(a.imageSizes)+'"]')):s+='[href="'+Mn(t)+'"]';var f=s;switch(e){case"style":f=bo(t);break;case"script":f=So(t)}jn.has(f)||(t=y({rel:"preload",href:e==="image"&&a&&a.imageSrcSet?void 0:t,as:e},a),jn.set(f,t),o.querySelector(s)!==null||e==="style"&&o.querySelector($i(f))||e==="script"&&o.querySelector(ji(f))||(e=o.createElement("link"),Ke(e,"link",t),qe(e),o.head.appendChild(e)))}}function J1(t,e){Ea.m(t,e);var a=vo;if(a&&t){var o=e&&typeof e.as=="string"?e.as:"script",s='link[rel="modulepreload"][as="'+Mn(o)+'"][href="'+Mn(t)+'"]',f=s;switch(o){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":f=So(t)}if(!jn.has(f)&&(t=y({rel:"modulepreload",href:t},e),jn.set(f,t),a.querySelector(s)===null)){switch(o){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(ji(f)))return}o=a.createElement("link"),Ke(o,"link",t),qe(o),a.head.appendChild(o)}}}function tS(t,e,a){Ea.S(t,e,a);var o=vo;if(o&&t){var s=Ur(o).hoistableStyles,f=bo(t);e=e||"default";var g=s.get(f);if(!g){var b={loading:0,preload:null};if(g=o.querySelector($i(f)))b.loading=5;else{t=y({rel:"stylesheet",href:t,"data-precedence":e},a),(a=jn.get(f))&&Uf(t,a);var B=g=o.createElement("link");qe(B),Ke(B,"link",t),B._p=new Promise(function(G,tt){B.onload=G,B.onerror=tt}),B.addEventListener("load",function(){b.loading|=1}),B.addEventListener("error",function(){b.loading|=2}),b.loading|=4,Ns(g,e,o)}g={type:"stylesheet",instance:g,count:1,state:b},s.set(f,g)}}}function eS(t,e){Ea.X(t,e);var a=vo;if(a&&t){var o=Ur(a).hoistableScripts,s=So(t),f=o.get(s);f||(f=a.querySelector(ji(s)),f||(t=y({src:t,async:!0},e),(e=jn.get(s))&&Lf(t,e),f=a.createElement("script"),qe(f),Ke(f,"link",t),a.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},o.set(s,f))}}function nS(t,e){Ea.M(t,e);var a=vo;if(a&&t){var o=Ur(a).hoistableScripts,s=So(t),f=o.get(s);f||(f=a.querySelector(ji(s)),f||(t=y({src:t,async:!0,type:"module"},e),(e=jn.get(s))&&Lf(t,e),f=a.createElement("script"),qe(f),Ke(f,"link",t),a.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},o.set(s,f))}}function Gg(t,e,a,o){var s=(s=ft.current)?Ds(s):null;if(!s)throw Error(l(446));switch(t){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(e=bo(a.href),a=Ur(s).hoistableStyles,o=a.get(e),o||(o={type:"style",instance:null,count:0,state:null},a.set(e,o)),o):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){t=bo(a.href);var f=Ur(s).hoistableStyles,g=f.get(t);if(g||(s=s.ownerDocument||s,g={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},f.set(t,g),(f=s.querySelector($i(t)))&&!f._p&&(g.instance=f,g.state.loading=5),jn.has(t)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},jn.set(t,a),f||aS(s,t,a,g.state))),e&&o===null)throw Error(l(528,""));return g}if(e&&o!==null)throw Error(l(529,""));return null;case"script":return e=a.async,a=a.src,typeof a=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=So(a),a=Ur(s).hoistableScripts,o=a.get(e),o||(o={type:"script",instance:null,count:0,state:null},a.set(e,o)),o):{type:"void",instance:null,count:0,state:null};default:throw Error(l(444,t))}}function bo(t){return'href="'+Mn(t)+'"'}function $i(t){return'link[rel="stylesheet"]['+t+"]"}function Vg(t){return y({},t,{"data-precedence":t.precedence,precedence:null})}function aS(t,e,a,o){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?o.loading=1:(e=t.createElement("link"),o.preload=e,e.addEventListener("load",function(){return o.loading|=1}),e.addEventListener("error",function(){return o.loading|=2}),Ke(e,"link",a),qe(e),t.head.appendChild(e))}function So(t){return'[src="'+Mn(t)+'"]'}function ji(t){return"script[async]"+t}function Yg(t,e,a){if(e.count++,e.instance===null)switch(e.type){case"style":var o=t.querySelector('style[data-href~="'+Mn(a.href)+'"]');if(o)return e.instance=o,qe(o),o;var s=y({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return o=(t.ownerDocument||t).createElement("style"),qe(o),Ke(o,"style",s),Ns(o,a.precedence,t),e.instance=o;case"stylesheet":s=bo(a.href);var f=t.querySelector($i(s));if(f)return e.state.loading|=4,e.instance=f,qe(f),f;o=Vg(a),(s=jn.get(s))&&Uf(o,s),f=(t.ownerDocument||t).createElement("link"),qe(f);var g=f;return g._p=new Promise(function(b,B){g.onload=b,g.onerror=B}),Ke(f,"link",o),e.state.loading|=4,Ns(f,a.precedence,t),e.instance=f;case"script":return f=So(a.src),(s=t.querySelector(ji(f)))?(e.instance=s,qe(s),s):(o=a,(s=jn.get(f))&&(o=y({},a),Lf(o,s)),t=t.ownerDocument||t,s=t.createElement("script"),qe(s),Ke(s,"link",o),t.head.appendChild(s),e.instance=s);case"void":return null;default:throw Error(l(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(o=e.instance,e.state.loading|=4,Ns(o,a.precedence,t));return e.instance}function Ns(t,e,a){for(var o=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),s=o.length?o[o.length-1]:null,f=s,g=0;g<o.length;g++){var b=o[g];if(b.dataset.precedence===e)f=b;else if(f!==s)break}f?f.parentNode.insertBefore(t,f.nextSibling):(e=a.nodeType===9?a.head:a,e.insertBefore(t,e.firstChild))}function Uf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function Lf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Bs=null;function Xg(t,e,a){if(Bs===null){var o=new Map,s=Bs=new Map;s.set(a,o)}else s=Bs,o=s.get(a),o||(o=new Map,s.set(a,o));if(o.has(t))return o;for(o.set(t,null),a=a.getElementsByTagName(t),s=0;s<a.length;s++){var f=a[s];if(!(f[Qo]||f[Fe]||t==="link"&&f.getAttribute("rel")==="stylesheet")&&f.namespaceURI!=="http://www.w3.org/2000/svg"){var g=f.getAttribute(e)||"";g=t+g;var b=o.get(g);b?b.push(f):o.set(g,[f])}}return o}function Ig(t,e,a){t=t.ownerDocument||t,t.head.insertBefore(a,e==="title"?t.querySelector("head > title"):null)}function rS(t,e,a){if(a===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Kg(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var _i=null;function oS(){}function iS(t,e,a){if(_i===null)throw Error(l(475));var o=_i;if(e.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var s=bo(a.href),f=t.querySelector($i(s));if(f){t=f._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(o.count++,o=ks.bind(o),t.then(o,o)),e.state.loading|=4,e.instance=f,qe(f);return}f=t.ownerDocument||t,a=Vg(a),(s=jn.get(s))&&Uf(a,s),f=f.createElement("link"),qe(f);var g=f;g._p=new Promise(function(b,B){g.onload=b,g.onerror=B}),Ke(f,"link",a),e.instance=f}o.stylesheets===null&&(o.stylesheets=new Map),o.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(o.count++,e=ks.bind(o),t.addEventListener("load",e),t.addEventListener("error",e))}}function lS(){if(_i===null)throw Error(l(475));var t=_i;return t.stylesheets&&t.count===0&&Hf(t,t.stylesheets),0<t.count?function(e){var a=setTimeout(function(){if(t.stylesheets&&Hf(t,t.stylesheets),t.unsuspend){var o=t.unsuspend;t.unsuspend=null,o()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(a)}}:null}function ks(){if(this.count--,this.count===0){if(this.stylesheets)Hf(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var $s=null;function Hf(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,$s=new Map,e.forEach(sS,t),$s=null,ks.call(t))}function sS(t,e){if(!(e.state.loading&4)){var a=$s.get(t);if(a)var o=a.get(null);else{a=new Map,$s.set(t,a);for(var s=t.querySelectorAll("link[data-precedence],style[data-precedence]"),f=0;f<s.length;f++){var g=s[f];(g.nodeName==="LINK"||g.getAttribute("media")!=="not all")&&(a.set(g.dataset.precedence,g),o=g)}o&&a.set(null,o)}s=e.instance,g=s.getAttribute("data-precedence"),f=a.get(g)||o,f===o&&a.set(null,s),a.set(g,s),this.count++,o=ks.bind(this),s.addEventListener("load",o),s.addEventListener("error",o),f?f.parentNode.insertBefore(s,f.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(s,t.firstChild)),e.state.loading|=4}}var Ui={$$typeof:M,Provider:null,Consumer:null,_currentValue:ot,_currentValue2:ot,_threadCount:0};function uS(t,e,a,o,s,f,g,b){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Io(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Io(0),this.hiddenUpdates=Io(null),this.identifierPrefix=o,this.onUncaughtError=s,this.onCaughtError=f,this.onRecoverableError=g,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=b,this.incompleteTransitions=new Map}function Qg(t,e,a,o,s,f,g,b,B,G,tt,rt){return t=new uS(t,e,a,g,b,B,G,rt),e=1,f===!0&&(e|=24),f=yn(3,null,null,e),t.current=f,f.stateNode=t,e=Sc(),e.refCount++,t.pooledCache=e,e.refCount++,f.memoizedState={element:o,isDehydrated:a,cache:e},Ec(f),t}function Wg(t){return t?(t=Wr,t):Wr}function Zg(t,e,a,o,s,f){s=Wg(s),o.context===null?o.context=s:o.pendingContext=s,o=_a(e),o.payload={element:a},f=f===void 0?null:f,f!==null&&(o.callback=f),a=Ua(t,o,e),a!==null&&(Cn(a,t,e),mi(a,t,e))}function Fg(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var a=t.retryLane;t.retryLane=a!==0&&a<e?a:e}}function Pf(t,e){Fg(t,e),(t=t.alternate)&&Fg(t,e)}function Jg(t){if(t.tag===13){var e=Qr(t,67108864);e!==null&&Cn(e,t,67108864),Pf(t,67108864)}}var js=!0;function cS(t,e,a,o){var s=N.T;N.T=null;var f=V.p;try{V.p=2,qf(t,e,a,o)}finally{V.p=f,N.T=s}}function fS(t,e,a,o){var s=N.T;N.T=null;var f=V.p;try{V.p=8,qf(t,e,a,o)}finally{V.p=f,N.T=s}}function qf(t,e,a,o){if(js){var s=Gf(o);if(s===null)Mf(t,e,o,_s,a),ey(t,o);else if(pS(s,t,e,a,o))o.stopPropagation();else if(ey(t,o),e&4&&-1<dS.indexOf(t)){for(;s!==null;){var f=_r(s);if(f!==null)switch(f.tag){case 3:if(f=f.stateNode,f.current.memoizedState.isDehydrated){var g=ht(f.pendingLanes);if(g!==0){var b=f;for(b.pendingLanes|=2,b.entangledLanes|=2;g;){var B=1<<31-ne(g);b.entanglements[1]|=B,g&=~B}Fn(f),(re&6)===0&&(Ss=Ut()+500,Di(0))}}break;case 13:b=Qr(f,2),b!==null&&Cn(b,f,2),Cs(),Pf(f,2)}if(f=Gf(o),f===null&&Mf(t,e,o,_s,a),f===s)break;s=f}s!==null&&o.stopPropagation()}else Mf(t,e,o,null,a)}}function Gf(t){return t=Iu(t),Vf(t)}var _s=null;function Vf(t){if(_s=null,t=jr(t),t!==null){var e=c(t);if(e===null)t=null;else{var a=e.tag;if(a===13){if(t=d(e),t!==null)return t;t=null}else if(a===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return _s=t,null}function ty(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Kt()){case Qt:return 2;case Vt:return 8;case jt:case dt:return 32;case _e:return 268435456;default:return 32}default:return 32}}var Yf=!1,Za=null,Fa=null,Ja=null,Li=new Map,Hi=new Map,tr=[],dS="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function ey(t,e){switch(t){case"focusin":case"focusout":Za=null;break;case"dragenter":case"dragleave":Fa=null;break;case"mouseover":case"mouseout":Ja=null;break;case"pointerover":case"pointerout":Li.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Hi.delete(e.pointerId)}}function Pi(t,e,a,o,s,f){return t===null||t.nativeEvent!==f?(t={blockedOn:e,domEventName:a,eventSystemFlags:o,nativeEvent:f,targetContainers:[s]},e!==null&&(e=_r(e),e!==null&&Jg(e)),t):(t.eventSystemFlags|=o,e=t.targetContainers,s!==null&&e.indexOf(s)===-1&&e.push(s),t)}function pS(t,e,a,o,s){switch(e){case"focusin":return Za=Pi(Za,t,e,a,o,s),!0;case"dragenter":return Fa=Pi(Fa,t,e,a,o,s),!0;case"mouseover":return Ja=Pi(Ja,t,e,a,o,s),!0;case"pointerover":var f=s.pointerId;return Li.set(f,Pi(Li.get(f)||null,t,e,a,o,s)),!0;case"gotpointercapture":return f=s.pointerId,Hi.set(f,Pi(Hi.get(f)||null,t,e,a,o,s)),!0}return!1}function ny(t){var e=jr(t.target);if(e!==null){var a=c(e);if(a!==null){if(e=a.tag,e===13){if(e=d(a),e!==null){t.blockedOn=e,Nl(t.priority,function(){if(a.tag===13){var o=xn();o=Ko(o);var s=Qr(a,o);s!==null&&Cn(s,a,o),Pf(a,o)}});return}}else if(e===3&&a.stateNode.current.memoizedState.isDehydrated){t.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Us(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var a=Gf(t.nativeEvent);if(a===null){a=t.nativeEvent;var o=new a.constructor(a.type,a);Xu=o,a.target.dispatchEvent(o),Xu=null}else return e=_r(a),e!==null&&Jg(e),t.blockedOn=a,!1;e.shift()}return!0}function ay(t,e,a){Us(t)&&a.delete(e)}function mS(){Yf=!1,Za!==null&&Us(Za)&&(Za=null),Fa!==null&&Us(Fa)&&(Fa=null),Ja!==null&&Us(Ja)&&(Ja=null),Li.forEach(ay),Hi.forEach(ay)}function Ls(t,e){t.blockedOn===e&&(t.blockedOn=null,Yf||(Yf=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,mS)))}var Hs=null;function ry(t){Hs!==t&&(Hs=t,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){Hs===t&&(Hs=null);for(var e=0;e<t.length;e+=3){var a=t[e],o=t[e+1],s=t[e+2];if(typeof o!="function"){if(Vf(o||a)===null)continue;break}var f=_r(a);f!==null&&(t.splice(e,3),e-=3,Gc(f,{pending:!0,data:s,method:a.method,action:o},o,s))}}))}function qi(t){function e(B){return Ls(B,t)}Za!==null&&Ls(Za,t),Fa!==null&&Ls(Fa,t),Ja!==null&&Ls(Ja,t),Li.forEach(e),Hi.forEach(e);for(var a=0;a<tr.length;a++){var o=tr[a];o.blockedOn===t&&(o.blockedOn=null)}for(;0<tr.length&&(a=tr[0],a.blockedOn===null);)ny(a),a.blockedOn===null&&tr.shift();if(a=(t.ownerDocument||t).$$reactFormReplay,a!=null)for(o=0;o<a.length;o+=3){var s=a[o],f=a[o+1],g=s[ln]||null;if(typeof f=="function")g||ry(a);else if(g){var b=null;if(f&&f.hasAttribute("formAction")){if(s=f,g=f[ln]||null)b=g.formAction;else if(Vf(s)!==null)continue}else b=g.action;typeof b=="function"?a[o+1]=b:(a.splice(o,3),o-=3),ry(a)}}}function Xf(t){this._internalRoot=t}Ps.prototype.render=Xf.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(l(409));var a=e.current,o=xn();Zg(a,o,t,e,null,null)},Ps.prototype.unmount=Xf.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Zg(t.current,2,null,t,null,null),Cs(),e[$r]=null}};function Ps(t){this._internalRoot=t}Ps.prototype.unstable_scheduleHydration=function(t){if(t){var e=on();t={blockedOn:null,target:t,priority:e};for(var a=0;a<tr.length&&e!==0&&e<tr[a].priority;a++);tr.splice(a,0,t),a===0&&ny(t)}};var oy=r.version;if(oy!=="19.1.0")throw Error(l(527,oy,"19.1.0"));V.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(l(188)):(t=Object.keys(t).join(","),Error(l(268,t)));return t=m(e),t=t!==null?h(t):null,t=t===null?null:t.stateNode,t};var hS={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:N,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var qs=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!qs.isDisabled&&qs.supportsFiber)try{Ue=qs.inject(hS),ee=qs}catch{}}return Yi.createRoot=function(t,e){if(!u(t))throw Error(l(299));var a=!1,o="",s=xh,f=Ch,g=Th,b=null;return e!=null&&(e.unstable_strictMode===!0&&(a=!0),e.identifierPrefix!==void 0&&(o=e.identifierPrefix),e.onUncaughtError!==void 0&&(s=e.onUncaughtError),e.onCaughtError!==void 0&&(f=e.onCaughtError),e.onRecoverableError!==void 0&&(g=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(b=e.unstable_transitionCallbacks)),e=Qg(t,1,!1,null,null,a,o,s,f,g,b,null),t[$r]=e.current,Af(t),new Xf(e)},Yi.hydrateRoot=function(t,e,a){if(!u(t))throw Error(l(299));var o=!1,s="",f=xh,g=Ch,b=Th,B=null,G=null;return a!=null&&(a.unstable_strictMode===!0&&(o=!0),a.identifierPrefix!==void 0&&(s=a.identifierPrefix),a.onUncaughtError!==void 0&&(f=a.onUncaughtError),a.onCaughtError!==void 0&&(g=a.onCaughtError),a.onRecoverableError!==void 0&&(b=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(B=a.unstable_transitionCallbacks),a.formState!==void 0&&(G=a.formState)),e=Qg(t,1,!0,e,a??null,o,s,f,g,b,B,G),e.context=Wg(null),a=e.current,o=xn(),o=Ko(o),s=_a(o),s.callback=null,Ua(a,s,o),a=o,e.current.lanes=a,lr(e,a),Fn(e),t[$r]=e.current,Af(t),new Ps(e)},Yi.version="19.1.0",Yi}var hy;function OS(){if(hy)return Qf.exports;hy=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),Qf.exports=wS(),Qf.exports}var AS=OS();function Ma(n,...r){const i=new URL(`https://mui.com/production-error/?code=${n}`);return r.forEach(l=>i.searchParams.append("args[]",l)),`Minified MUI error #${n}; visit ${i} for the full message.`}const oa="$$material";function lu(){return lu=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var i=arguments[r];for(var l in i)({}).hasOwnProperty.call(i,l)&&(n[l]=i[l])}return n},lu.apply(null,arguments)}function MS(n){if(n.sheet)return n.sheet;for(var r=0;r<document.styleSheets.length;r++)if(document.styleSheets[r].ownerNode===n)return document.styleSheets[r]}function zS(n){var r=document.createElement("style");return r.setAttribute("data-emotion",n.key),n.nonce!==void 0&&r.setAttribute("nonce",n.nonce),r.appendChild(document.createTextNode("")),r.setAttribute("data-s",""),r}var DS=function(){function n(i){var l=this;this._insertTag=function(u){var c;l.tags.length===0?l.insertionPoint?c=l.insertionPoint.nextSibling:l.prepend?c=l.container.firstChild:c=l.before:c=l.tags[l.tags.length-1].nextSibling,l.container.insertBefore(u,c),l.tags.push(u)},this.isSpeedy=i.speedy===void 0?!0:i.speedy,this.tags=[],this.ctr=0,this.nonce=i.nonce,this.key=i.key,this.container=i.container,this.prepend=i.prepend,this.insertionPoint=i.insertionPoint,this.before=null}var r=n.prototype;return r.hydrate=function(l){l.forEach(this._insertTag)},r.insert=function(l){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(zS(this));var u=this.tags[this.tags.length-1];if(this.isSpeedy){var c=MS(u);try{c.insertRule(l,c.cssRules.length)}catch{}}else u.appendChild(document.createTextNode(l));this.ctr++},r.flush=function(){this.tags.forEach(function(l){var u;return(u=l.parentNode)==null?void 0:u.removeChild(l)}),this.tags=[],this.ctr=0},n}(),en="-ms-",su="-moz-",Ft="-webkit-",Lv="comm",_d="rule",Ud="decl",NS="@import",Hv="@keyframes",BS="@layer",kS=Math.abs,vu=String.fromCharCode,$S=Object.assign;function jS(n,r){return We(n,0)^45?(((r<<2^We(n,0))<<2^We(n,1))<<2^We(n,2))<<2^We(n,3):0}function Pv(n){return n.trim()}function _S(n,r){return(n=r.exec(n))?n[0]:n}function Jt(n,r,i){return n.replace(r,i)}function pd(n,r){return n.indexOf(r)}function We(n,r){return n.charCodeAt(r)|0}function cl(n,r,i){return n.slice(r,i)}function ea(n){return n.length}function Ld(n){return n.length}function Gs(n,r){return r.push(n),n}function US(n,r){return n.map(r).join("")}var bu=1,Bo=1,qv=0,mn=0,je=0,Ho="";function Su(n,r,i,l,u,c,d){return{value:n,root:r,parent:i,type:l,props:u,children:c,line:bu,column:Bo,length:d,return:""}}function Xi(n,r){return $S(Su("",null,null,"",null,null,0),n,{length:-n.length},r)}function LS(){return je}function HS(){return je=mn>0?We(Ho,--mn):0,Bo--,je===10&&(Bo=1,bu--),je}function En(){return je=mn<qv?We(Ho,mn++):0,Bo++,je===10&&(Bo=1,bu++),je}function ia(){return We(Ho,mn)}function tu(){return mn}function yl(n,r){return cl(Ho,n,r)}function fl(n){switch(n){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Gv(n){return bu=Bo=1,qv=ea(Ho=n),mn=0,[]}function Vv(n){return Ho="",n}function eu(n){return Pv(yl(mn-1,md(n===91?n+2:n===40?n+1:n)))}function PS(n){for(;(je=ia())&&je<33;)En();return fl(n)>2||fl(je)>3?"":" "}function qS(n,r){for(;--r&&En()&&!(je<48||je>102||je>57&&je<65||je>70&&je<97););return yl(n,tu()+(r<6&&ia()==32&&En()==32))}function md(n){for(;En();)switch(je){case n:return mn;case 34:case 39:n!==34&&n!==39&&md(je);break;case 40:n===41&&md(n);break;case 92:En();break}return mn}function GS(n,r){for(;En()&&n+je!==57;)if(n+je===84&&ia()===47)break;return"/*"+yl(r,mn-1)+"*"+vu(n===47?n:En())}function VS(n){for(;!fl(ia());)En();return yl(n,mn)}function YS(n){return Vv(nu("",null,null,null,[""],n=Gv(n),0,[0],n))}function nu(n,r,i,l,u,c,d,p,m){for(var h=0,y=0,S=d,T=0,E=0,x=0,C=1,D=1,k=1,U=0,M="",A=u,w=c,j=l,H=M;D;)switch(x=U,U=En()){case 40:if(x!=108&&We(H,S-1)==58){pd(H+=Jt(eu(U),"&","&\f"),"&\f")!=-1&&(k=-1);break}case 34:case 39:case 91:H+=eu(U);break;case 9:case 10:case 13:case 32:H+=PS(x);break;case 92:H+=qS(tu()-1,7);continue;case 47:switch(ia()){case 42:case 47:Gs(XS(GS(En(),tu()),r,i),m);break;default:H+="/"}break;case 123*C:p[h++]=ea(H)*k;case 125*C:case 59:case 0:switch(U){case 0:case 125:D=0;case 59+y:k==-1&&(H=Jt(H,/\f/g,"")),E>0&&ea(H)-S&&Gs(E>32?yy(H+";",l,i,S-1):yy(Jt(H," ","")+";",l,i,S-2),m);break;case 59:H+=";";default:if(Gs(j=gy(H,r,i,h,y,u,p,M,A=[],w=[],S),c),U===123)if(y===0)nu(H,r,j,j,A,c,S,p,w);else switch(T===99&&We(H,3)===110?100:T){case 100:case 108:case 109:case 115:nu(n,j,j,l&&Gs(gy(n,j,j,0,0,u,p,M,u,A=[],S),w),u,w,S,p,l?A:w);break;default:nu(H,j,j,j,[""],w,0,p,w)}}h=y=E=0,C=k=1,M=H="",S=d;break;case 58:S=1+ea(H),E=x;default:if(C<1){if(U==123)--C;else if(U==125&&C++==0&&HS()==125)continue}switch(H+=vu(U),U*C){case 38:k=y>0?1:(H+="\f",-1);break;case 44:p[h++]=(ea(H)-1)*k,k=1;break;case 64:ia()===45&&(H+=eu(En())),T=ia(),y=S=ea(M=H+=VS(tu())),U++;break;case 45:x===45&&ea(H)==2&&(C=0)}}return c}function gy(n,r,i,l,u,c,d,p,m,h,y){for(var S=u-1,T=u===0?c:[""],E=Ld(T),x=0,C=0,D=0;x<l;++x)for(var k=0,U=cl(n,S+1,S=kS(C=d[x])),M=n;k<E;++k)(M=Pv(C>0?T[k]+" "+U:Jt(U,/&\f/g,T[k])))&&(m[D++]=M);return Su(n,r,i,u===0?_d:p,m,h,y)}function XS(n,r,i){return Su(n,r,i,Lv,vu(LS()),cl(n,2,-2),0)}function yy(n,r,i,l){return Su(n,r,i,Ud,cl(n,0,l),cl(n,l+1,-1),l)}function zo(n,r){for(var i="",l=Ld(n),u=0;u<l;u++)i+=r(n[u],u,n,r)||"";return i}function IS(n,r,i,l){switch(n.type){case BS:if(n.children.length)break;case NS:case Ud:return n.return=n.return||n.value;case Lv:return"";case Hv:return n.return=n.value+"{"+zo(n.children,l)+"}";case _d:n.value=n.props.join(",")}return ea(i=zo(n.children,l))?n.return=n.value+"{"+i+"}":""}function KS(n){var r=Ld(n);return function(i,l,u,c){for(var d="",p=0;p<r;p++)d+=n[p](i,l,u,c)||"";return d}}function QS(n){return function(r){r.root||(r=r.return)&&n(r)}}function Yv(n){var r=Object.create(null);return function(i){return r[i]===void 0&&(r[i]=n(i)),r[i]}}var WS=function(r,i,l){for(var u=0,c=0;u=c,c=ia(),u===38&&c===12&&(i[l]=1),!fl(c);)En();return yl(r,mn)},ZS=function(r,i){var l=-1,u=44;do switch(fl(u)){case 0:u===38&&ia()===12&&(i[l]=1),r[l]+=WS(mn-1,i,l);break;case 2:r[l]+=eu(u);break;case 4:if(u===44){r[++l]=ia()===58?"&\f":"",i[l]=r[l].length;break}default:r[l]+=vu(u)}while(u=En());return r},FS=function(r,i){return Vv(ZS(Gv(r),i))},vy=new WeakMap,JS=function(r){if(!(r.type!=="rule"||!r.parent||r.length<1)){for(var i=r.value,l=r.parent,u=r.column===l.column&&r.line===l.line;l.type!=="rule";)if(l=l.parent,!l)return;if(!(r.props.length===1&&i.charCodeAt(0)!==58&&!vy.get(l))&&!u){vy.set(r,!0);for(var c=[],d=FS(i,c),p=l.props,m=0,h=0;m<d.length;m++)for(var y=0;y<p.length;y++,h++)r.props[h]=c[m]?d[m].replace(/&\f/g,p[y]):p[y]+" "+d[m]}}},tx=function(r){if(r.type==="decl"){var i=r.value;i.charCodeAt(0)===108&&i.charCodeAt(2)===98&&(r.return="",r.value="")}};function Xv(n,r){switch(jS(n,r)){case 5103:return Ft+"print-"+n+n;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return Ft+n+n;case 5349:case 4246:case 4810:case 6968:case 2756:return Ft+n+su+n+en+n+n;case 6828:case 4268:return Ft+n+en+n+n;case 6165:return Ft+n+en+"flex-"+n+n;case 5187:return Ft+n+Jt(n,/(\w+).+(:[^]+)/,Ft+"box-$1$2"+en+"flex-$1$2")+n;case 5443:return Ft+n+en+"flex-item-"+Jt(n,/flex-|-self/,"")+n;case 4675:return Ft+n+en+"flex-line-pack"+Jt(n,/align-content|flex-|-self/,"")+n;case 5548:return Ft+n+en+Jt(n,"shrink","negative")+n;case 5292:return Ft+n+en+Jt(n,"basis","preferred-size")+n;case 6060:return Ft+"box-"+Jt(n,"-grow","")+Ft+n+en+Jt(n,"grow","positive")+n;case 4554:return Ft+Jt(n,/([^-])(transform)/g,"$1"+Ft+"$2")+n;case 6187:return Jt(Jt(Jt(n,/(zoom-|grab)/,Ft+"$1"),/(image-set)/,Ft+"$1"),n,"")+n;case 5495:case 3959:return Jt(n,/(image-set\([^]*)/,Ft+"$1$`$1");case 4968:return Jt(Jt(n,/(.+:)(flex-)?(.*)/,Ft+"box-pack:$3"+en+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+Ft+n+n;case 4095:case 3583:case 4068:case 2532:return Jt(n,/(.+)-inline(.+)/,Ft+"$1$2")+n;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(ea(n)-1-r>6)switch(We(n,r+1)){case 109:if(We(n,r+4)!==45)break;case 102:return Jt(n,/(.+:)(.+)-([^]+)/,"$1"+Ft+"$2-$3$1"+su+(We(n,r+3)==108?"$3":"$2-$3"))+n;case 115:return~pd(n,"stretch")?Xv(Jt(n,"stretch","fill-available"),r)+n:n}break;case 4949:if(We(n,r+1)!==115)break;case 6444:switch(We(n,ea(n)-3-(~pd(n,"!important")&&10))){case 107:return Jt(n,":",":"+Ft)+n;case 101:return Jt(n,/(.+:)([^;!]+)(;|!.+)?/,"$1"+Ft+(We(n,14)===45?"inline-":"")+"box$3$1"+Ft+"$2$3$1"+en+"$2box$3")+n}break;case 5936:switch(We(n,r+11)){case 114:return Ft+n+en+Jt(n,/[svh]\w+-[tblr]{2}/,"tb")+n;case 108:return Ft+n+en+Jt(n,/[svh]\w+-[tblr]{2}/,"tb-rl")+n;case 45:return Ft+n+en+Jt(n,/[svh]\w+-[tblr]{2}/,"lr")+n}return Ft+n+en+n+n}return n}var ex=function(r,i,l,u){if(r.length>-1&&!r.return)switch(r.type){case Ud:r.return=Xv(r.value,r.length);break;case Hv:return zo([Xi(r,{value:Jt(r.value,"@","@"+Ft)})],u);case _d:if(r.length)return US(r.props,function(c){switch(_S(c,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return zo([Xi(r,{props:[Jt(c,/:(read-\w+)/,":"+su+"$1")]})],u);case"::placeholder":return zo([Xi(r,{props:[Jt(c,/:(plac\w+)/,":"+Ft+"input-$1")]}),Xi(r,{props:[Jt(c,/:(plac\w+)/,":"+su+"$1")]}),Xi(r,{props:[Jt(c,/:(plac\w+)/,en+"input-$1")]})],u)}return""})}},nx=[ex],ax=function(r){var i=r.key;if(i==="css"){var l=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(l,function(C){var D=C.getAttribute("data-emotion");D.indexOf(" ")!==-1&&(document.head.appendChild(C),C.setAttribute("data-s",""))})}var u=r.stylisPlugins||nx,c={},d,p=[];d=r.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+i+' "]'),function(C){for(var D=C.getAttribute("data-emotion").split(" "),k=1;k<D.length;k++)c[D[k]]=!0;p.push(C)});var m,h=[JS,tx];{var y,S=[IS,QS(function(C){y.insert(C)})],T=KS(h.concat(u,S)),E=function(D){return zo(YS(D),T)};m=function(D,k,U,M){y=U,E(D?D+"{"+k.styles+"}":k.styles),M&&(x.inserted[k.name]=!0)}}var x={key:i,sheet:new DS({key:i,container:d,nonce:r.nonce,speedy:r.speedy,prepend:r.prepend,insertionPoint:r.insertionPoint}),nonce:r.nonce,inserted:c,registered:{},insert:m};return x.sheet.hydrate(p),x},Jf={exports:{}},te={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var by;function rx(){if(by)return te;by=1;var n=typeof Symbol=="function"&&Symbol.for,r=n?Symbol.for("react.element"):60103,i=n?Symbol.for("react.portal"):60106,l=n?Symbol.for("react.fragment"):60107,u=n?Symbol.for("react.strict_mode"):60108,c=n?Symbol.for("react.profiler"):60114,d=n?Symbol.for("react.provider"):60109,p=n?Symbol.for("react.context"):60110,m=n?Symbol.for("react.async_mode"):60111,h=n?Symbol.for("react.concurrent_mode"):60111,y=n?Symbol.for("react.forward_ref"):60112,S=n?Symbol.for("react.suspense"):60113,T=n?Symbol.for("react.suspense_list"):60120,E=n?Symbol.for("react.memo"):60115,x=n?Symbol.for("react.lazy"):60116,C=n?Symbol.for("react.block"):60121,D=n?Symbol.for("react.fundamental"):60117,k=n?Symbol.for("react.responder"):60118,U=n?Symbol.for("react.scope"):60119;function M(w){if(typeof w=="object"&&w!==null){var j=w.$$typeof;switch(j){case r:switch(w=w.type,w){case m:case h:case l:case c:case u:case S:return w;default:switch(w=w&&w.$$typeof,w){case p:case y:case x:case E:case d:return w;default:return j}}case i:return j}}}function A(w){return M(w)===h}return te.AsyncMode=m,te.ConcurrentMode=h,te.ContextConsumer=p,te.ContextProvider=d,te.Element=r,te.ForwardRef=y,te.Fragment=l,te.Lazy=x,te.Memo=E,te.Portal=i,te.Profiler=c,te.StrictMode=u,te.Suspense=S,te.isAsyncMode=function(w){return A(w)||M(w)===m},te.isConcurrentMode=A,te.isContextConsumer=function(w){return M(w)===p},te.isContextProvider=function(w){return M(w)===d},te.isElement=function(w){return typeof w=="object"&&w!==null&&w.$$typeof===r},te.isForwardRef=function(w){return M(w)===y},te.isFragment=function(w){return M(w)===l},te.isLazy=function(w){return M(w)===x},te.isMemo=function(w){return M(w)===E},te.isPortal=function(w){return M(w)===i},te.isProfiler=function(w){return M(w)===c},te.isStrictMode=function(w){return M(w)===u},te.isSuspense=function(w){return M(w)===S},te.isValidElementType=function(w){return typeof w=="string"||typeof w=="function"||w===l||w===h||w===c||w===u||w===S||w===T||typeof w=="object"&&w!==null&&(w.$$typeof===x||w.$$typeof===E||w.$$typeof===d||w.$$typeof===p||w.$$typeof===y||w.$$typeof===D||w.$$typeof===k||w.$$typeof===U||w.$$typeof===C)},te.typeOf=M,te}var Sy;function ox(){return Sy||(Sy=1,Jf.exports=rx()),Jf.exports}var td,xy;function ix(){if(xy)return td;xy=1;var n=ox(),r={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},i={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},l={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},u={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},c={};c[n.ForwardRef]=l,c[n.Memo]=u;function d(x){return n.isMemo(x)?u:c[x.$$typeof]||r}var p=Object.defineProperty,m=Object.getOwnPropertyNames,h=Object.getOwnPropertySymbols,y=Object.getOwnPropertyDescriptor,S=Object.getPrototypeOf,T=Object.prototype;function E(x,C,D){if(typeof C!="string"){if(T){var k=S(C);k&&k!==T&&E(x,k,D)}var U=m(C);h&&(U=U.concat(h(C)));for(var M=d(x),A=d(C),w=0;w<U.length;++w){var j=U[w];if(!i[j]&&!(D&&D[j])&&!(A&&A[j])&&!(M&&M[j])){var H=y(C,j);try{p(x,j,H)}catch{}}}}return x}return td=E,td}ix();var lx=!0;function Iv(n,r,i){var l="";return i.split(" ").forEach(function(u){n[u]!==void 0?r.push(n[u]+";"):u&&(l+=u+" ")}),l}var Hd=function(r,i,l){var u=r.key+"-"+i.name;(l===!1||lx===!1)&&r.registered[u]===void 0&&(r.registered[u]=i.styles)},Pd=function(r,i,l){Hd(r,i,l);var u=r.key+"-"+i.name;if(r.inserted[i.name]===void 0){var c=i;do r.insert(i===c?"."+u:"",c,r.sheet,!0),c=c.next;while(c!==void 0)}};function sx(n){for(var r=0,i,l=0,u=n.length;u>=4;++l,u-=4)i=n.charCodeAt(l)&255|(n.charCodeAt(++l)&255)<<8|(n.charCodeAt(++l)&255)<<16|(n.charCodeAt(++l)&255)<<24,i=(i&65535)*1540483477+((i>>>16)*59797<<16),i^=i>>>24,r=(i&65535)*1540483477+((i>>>16)*59797<<16)^(r&65535)*1540483477+((r>>>16)*59797<<16);switch(u){case 3:r^=(n.charCodeAt(l+2)&255)<<16;case 2:r^=(n.charCodeAt(l+1)&255)<<8;case 1:r^=n.charCodeAt(l)&255,r=(r&65535)*1540483477+((r>>>16)*59797<<16)}return r^=r>>>13,r=(r&65535)*1540483477+((r>>>16)*59797<<16),((r^r>>>15)>>>0).toString(36)}var ux={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},cx=/[A-Z]|^ms/g,fx=/_EMO_([^_]+?)_([^]*?)_EMO_/g,Kv=function(r){return r.charCodeAt(1)===45},Cy=function(r){return r!=null&&typeof r!="boolean"},ed=Yv(function(n){return Kv(n)?n:n.replace(cx,"-$&").toLowerCase()}),Ty=function(r,i){switch(r){case"animation":case"animationName":if(typeof i=="string")return i.replace(fx,function(l,u,c){return na={name:u,styles:c,next:na},u})}return ux[r]!==1&&!Kv(r)&&typeof i=="number"&&i!==0?i+"px":i};function dl(n,r,i){if(i==null)return"";var l=i;if(l.__emotion_styles!==void 0)return l;switch(typeof i){case"boolean":return"";case"object":{var u=i;if(u.anim===1)return na={name:u.name,styles:u.styles,next:na},u.name;var c=i;if(c.styles!==void 0){var d=c.next;if(d!==void 0)for(;d!==void 0;)na={name:d.name,styles:d.styles,next:na},d=d.next;var p=c.styles+";";return p}return dx(n,r,i)}case"function":{if(n!==void 0){var m=na,h=i(n);return na=m,dl(n,r,h)}break}}var y=i;if(r==null)return y;var S=r[y];return S!==void 0?S:y}function dx(n,r,i){var l="";if(Array.isArray(i))for(var u=0;u<i.length;u++)l+=dl(n,r,i[u])+";";else for(var c in i){var d=i[c];if(typeof d!="object"){var p=d;r!=null&&r[p]!==void 0?l+=c+"{"+r[p]+"}":Cy(p)&&(l+=ed(c)+":"+Ty(c,p)+";")}else if(Array.isArray(d)&&typeof d[0]=="string"&&(r==null||r[d[0]]===void 0))for(var m=0;m<d.length;m++)Cy(d[m])&&(l+=ed(c)+":"+Ty(c,d[m])+";");else{var h=dl(n,r,d);switch(c){case"animation":case"animationName":{l+=ed(c)+":"+h+";";break}default:l+=c+"{"+h+"}"}}}return l}var Ey=/label:\s*([^\s;{]+)\s*(;|$)/g,na;function vl(n,r,i){if(n.length===1&&typeof n[0]=="object"&&n[0]!==null&&n[0].styles!==void 0)return n[0];var l=!0,u="";na=void 0;var c=n[0];if(c==null||c.raw===void 0)l=!1,u+=dl(i,r,c);else{var d=c;u+=d[0]}for(var p=1;p<n.length;p++)if(u+=dl(i,r,n[p]),l){var m=c;u+=m[p]}Ey.lastIndex=0;for(var h="",y;(y=Ey.exec(u))!==null;)h+="-"+y[1];var S=sx(u)+h;return{name:S,styles:u,next:na}}var px=function(r){return r()},Qv=dd.useInsertionEffect?dd.useInsertionEffect:!1,Wv=Qv||px,Ry=Qv||R.useLayoutEffect,Zv=R.createContext(typeof HTMLElement<"u"?ax({key:"css"}):null);Zv.Provider;var qd=function(r){return R.forwardRef(function(i,l){var u=R.useContext(Zv);return r(i,u,l)})},bl=R.createContext({}),Gd={}.hasOwnProperty,hd="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",mx=function(r,i){var l={};for(var u in i)Gd.call(i,u)&&(l[u]=i[u]);return l[hd]=r,l},hx=function(r){var i=r.cache,l=r.serialized,u=r.isStringTag;return Hd(i,l,u),Wv(function(){return Pd(i,l,u)}),null},gx=qd(function(n,r,i){var l=n.css;typeof l=="string"&&r.registered[l]!==void 0&&(l=r.registered[l]);var u=n[hd],c=[l],d="";typeof n.className=="string"?d=Iv(r.registered,c,n.className):n.className!=null&&(d=n.className+" ");var p=vl(c,void 0,R.useContext(bl));d+=r.key+"-"+p.name;var m={};for(var h in n)Gd.call(n,h)&&h!=="css"&&h!==hd&&(m[h]=n[h]);return m.className=d,i&&(m.ref=i),R.createElement(R.Fragment,null,R.createElement(hx,{cache:r,serialized:p,isStringTag:typeof u=="string"}),R.createElement(u,m))}),yx=gx,wy=function(r,i){var l=arguments;if(i==null||!Gd.call(i,"css"))return R.createElement.apply(void 0,l);var u=l.length,c=new Array(u);c[0]=yx,c[1]=mx(r,i);for(var d=2;d<u;d++)c[d]=l[d];return R.createElement.apply(null,c)};(function(n){var r;r||(r=n.JSX||(n.JSX={}))})(wy||(wy={}));var vx=qd(function(n,r){var i=n.styles,l=vl([i],void 0,R.useContext(bl)),u=R.useRef();return Ry(function(){var c=r.key+"-global",d=new r.sheet.constructor({key:c,nonce:r.sheet.nonce,container:r.sheet.container,speedy:r.sheet.isSpeedy}),p=!1,m=document.querySelector('style[data-emotion="'+c+" "+l.name+'"]');return r.sheet.tags.length&&(d.before=r.sheet.tags[0]),m!==null&&(p=!0,m.setAttribute("data-emotion",c),d.hydrate([m])),u.current=[d,p],function(){d.flush()}},[r]),Ry(function(){var c=u.current,d=c[0],p=c[1];if(p){c[1]=!1;return}if(l.next!==void 0&&Pd(r,l.next,!0),d.tags.length){var m=d.tags[d.tags.length-1].nextElementSibling;d.before=m,d.flush()}r.insert("",l,d,!1)},[r,l.name]),null});function Vd(){for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return vl(r)}function Sl(){var n=Vd.apply(void 0,arguments),r="animation-"+n.name;return{name:r,styles:"@keyframes "+r+"{"+n.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}var bx=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,Sx=Yv(function(n){return bx.test(n)||n.charCodeAt(0)===111&&n.charCodeAt(1)===110&&n.charCodeAt(2)<91}),xx=Sx,Cx=function(r){return r!=="theme"},Oy=function(r){return typeof r=="string"&&r.charCodeAt(0)>96?xx:Cx},Ay=function(r,i,l){var u;if(i){var c=i.shouldForwardProp;u=r.__emotion_forwardProp&&c?function(d){return r.__emotion_forwardProp(d)&&c(d)}:c}return typeof u!="function"&&l&&(u=r.__emotion_forwardProp),u},Tx=function(r){var i=r.cache,l=r.serialized,u=r.isStringTag;return Hd(i,l,u),Wv(function(){return Pd(i,l,u)}),null},Ex=function n(r,i){var l=r.__emotion_real===r,u=l&&r.__emotion_base||r,c,d;i!==void 0&&(c=i.label,d=i.target);var p=Ay(r,i,l),m=p||Oy(u),h=!m("as");return function(){var y=arguments,S=l&&r.__emotion_styles!==void 0?r.__emotion_styles.slice(0):[];if(c!==void 0&&S.push("label:"+c+";"),y[0]==null||y[0].raw===void 0)S.push.apply(S,y);else{var T=y[0];S.push(T[0]);for(var E=y.length,x=1;x<E;x++)S.push(y[x],T[x])}var C=qd(function(D,k,U){var M=h&&D.as||u,A="",w=[],j=D;if(D.theme==null){j={};for(var H in D)j[H]=D[H];j.theme=R.useContext(bl)}typeof D.className=="string"?A=Iv(k.registered,w,D.className):D.className!=null&&(A=D.className+" ");var q=vl(S.concat(w),k.registered,j);A+=k.key+"-"+q.name,d!==void 0&&(A+=" "+d);var Q=h&&p===void 0?Oy(M):m,v={};for(var _ in D)h&&_==="as"||Q(_)&&(v[_]=D[_]);return v.className=A,U&&(v.ref=U),R.createElement(R.Fragment,null,R.createElement(Tx,{cache:k,serialized:q,isStringTag:typeof M=="string"}),R.createElement(M,v))});return C.displayName=c!==void 0?c:"Styled("+(typeof u=="string"?u:u.displayName||u.name||"Component")+")",C.defaultProps=r.defaultProps,C.__emotion_real=C,C.__emotion_base=u,C.__emotion_styles=S,C.__emotion_forwardProp=p,Object.defineProperty(C,"toString",{value:function(){return"."+d}}),C.withComponent=function(D,k){var U=n(D,lu({},i,k,{shouldForwardProp:Ay(C,k,!0)}));return U.apply(void 0,S)},C}},Rx=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"],gd=Ex.bind(null);Rx.forEach(function(n){gd[n]=gd(n)});function wx(n){return n==null||Object.keys(n).length===0}function Fv(n){const{styles:r,defaultTheme:i={}}=n,l=typeof r=="function"?u=>r(wx(u)?i:u):r;return O.jsx(vx,{styles:l})}function Jv(n,r){return gd(n,r)}function Ox(n,r){Array.isArray(n.__emotion_styles)&&(n.__emotion_styles=r(n.__emotion_styles))}const My=[];function zy(n){return My[0]=n,vl(My)}var nd={exports:{}},fe={};/**
 * @license React
 * react-is.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Dy;function Ax(){if(Dy)return fe;Dy=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),c=Symbol.for("react.consumer"),d=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),y=Symbol.for("react.memo"),S=Symbol.for("react.lazy"),T=Symbol.for("react.view_transition"),E=Symbol.for("react.client.reference");function x(C){if(typeof C=="object"&&C!==null){var D=C.$$typeof;switch(D){case n:switch(C=C.type,C){case i:case u:case l:case m:case h:case T:return C;default:switch(C=C&&C.$$typeof,C){case d:case p:case S:case y:return C;case c:return C;default:return D}}case r:return D}}}return fe.ContextConsumer=c,fe.ContextProvider=d,fe.Element=n,fe.ForwardRef=p,fe.Fragment=i,fe.Lazy=S,fe.Memo=y,fe.Portal=r,fe.Profiler=u,fe.StrictMode=l,fe.Suspense=m,fe.SuspenseList=h,fe.isContextConsumer=function(C){return x(C)===c},fe.isContextProvider=function(C){return x(C)===d},fe.isElement=function(C){return typeof C=="object"&&C!==null&&C.$$typeof===n},fe.isForwardRef=function(C){return x(C)===p},fe.isFragment=function(C){return x(C)===i},fe.isLazy=function(C){return x(C)===S},fe.isMemo=function(C){return x(C)===y},fe.isPortal=function(C){return x(C)===r},fe.isProfiler=function(C){return x(C)===u},fe.isStrictMode=function(C){return x(C)===l},fe.isSuspense=function(C){return x(C)===m},fe.isSuspenseList=function(C){return x(C)===h},fe.isValidElementType=function(C){return typeof C=="string"||typeof C=="function"||C===i||C===u||C===l||C===m||C===h||typeof C=="object"&&C!==null&&(C.$$typeof===S||C.$$typeof===y||C.$$typeof===d||C.$$typeof===c||C.$$typeof===p||C.$$typeof===E||C.getModuleId!==void 0)},fe.typeOf=x,fe}var Ny;function Mx(){return Ny||(Ny=1,nd.exports=Ax()),nd.exports}var t0=Mx();function aa(n){if(typeof n!="object"||n===null)return!1;const r=Object.getPrototypeOf(n);return(r===null||r===Object.prototype||Object.getPrototypeOf(r)===null)&&!(Symbol.toStringTag in n)&&!(Symbol.iterator in n)}function e0(n){if(R.isValidElement(n)||t0.isValidElementType(n)||!aa(n))return n;const r={};return Object.keys(n).forEach(i=>{r[i]=e0(n[i])}),r}function nn(n,r,i={clone:!0}){const l=i.clone?{...n}:n;return aa(n)&&aa(r)&&Object.keys(r).forEach(u=>{R.isValidElement(r[u])||t0.isValidElementType(r[u])?l[u]=r[u]:aa(r[u])&&Object.prototype.hasOwnProperty.call(n,u)&&aa(n[u])?l[u]=nn(n[u],r[u],i):i.clone?l[u]=aa(r[u])?e0(r[u]):r[u]:l[u]=r[u]}),l}const zx=n=>{const r=Object.keys(n).map(i=>({key:i,val:n[i]}))||[];return r.sort((i,l)=>i.val-l.val),r.reduce((i,l)=>({...i,[l.key]:l.val}),{})};function Dx(n){const{values:r={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:i="px",step:l=5,...u}=n,c=zx(r),d=Object.keys(c);function p(T){return`@media (min-width:${typeof r[T]=="number"?r[T]:T}${i})`}function m(T){return`@media (max-width:${(typeof r[T]=="number"?r[T]:T)-l/100}${i})`}function h(T,E){const x=d.indexOf(E);return`@media (min-width:${typeof r[T]=="number"?r[T]:T}${i}) and (max-width:${(x!==-1&&typeof r[d[x]]=="number"?r[d[x]]:E)-l/100}${i})`}function y(T){return d.indexOf(T)+1<d.length?h(T,d[d.indexOf(T)+1]):p(T)}function S(T){const E=d.indexOf(T);return E===0?p(d[1]):E===d.length-1?m(d[E]):h(T,d[d.indexOf(T)+1]).replace("@media","@media not all and")}return{keys:d,values:c,up:p,down:m,between:h,only:y,not:S,unit:i,...u}}function Nx(n,r){if(!n.containerQueries)return r;const i=Object.keys(r).filter(l=>l.startsWith("@container")).sort((l,u)=>{var d,p;const c=/min-width:\s*([0-9.]+)/;return+(((d=l.match(c))==null?void 0:d[1])||0)-+(((p=u.match(c))==null?void 0:p[1])||0)});return i.length?i.reduce((l,u)=>{const c=r[u];return delete l[u],l[u]=c,l},{...r}):r}function Bx(n,r){return r==="@"||r.startsWith("@")&&(n.some(i=>r.startsWith(`@${i}`))||!!r.match(/^@\d/))}function kx(n,r){const i=r.match(/^@([^/]+)?\/?(.+)?$/);if(!i)return null;const[,l,u]=i,c=Number.isNaN(+l)?l||0:+l;return n.containerQueries(u).up(c)}function $x(n){const r=(c,d)=>c.replace("@media",d?`@container ${d}`:"@container");function i(c,d){c.up=(...p)=>r(n.breakpoints.up(...p),d),c.down=(...p)=>r(n.breakpoints.down(...p),d),c.between=(...p)=>r(n.breakpoints.between(...p),d),c.only=(...p)=>r(n.breakpoints.only(...p),d),c.not=(...p)=>{const m=r(n.breakpoints.not(...p),d);return m.includes("not all and")?m.replace("not all and ","").replace("min-width:","width<").replace("max-width:","width>").replace("and","or"):m}}const l={},u=c=>(i(l,c),l);return i(u),{...n,containerQueries:u}}const jx={borderRadius:4};function rl(n,r){return r?nn(n,r,{clone:!1}):n}const xu={xs:0,sm:600,md:900,lg:1200,xl:1536},By={keys:["xs","sm","md","lg","xl"],up:n=>`@media (min-width:${xu[n]}px)`},_x={containerQueries:n=>({up:r=>{let i=typeof r=="number"?r:xu[r]||r;return typeof i=="number"&&(i=`${i}px`),n?`@container ${n} (min-width:${i})`:`@container (min-width:${i})`}})};function za(n,r,i){const l=n.theme||{};if(Array.isArray(r)){const c=l.breakpoints||By;return r.reduce((d,p,m)=>(d[c.up(c.keys[m])]=i(r[m]),d),{})}if(typeof r=="object"){const c=l.breakpoints||By;return Object.keys(r).reduce((d,p)=>{if(Bx(c.keys,p)){const m=kx(l.containerQueries?l:_x,p);m&&(d[m]=i(r[p],p))}else if(Object.keys(c.values||xu).includes(p)){const m=c.up(p);d[m]=i(r[p],p)}else{const m=p;d[m]=r[m]}return d},{})}return i(r)}function Ux(n={}){var i;return((i=n.keys)==null?void 0:i.reduce((l,u)=>{const c=n.up(u);return l[c]={},l},{}))||{}}function Lx(n,r){return n.reduce((i,l)=>{const u=i[l];return(!u||Object.keys(u).length===0)&&delete i[l],i},r)}function st(n){if(typeof n!="string")throw new Error(Ma(7));return n.charAt(0).toUpperCase()+n.slice(1)}function Cu(n,r,i=!0){if(!r||typeof r!="string")return null;if(n&&n.vars&&i){const l=`vars.${r}`.split(".").reduce((u,c)=>u&&u[c]?u[c]:null,n);if(l!=null)return l}return r.split(".").reduce((l,u)=>l&&l[u]!=null?l[u]:null,n)}function uu(n,r,i,l=i){let u;return typeof n=="function"?u=n(i):Array.isArray(n)?u=n[i]||l:u=Cu(n,i)||l,r&&(u=r(u,l,n)),u}function Be(n){const{prop:r,cssProperty:i=n.prop,themeKey:l,transform:u}=n,c=d=>{if(d[r]==null)return null;const p=d[r],m=d.theme,h=Cu(m,l)||{};return za(d,p,S=>{let T=uu(h,u,S);return S===T&&typeof S=="string"&&(T=uu(h,u,`${r}${S==="default"?"":st(S)}`,S)),i===!1?T:{[i]:T}})};return c.propTypes={},c.filterProps=[r],c}function Hx(n){const r={};return i=>(r[i]===void 0&&(r[i]=n(i)),r[i])}const Px={m:"margin",p:"padding"},qx={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},ky={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},Gx=Hx(n=>{if(n.length>2)if(ky[n])n=ky[n];else return[n];const[r,i]=n.split(""),l=Px[r],u=qx[i]||"";return Array.isArray(u)?u.map(c=>l+c):[l+u]}),Yd=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],Xd=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"];[...Yd,...Xd];function xl(n,r,i,l){const u=Cu(n,r,!0)??i;return typeof u=="number"||typeof u=="string"?c=>typeof c=="string"?c:typeof u=="string"?u.startsWith("var(")&&c===0?0:u.startsWith("var(")&&c===1?u:`calc(${c} * ${u})`:u*c:Array.isArray(u)?c=>{if(typeof c=="string")return c;const d=Math.abs(c),p=u[d];return c>=0?p:typeof p=="number"?-p:typeof p=="string"&&p.startsWith("var(")?`calc(-1 * ${p})`:`-${p}`}:typeof u=="function"?u:()=>{}}function Id(n){return xl(n,"spacing",8)}function Cl(n,r){return typeof r=="string"||r==null?r:n(r)}function Vx(n,r){return i=>n.reduce((l,u)=>(l[u]=Cl(r,i),l),{})}function Yx(n,r,i,l){if(!r.includes(i))return null;const u=Gx(i),c=Vx(u,l),d=n[i];return za(n,d,c)}function n0(n,r){const i=Id(n.theme);return Object.keys(n).map(l=>Yx(n,r,l,i)).reduce(rl,{})}function Ae(n){return n0(n,Yd)}Ae.propTypes={};Ae.filterProps=Yd;function Me(n){return n0(n,Xd)}Me.propTypes={};Me.filterProps=Xd;function a0(n=8,r=Id({spacing:n})){if(n.mui)return n;const i=(...l)=>(l.length===0?[1]:l).map(c=>{const d=r(c);return typeof d=="number"?`${d}px`:d}).join(" ");return i.mui=!0,i}function Tu(...n){const r=n.reduce((l,u)=>(u.filterProps.forEach(c=>{l[c]=u}),l),{}),i=l=>Object.keys(l).reduce((u,c)=>r[c]?rl(u,r[c](l)):u,{});return i.propTypes={},i.filterProps=n.reduce((l,u)=>l.concat(u.filterProps),[]),i}function Un(n){return typeof n!="number"?n:`${n}px solid`}function qn(n,r){return Be({prop:n,themeKey:"borders",transform:r})}const Xx=qn("border",Un),Ix=qn("borderTop",Un),Kx=qn("borderRight",Un),Qx=qn("borderBottom",Un),Wx=qn("borderLeft",Un),Zx=qn("borderColor"),Fx=qn("borderTopColor"),Jx=qn("borderRightColor"),t2=qn("borderBottomColor"),e2=qn("borderLeftColor"),n2=qn("outline",Un),a2=qn("outlineColor"),Eu=n=>{if(n.borderRadius!==void 0&&n.borderRadius!==null){const r=xl(n.theme,"shape.borderRadius",4),i=l=>({borderRadius:Cl(r,l)});return za(n,n.borderRadius,i)}return null};Eu.propTypes={};Eu.filterProps=["borderRadius"];Tu(Xx,Ix,Kx,Qx,Wx,Zx,Fx,Jx,t2,e2,Eu,n2,a2);const Ru=n=>{if(n.gap!==void 0&&n.gap!==null){const r=xl(n.theme,"spacing",8),i=l=>({gap:Cl(r,l)});return za(n,n.gap,i)}return null};Ru.propTypes={};Ru.filterProps=["gap"];const wu=n=>{if(n.columnGap!==void 0&&n.columnGap!==null){const r=xl(n.theme,"spacing",8),i=l=>({columnGap:Cl(r,l)});return za(n,n.columnGap,i)}return null};wu.propTypes={};wu.filterProps=["columnGap"];const Ou=n=>{if(n.rowGap!==void 0&&n.rowGap!==null){const r=xl(n.theme,"spacing",8),i=l=>({rowGap:Cl(r,l)});return za(n,n.rowGap,i)}return null};Ou.propTypes={};Ou.filterProps=["rowGap"];const r2=Be({prop:"gridColumn"}),o2=Be({prop:"gridRow"}),i2=Be({prop:"gridAutoFlow"}),l2=Be({prop:"gridAutoColumns"}),s2=Be({prop:"gridAutoRows"}),u2=Be({prop:"gridTemplateColumns"}),c2=Be({prop:"gridTemplateRows"}),f2=Be({prop:"gridTemplateAreas"}),d2=Be({prop:"gridArea"});Tu(Ru,wu,Ou,r2,o2,i2,l2,s2,u2,c2,f2,d2);function Do(n,r){return r==="grey"?r:n}const p2=Be({prop:"color",themeKey:"palette",transform:Do}),m2=Be({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:Do}),h2=Be({prop:"backgroundColor",themeKey:"palette",transform:Do});Tu(p2,m2,h2);function Tn(n){return n<=1&&n!==0?`${n*100}%`:n}const g2=Be({prop:"width",transform:Tn}),Kd=n=>{if(n.maxWidth!==void 0&&n.maxWidth!==null){const r=i=>{var u,c,d,p,m;const l=((d=(c=(u=n.theme)==null?void 0:u.breakpoints)==null?void 0:c.values)==null?void 0:d[i])||xu[i];return l?((m=(p=n.theme)==null?void 0:p.breakpoints)==null?void 0:m.unit)!=="px"?{maxWidth:`${l}${n.theme.breakpoints.unit}`}:{maxWidth:l}:{maxWidth:Tn(i)}};return za(n,n.maxWidth,r)}return null};Kd.filterProps=["maxWidth"];const y2=Be({prop:"minWidth",transform:Tn}),v2=Be({prop:"height",transform:Tn}),b2=Be({prop:"maxHeight",transform:Tn}),S2=Be({prop:"minHeight",transform:Tn});Be({prop:"size",cssProperty:"width",transform:Tn});Be({prop:"size",cssProperty:"height",transform:Tn});const x2=Be({prop:"boxSizing"});Tu(g2,Kd,y2,v2,b2,S2,x2);const Tl={border:{themeKey:"borders",transform:Un},borderTop:{themeKey:"borders",transform:Un},borderRight:{themeKey:"borders",transform:Un},borderBottom:{themeKey:"borders",transform:Un},borderLeft:{themeKey:"borders",transform:Un},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:Un},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:Eu},color:{themeKey:"palette",transform:Do},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:Do},backgroundColor:{themeKey:"palette",transform:Do},p:{style:Me},pt:{style:Me},pr:{style:Me},pb:{style:Me},pl:{style:Me},px:{style:Me},py:{style:Me},padding:{style:Me},paddingTop:{style:Me},paddingRight:{style:Me},paddingBottom:{style:Me},paddingLeft:{style:Me},paddingX:{style:Me},paddingY:{style:Me},paddingInline:{style:Me},paddingInlineStart:{style:Me},paddingInlineEnd:{style:Me},paddingBlock:{style:Me},paddingBlockStart:{style:Me},paddingBlockEnd:{style:Me},m:{style:Ae},mt:{style:Ae},mr:{style:Ae},mb:{style:Ae},ml:{style:Ae},mx:{style:Ae},my:{style:Ae},margin:{style:Ae},marginTop:{style:Ae},marginRight:{style:Ae},marginBottom:{style:Ae},marginLeft:{style:Ae},marginX:{style:Ae},marginY:{style:Ae},marginInline:{style:Ae},marginInlineStart:{style:Ae},marginInlineEnd:{style:Ae},marginBlock:{style:Ae},marginBlockStart:{style:Ae},marginBlockEnd:{style:Ae},displayPrint:{cssProperty:!1,transform:n=>({"@media print":{display:n}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:Ru},rowGap:{style:Ou},columnGap:{style:wu},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:Tn},maxWidth:{style:Kd},minWidth:{transform:Tn},height:{transform:Tn},maxHeight:{transform:Tn},minHeight:{transform:Tn},boxSizing:{},font:{themeKey:"font"},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}};function C2(...n){const r=n.reduce((l,u)=>l.concat(Object.keys(u)),[]),i=new Set(r);return n.every(l=>i.size===Object.keys(l).length)}function T2(n,r){return typeof n=="function"?n(r):n}function E2(){function n(i,l,u,c){const d={[i]:l,theme:u},p=c[i];if(!p)return{[i]:l};const{cssProperty:m=i,themeKey:h,transform:y,style:S}=p;if(l==null)return null;if(h==="typography"&&l==="inherit")return{[i]:l};const T=Cu(u,h)||{};return S?S(d):za(d,l,x=>{let C=uu(T,y,x);return x===C&&typeof x=="string"&&(C=uu(T,y,`${i}${x==="default"?"":st(x)}`,x)),m===!1?C:{[m]:C}})}function r(i){const{sx:l,theme:u={}}=i||{};if(!l)return null;const c=u.unstable_sxConfig??Tl;function d(p){let m=p;if(typeof p=="function")m=p(u);else if(typeof p!="object")return p;if(!m)return null;const h=Ux(u.breakpoints),y=Object.keys(h);let S=h;return Object.keys(m).forEach(T=>{const E=T2(m[T],u);if(E!=null)if(typeof E=="object")if(c[T])S=rl(S,n(T,E,u,c));else{const x=za({theme:u},E,C=>({[T]:C}));C2(x,E)?S[T]=r({sx:E,theme:u}):S=rl(S,x)}else S=rl(S,n(T,E,u,c))}),Nx(u,Lx(y,S))}return Array.isArray(l)?l.map(d):d(l)}return r}const ar=E2();ar.filterProps=["sx"];function R2(n,r){var l;const i=this;if(i.vars){if(!((l=i.colorSchemes)!=null&&l[n])||typeof i.getColorSchemeSelector!="function")return{};let u=i.getColorSchemeSelector(n);return u==="&"?r:((u.includes("data-")||u.includes("."))&&(u=`*:where(${u.replace(/\s*&$/,"")}) &`),{[u]:r})}return i.palette.mode===n?r:{}}function El(n={},...r){const{breakpoints:i={},palette:l={},spacing:u,shape:c={},...d}=n,p=Dx(i),m=a0(u);let h=nn({breakpoints:p,direction:"ltr",components:{},palette:{mode:"light",...l},spacing:m,shape:{...jx,...c}},d);return h=$x(h),h.applyStyles=R2,h=r.reduce((y,S)=>nn(y,S),h),h.unstable_sxConfig={...Tl,...d==null?void 0:d.unstable_sxConfig},h.unstable_sx=function(S){return ar({sx:S,theme:this})},h}function w2(n){return Object.keys(n).length===0}function r0(n=null){const r=R.useContext(bl);return!r||w2(r)?n:r}const O2=El();function Rl(n=O2){return r0(n)}function A2({styles:n,themeId:r,defaultTheme:i={}}){const l=Rl(i),u=typeof n=="function"?n(r&&l[r]||l):n;return O.jsx(Fv,{styles:u})}const M2=n=>{var l;const r={systemProps:{},otherProps:{}},i=((l=n==null?void 0:n.theme)==null?void 0:l.unstable_sxConfig)??Tl;return Object.keys(n).forEach(u=>{i[u]?r.systemProps[u]=n[u]:r.otherProps[u]=n[u]}),r};function Qd(n){const{sx:r,...i}=n,{systemProps:l,otherProps:u}=M2(i);let c;return Array.isArray(r)?c=[l,...r]:typeof r=="function"?c=(...d)=>{const p=r(...d);return aa(p)?{...l,...p}:l}:c={...l,...r},{...u,sx:c}}const $y=n=>n,z2=()=>{let n=$y;return{configure(r){n=r},generate(r){return n(r)},reset(){n=$y}}},o0=z2();function i0(n){var r,i,l="";if(typeof n=="string"||typeof n=="number")l+=n;else if(typeof n=="object")if(Array.isArray(n)){var u=n.length;for(r=0;r<u;r++)n[r]&&(i=i0(n[r]))&&(l&&(l+=" "),l+=i)}else for(i in n)n[i]&&(l&&(l+=" "),l+=i);return l}function gt(){for(var n,r,i=0,l="",u=arguments.length;i<u;i++)(n=arguments[i])&&(r=i0(n))&&(l&&(l+=" "),l+=r);return l}function D2(n={}){const{themeId:r,defaultTheme:i,defaultClassName:l="MuiBox-root",generateClassName:u}=n,c=Jv("div",{shouldForwardProp:p=>p!=="theme"&&p!=="sx"&&p!=="as"})(ar);return R.forwardRef(function(m,h){const y=Rl(i),{className:S,component:T="div",...E}=Qd(m);return O.jsx(c,{as:T,ref:h,className:gt(S,u?u(l):l),theme:r&&y[r]||y,...E})})}const N2={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function Dt(n,r,i="Mui"){const l=N2[r];return l?`${i}-${l}`:`${o0.generate(n)}-${r}`}function Mt(n,r,i="Mui"){const l={};return r.forEach(u=>{l[u]=Dt(n,u,i)}),l}function l0(n){const{variants:r,...i}=n,l={variants:r,style:zy(i),isProcessed:!0};return l.style===i||r&&r.forEach(u=>{typeof u.style!="function"&&(u.style=zy(u.style))}),l}const B2=El();function ad(n){return n!=="ownerState"&&n!=="theme"&&n!=="sx"&&n!=="as"}function k2(n){return n?(r,i)=>i[n]:null}function $2(n,r,i){n.theme=_2(n.theme)?i:n.theme[r]||n.theme}function au(n,r){const i=typeof r=="function"?r(n):r;if(Array.isArray(i))return i.flatMap(l=>au(n,l));if(Array.isArray(i==null?void 0:i.variants)){let l;if(i.isProcessed)l=i.style;else{const{variants:u,...c}=i;l=c}return s0(n,i.variants,[l])}return i!=null&&i.isProcessed?i.style:i}function s0(n,r,i=[]){var u;let l;t:for(let c=0;c<r.length;c+=1){const d=r[c];if(typeof d.props=="function"){if(l??(l={...n,...n.ownerState,ownerState:n.ownerState}),!d.props(l))continue}else for(const p in d.props)if(n[p]!==d.props[p]&&((u=n.ownerState)==null?void 0:u[p])!==d.props[p])continue t;typeof d.style=="function"?(l??(l={...n,...n.ownerState,ownerState:n.ownerState}),i.push(d.style(l))):i.push(d.style)}return i}function u0(n={}){const{themeId:r,defaultTheme:i=B2,rootShouldForwardProp:l=ad,slotShouldForwardProp:u=ad}=n;function c(p){$2(p,r,i)}return(p,m={})=>{Ox(p,w=>w.filter(j=>j!==ar));const{name:h,slot:y,skipVariantsResolver:S,skipSx:T,overridesResolver:E=k2(L2(y)),...x}=m,C=S!==void 0?S:y&&y!=="Root"&&y!=="root"||!1,D=T||!1;let k=ad;y==="Root"||y==="root"?k=l:y?k=u:U2(p)&&(k=void 0);const U=Jv(p,{shouldForwardProp:k,label:j2(),...x}),M=w=>{if(typeof w=="function"&&w.__emotion_real!==w)return function(H){return au(H,w)};if(aa(w)){const j=l0(w);return j.variants?function(q){return au(q,j)}:j.style}return w},A=(...w)=>{const j=[],H=w.map(M),q=[];if(j.push(c),h&&E&&q.push(function(K){var N,V;const it=(V=(N=K.theme.components)==null?void 0:N[h])==null?void 0:V.styleOverrides;if(!it)return null;const F={};for(const ot in it)F[ot]=au(K,it[ot]);return E(K,F)}),h&&!C&&q.push(function(K){var F,N;const Z=K.theme,it=(N=(F=Z==null?void 0:Z.components)==null?void 0:F[h])==null?void 0:N.variants;return it?s0(K,it):null}),D||q.push(ar),Array.isArray(H[0])){const _=H.shift(),K=new Array(j.length).fill(""),Z=new Array(q.length).fill("");let it;it=[...K,..._,...Z],it.raw=[...K,..._.raw,...Z],j.unshift(it)}const Q=[...j,...H,...q],v=U(...Q);return p.muiName&&(v.muiName=p.muiName),v};return U.withConfig&&(A.withConfig=U.withConfig),A}}function j2(n,r){return void 0}function _2(n){for(const r in n)return!1;return!0}function U2(n){return typeof n=="string"&&n.charCodeAt(0)>96}function L2(n){return n&&n.charAt(0).toLowerCase()+n.slice(1)}const c0=u0();function pl(n,r){const i={...r};for(const l in n)if(Object.prototype.hasOwnProperty.call(n,l)){const u=l;if(u==="components"||u==="slots")i[u]={...n[u],...i[u]};else if(u==="componentsProps"||u==="slotProps"){const c=n[u],d=r[u];if(!d)i[u]=c||{};else if(!c)i[u]=d;else{i[u]={...d};for(const p in c)if(Object.prototype.hasOwnProperty.call(c,p)){const m=p;i[u][m]=pl(c[m],d[m])}}}else i[u]===void 0&&(i[u]=n[u])}return i}function H2(n){const{theme:r,name:i,props:l}=n;return!r||!r.components||!r.components[i]||!r.components[i].defaultProps?l:pl(r.components[i].defaultProps,l)}function f0({props:n,name:r,defaultTheme:i,themeId:l}){let u=Rl(i);return l&&(u=u[l]||u),H2({theme:u,name:r,props:n})}const In=typeof window<"u"?R.useLayoutEffect:R.useEffect;function P2(n,r=Number.MIN_SAFE_INTEGER,i=Number.MAX_SAFE_INTEGER){return Math.max(r,Math.min(n,i))}function Wd(n,r=0,i=1){return P2(n,r,i)}function q2(n){n=n.slice(1);const r=new RegExp(`.{1,${n.length>=6?2:1}}`,"g");let i=n.match(r);return i&&i[0].length===1&&(i=i.map(l=>l+l)),i?`rgb${i.length===4?"a":""}(${i.map((l,u)=>u<3?parseInt(l,16):Math.round(parseInt(l,16)/255*1e3)/1e3).join(", ")})`:""}function rr(n){if(n.type)return n;if(n.charAt(0)==="#")return rr(q2(n));const r=n.indexOf("("),i=n.substring(0,r);if(!["rgb","rgba","hsl","hsla","color"].includes(i))throw new Error(Ma(9,n));let l=n.substring(r+1,n.length-1),u;if(i==="color"){if(l=l.split(" "),u=l.shift(),l.length===4&&l[3].charAt(0)==="/"&&(l[3]=l[3].slice(1)),!["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].includes(u))throw new Error(Ma(10,u))}else l=l.split(",");return l=l.map(c=>parseFloat(c)),{type:i,values:l,colorSpace:u}}const G2=n=>{const r=rr(n);return r.values.slice(0,3).map((i,l)=>r.type.includes("hsl")&&l!==0?`${i}%`:i).join(" ")},tl=(n,r)=>{try{return G2(n)}catch{return n}};function Au(n){const{type:r,colorSpace:i}=n;let{values:l}=n;return r.includes("rgb")?l=l.map((u,c)=>c<3?parseInt(u,10):u):r.includes("hsl")&&(l[1]=`${l[1]}%`,l[2]=`${l[2]}%`),r.includes("color")?l=`${i} ${l.join(" ")}`:l=`${l.join(", ")}`,`${r}(${l})`}function d0(n){n=rr(n);const{values:r}=n,i=r[0],l=r[1]/100,u=r[2]/100,c=l*Math.min(u,1-u),d=(h,y=(h+i/30)%12)=>u-c*Math.max(Math.min(y-3,9-y,1),-1);let p="rgb";const m=[Math.round(d(0)*255),Math.round(d(8)*255),Math.round(d(4)*255)];return n.type==="hsla"&&(p+="a",m.push(r[3])),Au({type:p,values:m})}function yd(n){n=rr(n);let r=n.type==="hsl"||n.type==="hsla"?rr(d0(n)).values:n.values;return r=r.map(i=>(n.type!=="color"&&(i/=255),i<=.03928?i/12.92:((i+.055)/1.055)**2.4)),Number((.2126*r[0]+.7152*r[1]+.0722*r[2]).toFixed(3))}function V2(n,r){const i=yd(n),l=yd(r);return(Math.max(i,l)+.05)/(Math.min(i,l)+.05)}function ve(n,r){return n=rr(n),r=Wd(r),(n.type==="rgb"||n.type==="hsl")&&(n.type+="a"),n.type==="color"?n.values[3]=`/${r}`:n.values[3]=r,Au(n)}function Vs(n,r,i){try{return ve(n,r)}catch{return n}}function Zd(n,r){if(n=rr(n),r=Wd(r),n.type.includes("hsl"))n.values[2]*=1-r;else if(n.type.includes("rgb")||n.type.includes("color"))for(let i=0;i<3;i+=1)n.values[i]*=1-r;return Au(n)}function me(n,r,i){try{return Zd(n,r)}catch{return n}}function Fd(n,r){if(n=rr(n),r=Wd(r),n.type.includes("hsl"))n.values[2]+=(100-n.values[2])*r;else if(n.type.includes("rgb"))for(let i=0;i<3;i+=1)n.values[i]+=(255-n.values[i])*r;else if(n.type.includes("color"))for(let i=0;i<3;i+=1)n.values[i]+=(1-n.values[i])*r;return Au(n)}function he(n,r,i){try{return Fd(n,r)}catch{return n}}function Y2(n,r=.15){return yd(n)>.5?Zd(n,r):Fd(n,r)}function Ys(n,r,i){try{return Y2(n,r)}catch{return n}}const p0=R.createContext(null);function Jd(){return R.useContext(p0)}const X2=typeof Symbol=="function"&&Symbol.for,I2=X2?Symbol.for("mui.nested"):"__THEME_NESTED__";function K2(n,r){return typeof r=="function"?r(n):{...n,...r}}function Q2(n){const{children:r,theme:i}=n,l=Jd(),u=R.useMemo(()=>{const c=l===null?{...i}:K2(l,i);return c!=null&&(c[I2]=l!==null),c},[i,l]);return O.jsx(p0.Provider,{value:u,children:r})}const m0=R.createContext();function W2({value:n,...r}){return O.jsx(m0.Provider,{value:n??!0,...r})}const tp=()=>R.useContext(m0)??!1,h0=R.createContext(void 0);function Z2({value:n,children:r}){return O.jsx(h0.Provider,{value:n,children:r})}function F2(n){const{theme:r,name:i,props:l}=n;if(!r||!r.components||!r.components[i])return l;const u=r.components[i];return u.defaultProps?pl(u.defaultProps,l):!u.styleOverrides&&!u.variants?pl(u,l):l}function J2({props:n,name:r}){const i=R.useContext(h0);return F2({props:n,name:r,theme:{components:i}})}const jy={};function _y(n,r,i,l=!1){return R.useMemo(()=>{const u=n&&r[n]||r;if(typeof i=="function"){const c=i(u),d=n?{...r,[n]:c}:c;return l?()=>d:d}return n?{...r,[n]:i}:{...r,...i}},[n,r,i,l])}function g0(n){const{children:r,theme:i,themeId:l}=n,u=r0(jy),c=Jd()||jy,d=_y(l,u,i),p=_y(l,c,i,!0),m=(l?d[l]:d).direction==="rtl";return O.jsx(Q2,{theme:p,children:O.jsx(bl.Provider,{value:d,children:O.jsx(W2,{value:m,children:O.jsx(Z2,{value:l?d[l].components:d.components,children:r})})})})}const Uy={theme:void 0};function tC(n){let r,i;return function(u){let c=r;return(c===void 0||u.theme!==i)&&(Uy.theme=u.theme,c=l0(n(Uy)),r=c,i=u.theme),c}}const ep="mode",np="color-scheme",eC="data-color-scheme";function nC(n){const{defaultMode:r="system",defaultLightColorScheme:i="light",defaultDarkColorScheme:l="dark",modeStorageKey:u=ep,colorSchemeStorageKey:c=np,attribute:d=eC,colorSchemeNode:p="document.documentElement",nonce:m}=n||{};let h="",y=d;if(d==="class"&&(y=".%s"),d==="data"&&(y="[data-%s]"),y.startsWith(".")){const T=y.substring(1);h+=`${p}.classList.remove('${T}'.replace('%s', light), '${T}'.replace('%s', dark));
      ${p}.classList.add('${T}'.replace('%s', colorScheme));`}const S=y.match(/\[([^\]]+)\]/);if(S){const[T,E]=S[1].split("=");E||(h+=`${p}.removeAttribute('${T}'.replace('%s', light));
      ${p}.removeAttribute('${T}'.replace('%s', dark));`),h+=`
      ${p}.setAttribute('${T}'.replace('%s', colorScheme), ${E?`${E}.replace('%s', colorScheme)`:'""'});`}else h+=`${p}.setAttribute('${y}', colorScheme);`;return O.jsx("script",{suppressHydrationWarning:!0,nonce:typeof window>"u"?m:"",dangerouslySetInnerHTML:{__html:`(function() {
try {
  let colorScheme = '';
  const mode = localStorage.getItem('${u}') || '${r}';
  const dark = localStorage.getItem('${c}-dark') || '${l}';
  const light = localStorage.getItem('${c}-light') || '${i}';
  if (mode === 'system') {
    // handle system mode
    const mql = window.matchMedia('(prefers-color-scheme: dark)');
    if (mql.matches) {
      colorScheme = dark
    } else {
      colorScheme = light
    }
  }
  if (mode === 'light') {
    colorScheme = light;
  }
  if (mode === 'dark') {
    colorScheme = dark;
  }
  if (colorScheme) {
    ${h}
  }
} catch(e){}})();`}},"mui-color-scheme-init")}function aC(){}const rC=({key:n,storageWindow:r})=>(!r&&typeof window<"u"&&(r=window),{get(i){if(typeof window>"u")return;if(!r)return i;let l;try{l=r.localStorage.getItem(n)}catch{}return l||i},set:i=>{if(r)try{r.localStorage.setItem(n,i)}catch{}},subscribe:i=>{if(!r)return aC;const l=u=>{const c=u.newValue;u.key===n&&i(c)};return r.addEventListener("storage",l),()=>{r.removeEventListener("storage",l)}}});function rd(){}function Ly(n){if(typeof window<"u"&&typeof window.matchMedia=="function"&&n==="system")return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}function y0(n,r){if(n.mode==="light"||n.mode==="system"&&n.systemMode==="light")return r("light");if(n.mode==="dark"||n.mode==="system"&&n.systemMode==="dark")return r("dark")}function oC(n){return y0(n,r=>{if(r==="light")return n.lightColorScheme;if(r==="dark")return n.darkColorScheme})}function iC(n){const{defaultMode:r="light",defaultLightColorScheme:i,defaultDarkColorScheme:l,supportedColorSchemes:u=[],modeStorageKey:c=ep,colorSchemeStorageKey:d=np,storageWindow:p=typeof window>"u"?void 0:window,storageManager:m=rC,noSsr:h=!1}=n,y=u.join(","),S=u.length>1,T=R.useMemo(()=>m==null?void 0:m({key:c,storageWindow:p}),[m,c,p]),E=R.useMemo(()=>m==null?void 0:m({key:`${d}-light`,storageWindow:p}),[m,d,p]),x=R.useMemo(()=>m==null?void 0:m({key:`${d}-dark`,storageWindow:p}),[m,d,p]),[C,D]=R.useState(()=>{const q=(T==null?void 0:T.get(r))||r,Q=(E==null?void 0:E.get(i))||i,v=(x==null?void 0:x.get(l))||l;return{mode:q,systemMode:Ly(q),lightColorScheme:Q,darkColorScheme:v}}),[k,U]=R.useState(h||!S);R.useEffect(()=>{U(!0)},[]);const M=oC(C),A=R.useCallback(q=>{D(Q=>{if(q===Q.mode)return Q;const v=q??r;return T==null||T.set(v),{...Q,mode:v,systemMode:Ly(v)}})},[T,r]),w=R.useCallback(q=>{q?typeof q=="string"?q&&!y.includes(q)?console.error(`\`${q}\` does not exist in \`theme.colorSchemes\`.`):D(Q=>{const v={...Q};return y0(Q,_=>{_==="light"&&(E==null||E.set(q),v.lightColorScheme=q),_==="dark"&&(x==null||x.set(q),v.darkColorScheme=q)}),v}):D(Q=>{const v={...Q},_=q.light===null?i:q.light,K=q.dark===null?l:q.dark;return _&&(y.includes(_)?(v.lightColorScheme=_,E==null||E.set(_)):console.error(`\`${_}\` does not exist in \`theme.colorSchemes\`.`)),K&&(y.includes(K)?(v.darkColorScheme=K,x==null||x.set(K)):console.error(`\`${K}\` does not exist in \`theme.colorSchemes\`.`)),v}):D(Q=>(E==null||E.set(i),x==null||x.set(l),{...Q,lightColorScheme:i,darkColorScheme:l}))},[y,E,x,i,l]),j=R.useCallback(q=>{C.mode==="system"&&D(Q=>{const v=q!=null&&q.matches?"dark":"light";return Q.systemMode===v?Q:{...Q,systemMode:v}})},[C.mode]),H=R.useRef(j);return H.current=j,R.useEffect(()=>{if(typeof window.matchMedia!="function"||!S)return;const q=(...v)=>H.current(...v),Q=window.matchMedia("(prefers-color-scheme: dark)");return Q.addListener(q),q(Q),()=>{Q.removeListener(q)}},[S]),R.useEffect(()=>{if(S){const q=(T==null?void 0:T.subscribe(_=>{(!_||["light","dark","system"].includes(_))&&A(_||r)}))||rd,Q=(E==null?void 0:E.subscribe(_=>{(!_||y.match(_))&&w({light:_})}))||rd,v=(x==null?void 0:x.subscribe(_=>{(!_||y.match(_))&&w({dark:_})}))||rd;return()=>{q(),Q(),v()}}},[w,A,y,r,p,S,T,E,x]),{...C,mode:k?C.mode:void 0,systemMode:k?C.systemMode:void 0,colorScheme:k?M:void 0,setMode:A,setColorScheme:w}}const lC="*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function sC(n){const{themeId:r,theme:i={},modeStorageKey:l=ep,colorSchemeStorageKey:u=np,disableTransitionOnChange:c=!1,defaultColorScheme:d,resolveTheme:p}=n,m={allColorSchemes:[],colorScheme:void 0,darkColorScheme:void 0,lightColorScheme:void 0,mode:void 0,setColorScheme:()=>{},setMode:()=>{},systemMode:void 0},h=R.createContext(void 0),y=()=>R.useContext(h)||m,S={},T={};function E(k){var Ue,ee,Te,ne;const{children:U,theme:M,modeStorageKey:A=l,colorSchemeStorageKey:w=u,disableTransitionOnChange:j=c,storageManager:H,storageWindow:q=typeof window>"u"?void 0:window,documentNode:Q=typeof document>"u"?void 0:document,colorSchemeNode:v=typeof document>"u"?void 0:document.documentElement,disableNestedContext:_=!1,disableStyleSheetGeneration:K=!1,defaultMode:Z="system",forceThemeRerender:it=!1,noSsr:F}=k,N=R.useRef(!1),V=Jd(),ot=R.useContext(h),W=!!ot&&!_,z=R.useMemo(()=>M||(typeof i=="function"?i():i),[M]),X=z[r],et=X||z,{colorSchemes:nt=S,components:lt=T,cssVarPrefix:ut}=et,ft=Object.keys(nt).filter(le=>!!nt[le]).join(","),Tt=R.useMemo(()=>ft.split(","),[ft]),bt=typeof d=="string"?d:d.light,Ot=typeof d=="string"?d:d.dark,yt=nt[bt]&&nt[Ot]?Z:((ee=(Ue=nt[et.defaultColorScheme])==null?void 0:Ue.palette)==null?void 0:ee.mode)||((Te=et.palette)==null?void 0:Te.mode),{mode:wt,setMode:St,systemMode:$t,lightColorScheme:Et,darkColorScheme:qt,colorScheme:Ce,setColorScheme:Ut}=iC({supportedColorSchemes:Tt,defaultLightColorScheme:bt,defaultDarkColorScheme:Ot,modeStorageKey:A,colorSchemeStorageKey:w,defaultMode:yt,storageManager:H,storageWindow:q,noSsr:F});let Kt=wt,Qt=Ce;W&&(Kt=ot.mode,Qt=ot.colorScheme);let Vt=Qt||et.defaultColorScheme;et.vars&&!it&&(Vt=et.defaultColorScheme);const jt=R.useMemo(()=>{var Re;const le=((Re=et.generateThemeVars)==null?void 0:Re.call(et))||et.vars,mt={...et,components:lt,colorSchemes:nt,cssVarPrefix:ut,vars:le};if(typeof mt.generateSpacing=="function"&&(mt.spacing=mt.generateSpacing()),Vt){const ae=nt[Vt];ae&&typeof ae=="object"&&Object.keys(ae).forEach(Wt=>{ae[Wt]&&typeof ae[Wt]=="object"?mt[Wt]={...mt[Wt],...ae[Wt]}:mt[Wt]=ae[Wt]})}return p?p(mt):mt},[et,Vt,lt,nt,ut]),dt=et.colorSchemeSelector;In(()=>{if(Qt&&v&&dt&&dt!=="media"){const le=dt;let mt=dt;if(le==="class"&&(mt=".%s"),le==="data"&&(mt="[data-%s]"),le!=null&&le.startsWith("data-")&&!le.includes("%s")&&(mt=`[${le}="%s"]`),mt.startsWith("."))v.classList.remove(...Tt.map(Re=>mt.substring(1).replace("%s",Re))),v.classList.add(mt.substring(1).replace("%s",Qt));else{const Re=mt.replace("%s",Qt).match(/\[([^\]]+)\]/);if(Re){const[ae,Wt]=Re[1].split("=");Wt||Tt.forEach(ht=>{v.removeAttribute(ae.replace(Qt,ht))}),v.setAttribute(ae,Wt?Wt.replace(/"|'/g,""):"")}else v.setAttribute(mt,Qt)}}},[Qt,dt,v,Tt]),R.useEffect(()=>{let le;if(j&&N.current&&Q){const mt=Q.createElement("style");mt.appendChild(Q.createTextNode(lC)),Q.head.appendChild(mt),window.getComputedStyle(Q.body),le=setTimeout(()=>{Q.head.removeChild(mt)},1)}return()=>{clearTimeout(le)}},[Qt,j,Q]),R.useEffect(()=>(N.current=!0,()=>{N.current=!1}),[]);const _e=R.useMemo(()=>({allColorSchemes:Tt,colorScheme:Qt,darkColorScheme:qt,lightColorScheme:Et,mode:Kt,setColorScheme:Ut,setMode:St,systemMode:$t}),[Tt,Qt,qt,Et,Kt,Ut,St,$t,jt.colorSchemeSelector]);let ie=!0;(K||et.cssVariables===!1||W&&(V==null?void 0:V.cssVarPrefix)===ut)&&(ie=!1);const Ze=O.jsxs(R.Fragment,{children:[O.jsx(g0,{themeId:X?r:void 0,theme:jt,children:U}),ie&&O.jsx(Fv,{styles:((ne=jt.generateStyleSheets)==null?void 0:ne.call(jt))||[]})]});return W?Ze:O.jsx(h.Provider,{value:_e,children:Ze})}const x=typeof d=="string"?d:d.light,C=typeof d=="string"?d:d.dark;return{CssVarsProvider:E,useColorScheme:y,getInitColorSchemeScript:k=>nC({colorSchemeStorageKey:u,defaultLightColorScheme:x,defaultDarkColorScheme:C,modeStorageKey:l,...k})}}function uC(n=""){function r(...l){if(!l.length)return"";const u=l[0];return typeof u=="string"&&!u.match(/(#|\(|\)|(-?(\d*\.)?\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\d*\.)?\d+)$|(\d+ \d+ \d+)/)?`, var(--${n?`${n}-`:""}${u}${r(...l.slice(1))})`:`, ${u}`}return(l,...u)=>`var(--${n?`${n}-`:""}${l}${r(...u)})`}const Hy=(n,r,i,l=[])=>{let u=n;r.forEach((c,d)=>{d===r.length-1?Array.isArray(u)?u[Number(c)]=i:u&&typeof u=="object"&&(u[c]=i):u&&typeof u=="object"&&(u[c]||(u[c]=l.includes(c)?[]:{}),u=u[c])})},cC=(n,r,i)=>{function l(u,c=[],d=[]){Object.entries(u).forEach(([p,m])=>{(!i||i&&!i([...c,p]))&&m!=null&&(typeof m=="object"&&Object.keys(m).length>0?l(m,[...c,p],Array.isArray(m)?[...d,p]:d):r([...c,p],m,d))})}l(n)},fC=(n,r)=>typeof r=="number"?["lineHeight","fontWeight","opacity","zIndex"].some(l=>n.includes(l))||n[n.length-1].toLowerCase().includes("opacity")?r:`${r}px`:r;function od(n,r){const{prefix:i,shouldSkipGeneratingVar:l}=r||{},u={},c={},d={};return cC(n,(p,m,h)=>{if((typeof m=="string"||typeof m=="number")&&(!l||!l(p,m))){const y=`--${i?`${i}-`:""}${p.join("-")}`,S=fC(p,m);Object.assign(u,{[y]:S}),Hy(c,p,`var(${y})`,h),Hy(d,p,`var(${y}, ${S})`,h)}},p=>p[0]==="vars"),{css:u,vars:c,varsWithDefaults:d}}function dC(n,r={}){const{getSelector:i=D,disableCssColorScheme:l,colorSchemeSelector:u}=r,{colorSchemes:c={},components:d,defaultColorScheme:p="light",...m}=n,{vars:h,css:y,varsWithDefaults:S}=od(m,r);let T=S;const E={},{[p]:x,...C}=c;if(Object.entries(C||{}).forEach(([M,A])=>{const{vars:w,css:j,varsWithDefaults:H}=od(A,r);T=nn(T,H),E[M]={css:j,vars:w}}),x){const{css:M,vars:A,varsWithDefaults:w}=od(x,r);T=nn(T,w),E[p]={css:M,vars:A}}function D(M,A){var j,H;let w=u;if(u==="class"&&(w=".%s"),u==="data"&&(w="[data-%s]"),u!=null&&u.startsWith("data-")&&!u.includes("%s")&&(w=`[${u}="%s"]`),M){if(w==="media")return n.defaultColorScheme===M?":root":{[`@media (prefers-color-scheme: ${((H=(j=c[M])==null?void 0:j.palette)==null?void 0:H.mode)||M})`]:{":root":A}};if(w)return n.defaultColorScheme===M?`:root, ${w.replace("%s",String(M))}`:w.replace("%s",String(M))}return":root"}return{vars:T,generateThemeVars:()=>{let M={...h};return Object.entries(E).forEach(([,{vars:A}])=>{M=nn(M,A)}),M},generateStyleSheets:()=>{var q,Q;const M=[],A=n.defaultColorScheme||"light";function w(v,_){Object.keys(_).length&&M.push(typeof v=="string"?{[v]:{..._}}:v)}w(i(void 0,{...y}),y);const{[A]:j,...H}=E;if(j){const{css:v}=j,_=(Q=(q=c[A])==null?void 0:q.palette)==null?void 0:Q.mode,K=!l&&_?{colorScheme:_,...v}:{...v};w(i(A,{...K}),K)}return Object.entries(H).forEach(([v,{css:_}])=>{var it,F;const K=(F=(it=c[v])==null?void 0:it.palette)==null?void 0:F.mode,Z=!l&&K?{colorScheme:K,..._}:{..._};w(i(v,{...Z}),Z)}),M}}}function pC(n){return function(i){return n==="media"?`@media (prefers-color-scheme: ${i})`:n?n.startsWith("data-")&&!n.includes("%s")?`[${n}="${i}"] &`:n==="class"?`.${i} &`:n==="data"?`[data-${i}] &`:`${n.replace("%s",i)} &`:"&"}}function Nt(n,r,i=void 0){const l={};for(const u in n){const c=n[u];let d="",p=!0;for(let m=0;m<c.length;m+=1){const h=c[m];h&&(d+=(p===!0?"":" ")+r(h),p=!1,i&&i[h]&&(d+=" "+i[h]))}l[u]=d}return l}const mC=El(),hC=c0("div",{name:"MuiContainer",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.root,r[`maxWidth${st(String(i.maxWidth))}`],i.fixed&&r.fixed,i.disableGutters&&r.disableGutters]}}),gC=n=>f0({props:n,name:"MuiContainer",defaultTheme:mC}),yC=(n,r)=>{const i=m=>Dt(r,m),{classes:l,fixed:u,disableGutters:c,maxWidth:d}=n,p={root:["root",d&&`maxWidth${st(String(d))}`,u&&"fixed",c&&"disableGutters"]};return Nt(p,i,l)};function vC(n={}){const{createStyledComponent:r=hC,useThemeProps:i=gC,componentName:l="MuiContainer"}=n,u=r(({theme:d,ownerState:p})=>({width:"100%",marginLeft:"auto",boxSizing:"border-box",marginRight:"auto",...!p.disableGutters&&{paddingLeft:d.spacing(2),paddingRight:d.spacing(2),[d.breakpoints.up("sm")]:{paddingLeft:d.spacing(3),paddingRight:d.spacing(3)}}}),({theme:d,ownerState:p})=>p.fixed&&Object.keys(d.breakpoints.values).reduce((m,h)=>{const y=h,S=d.breakpoints.values[y];return S!==0&&(m[d.breakpoints.up(y)]={maxWidth:`${S}${d.breakpoints.unit}`}),m},{}),({theme:d,ownerState:p})=>({...p.maxWidth==="xs"&&{[d.breakpoints.up("xs")]:{maxWidth:Math.max(d.breakpoints.values.xs,444)}},...p.maxWidth&&p.maxWidth!=="xs"&&{[d.breakpoints.up(p.maxWidth)]:{maxWidth:`${d.breakpoints.values[p.maxWidth]}${d.breakpoints.unit}`}}}));return R.forwardRef(function(p,m){const h=i(p),{className:y,component:S="div",disableGutters:T=!1,fixed:E=!1,maxWidth:x="lg",classes:C,...D}=h,k={...h,component:S,disableGutters:T,fixed:E,maxWidth:x},U=yC(k,l);return O.jsx(u,{as:S,ownerState:k,className:gt(U.root,y),ref:m,...D})})}function ru(n,r){var i,l,u;return R.isValidElement(n)&&r.indexOf(n.type.muiName??((u=(l=(i=n.type)==null?void 0:i._payload)==null?void 0:l.value)==null?void 0:u.muiName))!==-1}const bC=(n,r)=>n.filter(i=>r.includes(i)),Po=(n,r,i)=>{const l=n.keys[0];Array.isArray(r)?r.forEach((u,c)=>{i((d,p)=>{c<=n.keys.length-1&&(c===0?Object.assign(d,p):d[n.up(n.keys[c])]=p)},u)}):r&&typeof r=="object"?(Object.keys(r).length>n.keys.length?n.keys:bC(n.keys,Object.keys(r))).forEach(c=>{if(n.keys.includes(c)){const d=r[c];d!==void 0&&i((p,m)=>{l===c?Object.assign(p,m):p[n.up(c)]=m},d)}}):(typeof r=="number"||typeof r=="string")&&i((u,c)=>{Object.assign(u,c)},r)};function cu(n){return`--Grid-${n}Spacing`}function Mu(n){return`--Grid-parent-${n}Spacing`}const Py="--Grid-columns",No="--Grid-parent-columns",SC=({theme:n,ownerState:r})=>{const i={};return Po(n.breakpoints,r.size,(l,u)=>{let c={};u==="grow"&&(c={flexBasis:0,flexGrow:1,maxWidth:"100%"}),u==="auto"&&(c={flexBasis:"auto",flexGrow:0,flexShrink:0,maxWidth:"none",width:"auto"}),typeof u=="number"&&(c={flexGrow:0,flexBasis:"auto",width:`calc(100% * ${u} / var(${No}) - (var(${No}) - ${u}) * (var(${Mu("column")}) / var(${No})))`}),l(i,c)}),i},xC=({theme:n,ownerState:r})=>{const i={};return Po(n.breakpoints,r.offset,(l,u)=>{let c={};u==="auto"&&(c={marginLeft:"auto"}),typeof u=="number"&&(c={marginLeft:u===0?"0px":`calc(100% * ${u} / var(${No}) + var(${Mu("column")}) * ${u} / var(${No}))`}),l(i,c)}),i},CC=({theme:n,ownerState:r})=>{if(!r.container)return{};const i={[Py]:12};return Po(n.breakpoints,r.columns,(l,u)=>{const c=u??12;l(i,{[Py]:c,"> *":{[No]:c}})}),i},TC=({theme:n,ownerState:r})=>{if(!r.container)return{};const i={};return Po(n.breakpoints,r.rowSpacing,(l,u)=>{var d;const c=typeof u=="string"?u:(d=n.spacing)==null?void 0:d.call(n,u);l(i,{[cu("row")]:c,"> *":{[Mu("row")]:c}})}),i},EC=({theme:n,ownerState:r})=>{if(!r.container)return{};const i={};return Po(n.breakpoints,r.columnSpacing,(l,u)=>{var d;const c=typeof u=="string"?u:(d=n.spacing)==null?void 0:d.call(n,u);l(i,{[cu("column")]:c,"> *":{[Mu("column")]:c}})}),i},RC=({theme:n,ownerState:r})=>{if(!r.container)return{};const i={};return Po(n.breakpoints,r.direction,(l,u)=>{l(i,{flexDirection:u})}),i},wC=({ownerState:n})=>({minWidth:0,boxSizing:"border-box",...n.container&&{display:"flex",flexWrap:"wrap",...n.wrap&&n.wrap!=="wrap"&&{flexWrap:n.wrap},gap:`var(${cu("row")}) var(${cu("column")})`}}),OC=n=>{const r=[];return Object.entries(n).forEach(([i,l])=>{l!==!1&&l!==void 0&&r.push(`grid-${i}-${String(l)}`)}),r},AC=(n,r="xs")=>{function i(l){return l===void 0?!1:typeof l=="string"&&!Number.isNaN(Number(l))||typeof l=="number"&&l>0}if(i(n))return[`spacing-${r}-${String(n)}`];if(typeof n=="object"&&!Array.isArray(n)){const l=[];return Object.entries(n).forEach(([u,c])=>{i(c)&&l.push(`spacing-${u}-${String(c)}`)}),l}return[]},MC=n=>n===void 0?[]:typeof n=="object"?Object.entries(n).map(([r,i])=>`direction-${r}-${i}`):[`direction-xs-${String(n)}`];function zC(n,r){n.item!==void 0&&delete n.item,n.zeroMinWidth!==void 0&&delete n.zeroMinWidth,r.keys.forEach(i=>{n[i]!==void 0&&delete n[i]})}const DC=El(),NC=c0("div",{name:"MuiGrid",slot:"Root"});function BC(n){return f0({props:n,name:"MuiGrid",defaultTheme:DC})}function kC(n={}){const{createStyledComponent:r=NC,useThemeProps:i=BC,useTheme:l=Rl,componentName:u="MuiGrid"}=n,c=(h,y)=>{const{container:S,direction:T,spacing:E,wrap:x,size:C}=h,D={root:["root",S&&"container",x!=="wrap"&&`wrap-xs-${String(x)}`,...MC(T),...OC(C),...S?AC(E,y.breakpoints.keys[0]):[]]};return Nt(D,k=>Dt(u,k),{})};function d(h,y,S=()=>!0){const T={};return h===null||(Array.isArray(h)?h.forEach((E,x)=>{E!==null&&S(E)&&y.keys[x]&&(T[y.keys[x]]=E)}):typeof h=="object"?Object.keys(h).forEach(E=>{const x=h[E];x!=null&&S(x)&&(T[E]=x)}):T[y.keys[0]]=h),T}const p=r(CC,EC,TC,SC,RC,wC,xC),m=R.forwardRef(function(y,S){const T=l(),E=i(y),x=Qd(E);zC(x,T.breakpoints);const{className:C,children:D,columns:k=12,container:U=!1,component:M="div",direction:A="row",wrap:w="wrap",size:j={},offset:H={},spacing:q=0,rowSpacing:Q=q,columnSpacing:v=q,unstable_level:_=0,...K}=x,Z=d(j,T.breakpoints,X=>X!==!1),it=d(H,T.breakpoints),F=y.columns??(_?void 0:k),N=y.spacing??(_?void 0:q),V=y.rowSpacing??y.spacing??(_?void 0:Q),ot=y.columnSpacing??y.spacing??(_?void 0:v),W={...x,level:_,columns:F,container:U,direction:A,wrap:w,spacing:N,rowSpacing:V,columnSpacing:ot,size:Z,offset:it},z=c(W,T);return O.jsx(p,{ref:S,as:M,ownerState:W,className:gt(z.root,C),...K,children:R.Children.map(D,X=>{var et;return R.isValidElement(X)&&ru(X,["Grid"])&&U&&X.props.container?R.cloneElement(X,{unstable_level:((et=X.props)==null?void 0:et.unstable_level)??_+1}):X})})});return m.muiName="Grid",m}const ml={black:"#000",white:"#fff"},$C={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"},xo={50:"#f3e5f5",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",700:"#7b1fa2"},Co={300:"#e57373",400:"#ef5350",500:"#f44336",700:"#d32f2f",800:"#c62828"},Ii={300:"#ffb74d",400:"#ffa726",500:"#ff9800",700:"#f57c00",900:"#e65100"},To={50:"#e3f2fd",200:"#90caf9",400:"#42a5f5",700:"#1976d2",800:"#1565c0"},Eo={300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",700:"#0288d1",900:"#01579b"},Ro={300:"#81c784",400:"#66bb6a",500:"#4caf50",700:"#388e3c",800:"#2e7d32",900:"#1b5e20"};function v0(){return{text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:ml.white,default:ml.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}}}const jC=v0();function b0(){return{text:{primary:ml.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:ml.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}}}const qy=b0();function Gy(n,r,i,l){const u=l.light||l,c=l.dark||l*1.5;n[r]||(n.hasOwnProperty(i)?n[r]=n[i]:r==="light"?n.light=Fd(n.main,u):r==="dark"&&(n.dark=Zd(n.main,c)))}function _C(n="light"){return n==="dark"?{main:To[200],light:To[50],dark:To[400]}:{main:To[700],light:To[400],dark:To[800]}}function UC(n="light"){return n==="dark"?{main:xo[200],light:xo[50],dark:xo[400]}:{main:xo[500],light:xo[300],dark:xo[700]}}function LC(n="light"){return n==="dark"?{main:Co[500],light:Co[300],dark:Co[700]}:{main:Co[700],light:Co[400],dark:Co[800]}}function HC(n="light"){return n==="dark"?{main:Eo[400],light:Eo[300],dark:Eo[700]}:{main:Eo[700],light:Eo[500],dark:Eo[900]}}function PC(n="light"){return n==="dark"?{main:Ro[400],light:Ro[300],dark:Ro[700]}:{main:Ro[800],light:Ro[500],dark:Ro[900]}}function qC(n="light"){return n==="dark"?{main:Ii[400],light:Ii[300],dark:Ii[700]}:{main:"#ed6c02",light:Ii[500],dark:Ii[900]}}function ap(n){const{mode:r="light",contrastThreshold:i=3,tonalOffset:l=.2,...u}=n,c=n.primary||_C(r),d=n.secondary||UC(r),p=n.error||LC(r),m=n.info||HC(r),h=n.success||PC(r),y=n.warning||qC(r);function S(C){return V2(C,qy.text.primary)>=i?qy.text.primary:jC.text.primary}const T=({color:C,name:D,mainShade:k=500,lightShade:U=300,darkShade:M=700})=>{if(C={...C},!C.main&&C[k]&&(C.main=C[k]),!C.hasOwnProperty("main"))throw new Error(Ma(11,D?` (${D})`:"",k));if(typeof C.main!="string")throw new Error(Ma(12,D?` (${D})`:"",JSON.stringify(C.main)));return Gy(C,"light",U,l),Gy(C,"dark",M,l),C.contrastText||(C.contrastText=S(C.main)),C};let E;return r==="light"?E=v0():r==="dark"&&(E=b0()),nn({common:{...ml},mode:r,primary:T({color:c,name:"primary"}),secondary:T({color:d,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:T({color:p,name:"error"}),warning:T({color:y,name:"warning"}),info:T({color:m,name:"info"}),success:T({color:h,name:"success"}),grey:$C,contrastThreshold:i,getContrastText:S,augmentColor:T,tonalOffset:l,...E},u)}function GC(n){const r={};return Object.entries(n).forEach(l=>{const[u,c]=l;typeof c=="object"&&(r[u]=`${c.fontStyle?`${c.fontStyle} `:""}${c.fontVariant?`${c.fontVariant} `:""}${c.fontWeight?`${c.fontWeight} `:""}${c.fontStretch?`${c.fontStretch} `:""}${c.fontSize||""}${c.lineHeight?`/${c.lineHeight} `:""}${c.fontFamily||""}`)}),r}function VC(n,r){return{toolbar:{minHeight:56,[n.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[n.up("sm")]:{minHeight:64}},...r}}function YC(n){return Math.round(n*1e5)/1e5}const Vy={textTransform:"uppercase"},Yy='"Roboto", "Helvetica", "Arial", sans-serif';function S0(n,r){const{fontFamily:i=Yy,fontSize:l=14,fontWeightLight:u=300,fontWeightRegular:c=400,fontWeightMedium:d=500,fontWeightBold:p=700,htmlFontSize:m=16,allVariants:h,pxToRem:y,...S}=typeof r=="function"?r(n):r,T=l/14,E=y||(D=>`${D/m*T}rem`),x=(D,k,U,M,A)=>({fontFamily:i,fontWeight:D,fontSize:E(k),lineHeight:U,...i===Yy?{letterSpacing:`${YC(M/k)}em`}:{},...A,...h}),C={h1:x(u,96,1.167,-1.5),h2:x(u,60,1.2,-.5),h3:x(c,48,1.167,0),h4:x(c,34,1.235,.25),h5:x(c,24,1.334,0),h6:x(d,20,1.6,.15),subtitle1:x(c,16,1.75,.15),subtitle2:x(d,14,1.57,.1),body1:x(c,16,1.5,.15),body2:x(c,14,1.43,.15),button:x(d,14,1.75,.4,Vy),caption:x(c,12,1.66,.4),overline:x(c,12,2.66,1,Vy),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return nn({htmlFontSize:m,pxToRem:E,fontFamily:i,fontSize:l,fontWeightLight:u,fontWeightRegular:c,fontWeightMedium:d,fontWeightBold:p,...C},S,{clone:!1})}const XC=.2,IC=.14,KC=.12;function xe(...n){return[`${n[0]}px ${n[1]}px ${n[2]}px ${n[3]}px rgba(0,0,0,${XC})`,`${n[4]}px ${n[5]}px ${n[6]}px ${n[7]}px rgba(0,0,0,${IC})`,`${n[8]}px ${n[9]}px ${n[10]}px ${n[11]}px rgba(0,0,0,${KC})`].join(",")}const QC=["none",xe(0,2,1,-1,0,1,1,0,0,1,3,0),xe(0,3,1,-2,0,2,2,0,0,1,5,0),xe(0,3,3,-2,0,3,4,0,0,1,8,0),xe(0,2,4,-1,0,4,5,0,0,1,10,0),xe(0,3,5,-1,0,5,8,0,0,1,14,0),xe(0,3,5,-1,0,6,10,0,0,1,18,0),xe(0,4,5,-2,0,7,10,1,0,2,16,1),xe(0,5,5,-3,0,8,10,1,0,3,14,2),xe(0,5,6,-3,0,9,12,1,0,3,16,2),xe(0,6,6,-3,0,10,14,1,0,4,18,3),xe(0,6,7,-4,0,11,15,1,0,4,20,3),xe(0,7,8,-4,0,12,17,2,0,5,22,4),xe(0,7,8,-4,0,13,19,2,0,5,24,4),xe(0,7,9,-4,0,14,21,2,0,5,26,4),xe(0,8,9,-5,0,15,22,2,0,6,28,5),xe(0,8,10,-5,0,16,24,2,0,6,30,5),xe(0,8,11,-5,0,17,26,2,0,6,32,5),xe(0,9,11,-5,0,18,28,2,0,7,34,6),xe(0,9,12,-6,0,19,29,2,0,7,36,6),xe(0,10,13,-6,0,20,31,3,0,8,38,7),xe(0,10,13,-6,0,21,33,3,0,8,40,7),xe(0,10,14,-6,0,22,35,3,0,8,42,7),xe(0,11,14,-7,0,23,36,3,0,9,44,8),xe(0,11,15,-7,0,24,38,3,0,9,46,8)],WC={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},ZC={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function Xy(n){return`${Math.round(n)}ms`}function FC(n){if(!n)return 0;const r=n/36;return Math.min(Math.round((4+15*r**.25+r/5)*10),3e3)}function JC(n){const r={...WC,...n.easing},i={...ZC,...n.duration};return{getAutoHeightDuration:FC,create:(u=["all"],c={})=>{const{duration:d=i.standard,easing:p=r.easeInOut,delay:m=0,...h}=c;return(Array.isArray(u)?u:[u]).map(y=>`${y} ${typeof d=="string"?d:Xy(d)} ${p} ${typeof m=="string"?m:Xy(m)}`).join(",")},...n,easing:r,duration:i}}const tT={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500};function eT(n){return aa(n)||typeof n>"u"||typeof n=="string"||typeof n=="boolean"||typeof n=="number"||Array.isArray(n)}function x0(n={}){const r={...n};function i(l){const u=Object.entries(l);for(let c=0;c<u.length;c++){const[d,p]=u[c];!eT(p)||d.startsWith("unstable_")?delete l[d]:aa(p)&&(l[d]={...p},i(l[d]))}}return i(r),`import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';

const theme = ${JSON.stringify(r,null,2)};

theme.breakpoints = createBreakpoints(theme.breakpoints || {});
theme.transitions = createTransitions(theme.transitions || {});

export default theme;`}function vd(n={},...r){const{breakpoints:i,mixins:l={},spacing:u,palette:c={},transitions:d={},typography:p={},shape:m,...h}=n;if(n.vars&&n.generateThemeVars===void 0)throw new Error(Ma(20));const y=ap(c),S=El(n);let T=nn(S,{mixins:VC(S.breakpoints,l),palette:y,shadows:QC.slice(),typography:S0(y,p),transitions:JC(d),zIndex:{...tT}});return T=nn(T,h),T=r.reduce((E,x)=>nn(E,x),T),T.unstable_sxConfig={...Tl,...h==null?void 0:h.unstable_sxConfig},T.unstable_sx=function(x){return ar({sx:x,theme:this})},T.toRuntimeSource=x0,T}function bd(n){let r;return n<1?r=5.11916*n**2:r=4.5*Math.log(n+1)+2,Math.round(r*10)/1e3}const nT=[...Array(25)].map((n,r)=>{if(r===0)return"none";const i=bd(r);return`linear-gradient(rgba(255 255 255 / ${i}), rgba(255 255 255 / ${i}))`});function C0(n){return{inputPlaceholder:n==="dark"?.5:.42,inputUnderline:n==="dark"?.7:.42,switchTrackDisabled:n==="dark"?.2:.12,switchTrack:n==="dark"?.3:.38}}function T0(n){return n==="dark"?nT:[]}function aT(n){const{palette:r={mode:"light"},opacity:i,overlays:l,...u}=n,c=ap(r);return{palette:c,opacity:{...C0(c.mode),...i},overlays:l||T0(c.mode),...u}}function rT(n){var r;return!!n[0].match(/(cssVarPrefix|colorSchemeSelector|rootSelector|typography|mixins|breakpoints|direction|transitions)/)||!!n[0].match(/sxConfig$/)||n[0]==="palette"&&!!((r=n[1])!=null&&r.match(/(mode|contrastThreshold|tonalOffset)/))}const oT=n=>[...[...Array(25)].map((r,i)=>`--${n?`${n}-`:""}overlays-${i}`),`--${n?`${n}-`:""}palette-AppBar-darkBg`,`--${n?`${n}-`:""}palette-AppBar-darkColor`],iT=n=>(r,i)=>{const l=n.rootSelector||":root",u=n.colorSchemeSelector;let c=u;if(u==="class"&&(c=".%s"),u==="data"&&(c="[data-%s]"),u!=null&&u.startsWith("data-")&&!u.includes("%s")&&(c=`[${u}="%s"]`),n.defaultColorScheme===r){if(r==="dark"){const d={};return oT(n.cssVarPrefix).forEach(p=>{d[p]=i[p],delete i[p]}),c==="media"?{[l]:i,"@media (prefers-color-scheme: dark)":{[l]:d}}:c?{[c.replace("%s",r)]:d,[`${l}, ${c.replace("%s",r)}`]:i}:{[l]:{...i,...d}}}if(c&&c!=="media")return`${l}, ${c.replace("%s",String(r))}`}else if(r){if(c==="media")return{[`@media (prefers-color-scheme: ${String(r)})`]:{[l]:i}};if(c)return c.replace("%s",String(r))}return l};function lT(n,r){r.forEach(i=>{n[i]||(n[i]={})})}function J(n,r,i){!n[r]&&i&&(n[r]=i)}function el(n){return typeof n!="string"||!n.startsWith("hsl")?n:d0(n)}function Ra(n,r){`${r}Channel`in n||(n[`${r}Channel`]=tl(el(n[r])))}function sT(n){return typeof n=="number"?`${n}px`:typeof n=="string"||typeof n=="function"||Array.isArray(n)?n:"8px"}const Jn=n=>{try{return n()}catch{}},uT=(n="mui")=>uC(n);function id(n,r,i,l){if(!r)return;r=r===!0?{}:r;const u=l==="dark"?"dark":"light";if(!i){n[l]=aT({...r,palette:{mode:u,...r==null?void 0:r.palette}});return}const{palette:c,...d}=vd({...i,palette:{mode:u,...r==null?void 0:r.palette}});return n[l]={...r,palette:c,opacity:{...C0(u),...r==null?void 0:r.opacity},overlays:(r==null?void 0:r.overlays)||T0(u)},d}function cT(n={},...r){const{colorSchemes:i={light:!0},defaultColorScheme:l,disableCssColorScheme:u=!1,cssVarPrefix:c="mui",shouldSkipGeneratingVar:d=rT,colorSchemeSelector:p=i.light&&i.dark?"media":void 0,rootSelector:m=":root",...h}=n,y=Object.keys(i)[0],S=l||(i.light&&y!=="light"?"light":y),T=uT(c),{[S]:E,light:x,dark:C,...D}=i,k={...D};let U=E;if((S==="dark"&&!("dark"in i)||S==="light"&&!("light"in i))&&(U=!0),!U)throw new Error(Ma(21,S));const M=id(k,U,h,S);x&&!k.light&&id(k,x,void 0,"light"),C&&!k.dark&&id(k,C,void 0,"dark");let A={defaultColorScheme:S,...M,cssVarPrefix:c,colorSchemeSelector:p,rootSelector:m,getCssVar:T,colorSchemes:k,font:{...GC(M.typography),...M.font},spacing:sT(h.spacing)};Object.keys(A.colorSchemes).forEach(Q=>{const v=A.colorSchemes[Q].palette,_=K=>{const Z=K.split("-"),it=Z[1],F=Z[2];return T(K,v[it][F])};if(v.mode==="light"&&(J(v.common,"background","#fff"),J(v.common,"onBackground","#000")),v.mode==="dark"&&(J(v.common,"background","#000"),J(v.common,"onBackground","#fff")),lT(v,["Alert","AppBar","Avatar","Button","Chip","FilledInput","LinearProgress","Skeleton","Slider","SnackbarContent","SpeedDialAction","StepConnector","StepContent","Switch","TableCell","Tooltip"]),v.mode==="light"){J(v.Alert,"errorColor",me(v.error.light,.6)),J(v.Alert,"infoColor",me(v.info.light,.6)),J(v.Alert,"successColor",me(v.success.light,.6)),J(v.Alert,"warningColor",me(v.warning.light,.6)),J(v.Alert,"errorFilledBg",_("palette-error-main")),J(v.Alert,"infoFilledBg",_("palette-info-main")),J(v.Alert,"successFilledBg",_("palette-success-main")),J(v.Alert,"warningFilledBg",_("palette-warning-main")),J(v.Alert,"errorFilledColor",Jn(()=>v.getContrastText(v.error.main))),J(v.Alert,"infoFilledColor",Jn(()=>v.getContrastText(v.info.main))),J(v.Alert,"successFilledColor",Jn(()=>v.getContrastText(v.success.main))),J(v.Alert,"warningFilledColor",Jn(()=>v.getContrastText(v.warning.main))),J(v.Alert,"errorStandardBg",he(v.error.light,.9)),J(v.Alert,"infoStandardBg",he(v.info.light,.9)),J(v.Alert,"successStandardBg",he(v.success.light,.9)),J(v.Alert,"warningStandardBg",he(v.warning.light,.9)),J(v.Alert,"errorIconColor",_("palette-error-main")),J(v.Alert,"infoIconColor",_("palette-info-main")),J(v.Alert,"successIconColor",_("palette-success-main")),J(v.Alert,"warningIconColor",_("palette-warning-main")),J(v.AppBar,"defaultBg",_("palette-grey-100")),J(v.Avatar,"defaultBg",_("palette-grey-400")),J(v.Button,"inheritContainedBg",_("palette-grey-300")),J(v.Button,"inheritContainedHoverBg",_("palette-grey-A100")),J(v.Chip,"defaultBorder",_("palette-grey-400")),J(v.Chip,"defaultAvatarColor",_("palette-grey-700")),J(v.Chip,"defaultIconColor",_("palette-grey-700")),J(v.FilledInput,"bg","rgba(0, 0, 0, 0.06)"),J(v.FilledInput,"hoverBg","rgba(0, 0, 0, 0.09)"),J(v.FilledInput,"disabledBg","rgba(0, 0, 0, 0.12)"),J(v.LinearProgress,"primaryBg",he(v.primary.main,.62)),J(v.LinearProgress,"secondaryBg",he(v.secondary.main,.62)),J(v.LinearProgress,"errorBg",he(v.error.main,.62)),J(v.LinearProgress,"infoBg",he(v.info.main,.62)),J(v.LinearProgress,"successBg",he(v.success.main,.62)),J(v.LinearProgress,"warningBg",he(v.warning.main,.62)),J(v.Skeleton,"bg",`rgba(${_("palette-text-primaryChannel")} / 0.11)`),J(v.Slider,"primaryTrack",he(v.primary.main,.62)),J(v.Slider,"secondaryTrack",he(v.secondary.main,.62)),J(v.Slider,"errorTrack",he(v.error.main,.62)),J(v.Slider,"infoTrack",he(v.info.main,.62)),J(v.Slider,"successTrack",he(v.success.main,.62)),J(v.Slider,"warningTrack",he(v.warning.main,.62));const K=Ys(v.background.default,.8);J(v.SnackbarContent,"bg",K),J(v.SnackbarContent,"color",Jn(()=>v.getContrastText(K))),J(v.SpeedDialAction,"fabHoverBg",Ys(v.background.paper,.15)),J(v.StepConnector,"border",_("palette-grey-400")),J(v.StepContent,"border",_("palette-grey-400")),J(v.Switch,"defaultColor",_("palette-common-white")),J(v.Switch,"defaultDisabledColor",_("palette-grey-100")),J(v.Switch,"primaryDisabledColor",he(v.primary.main,.62)),J(v.Switch,"secondaryDisabledColor",he(v.secondary.main,.62)),J(v.Switch,"errorDisabledColor",he(v.error.main,.62)),J(v.Switch,"infoDisabledColor",he(v.info.main,.62)),J(v.Switch,"successDisabledColor",he(v.success.main,.62)),J(v.Switch,"warningDisabledColor",he(v.warning.main,.62)),J(v.TableCell,"border",he(Vs(v.divider,1),.88)),J(v.Tooltip,"bg",Vs(v.grey[700],.92))}if(v.mode==="dark"){J(v.Alert,"errorColor",he(v.error.light,.6)),J(v.Alert,"infoColor",he(v.info.light,.6)),J(v.Alert,"successColor",he(v.success.light,.6)),J(v.Alert,"warningColor",he(v.warning.light,.6)),J(v.Alert,"errorFilledBg",_("palette-error-dark")),J(v.Alert,"infoFilledBg",_("palette-info-dark")),J(v.Alert,"successFilledBg",_("palette-success-dark")),J(v.Alert,"warningFilledBg",_("palette-warning-dark")),J(v.Alert,"errorFilledColor",Jn(()=>v.getContrastText(v.error.dark))),J(v.Alert,"infoFilledColor",Jn(()=>v.getContrastText(v.info.dark))),J(v.Alert,"successFilledColor",Jn(()=>v.getContrastText(v.success.dark))),J(v.Alert,"warningFilledColor",Jn(()=>v.getContrastText(v.warning.dark))),J(v.Alert,"errorStandardBg",me(v.error.light,.9)),J(v.Alert,"infoStandardBg",me(v.info.light,.9)),J(v.Alert,"successStandardBg",me(v.success.light,.9)),J(v.Alert,"warningStandardBg",me(v.warning.light,.9)),J(v.Alert,"errorIconColor",_("palette-error-main")),J(v.Alert,"infoIconColor",_("palette-info-main")),J(v.Alert,"successIconColor",_("palette-success-main")),J(v.Alert,"warningIconColor",_("palette-warning-main")),J(v.AppBar,"defaultBg",_("palette-grey-900")),J(v.AppBar,"darkBg",_("palette-background-paper")),J(v.AppBar,"darkColor",_("palette-text-primary")),J(v.Avatar,"defaultBg",_("palette-grey-600")),J(v.Button,"inheritContainedBg",_("palette-grey-800")),J(v.Button,"inheritContainedHoverBg",_("palette-grey-700")),J(v.Chip,"defaultBorder",_("palette-grey-700")),J(v.Chip,"defaultAvatarColor",_("palette-grey-300")),J(v.Chip,"defaultIconColor",_("palette-grey-300")),J(v.FilledInput,"bg","rgba(255, 255, 255, 0.09)"),J(v.FilledInput,"hoverBg","rgba(255, 255, 255, 0.13)"),J(v.FilledInput,"disabledBg","rgba(255, 255, 255, 0.12)"),J(v.LinearProgress,"primaryBg",me(v.primary.main,.5)),J(v.LinearProgress,"secondaryBg",me(v.secondary.main,.5)),J(v.LinearProgress,"errorBg",me(v.error.main,.5)),J(v.LinearProgress,"infoBg",me(v.info.main,.5)),J(v.LinearProgress,"successBg",me(v.success.main,.5)),J(v.LinearProgress,"warningBg",me(v.warning.main,.5)),J(v.Skeleton,"bg",`rgba(${_("palette-text-primaryChannel")} / 0.13)`),J(v.Slider,"primaryTrack",me(v.primary.main,.5)),J(v.Slider,"secondaryTrack",me(v.secondary.main,.5)),J(v.Slider,"errorTrack",me(v.error.main,.5)),J(v.Slider,"infoTrack",me(v.info.main,.5)),J(v.Slider,"successTrack",me(v.success.main,.5)),J(v.Slider,"warningTrack",me(v.warning.main,.5));const K=Ys(v.background.default,.98);J(v.SnackbarContent,"bg",K),J(v.SnackbarContent,"color",Jn(()=>v.getContrastText(K))),J(v.SpeedDialAction,"fabHoverBg",Ys(v.background.paper,.15)),J(v.StepConnector,"border",_("palette-grey-600")),J(v.StepContent,"border",_("palette-grey-600")),J(v.Switch,"defaultColor",_("palette-grey-300")),J(v.Switch,"defaultDisabledColor",_("palette-grey-600")),J(v.Switch,"primaryDisabledColor",me(v.primary.main,.55)),J(v.Switch,"secondaryDisabledColor",me(v.secondary.main,.55)),J(v.Switch,"errorDisabledColor",me(v.error.main,.55)),J(v.Switch,"infoDisabledColor",me(v.info.main,.55)),J(v.Switch,"successDisabledColor",me(v.success.main,.55)),J(v.Switch,"warningDisabledColor",me(v.warning.main,.55)),J(v.TableCell,"border",me(Vs(v.divider,1),.68)),J(v.Tooltip,"bg",Vs(v.grey[700],.92))}Ra(v.background,"default"),Ra(v.background,"paper"),Ra(v.common,"background"),Ra(v.common,"onBackground"),Ra(v,"divider"),Object.keys(v).forEach(K=>{const Z=v[K];K!=="tonalOffset"&&Z&&typeof Z=="object"&&(Z.main&&J(v[K],"mainChannel",tl(el(Z.main))),Z.light&&J(v[K],"lightChannel",tl(el(Z.light))),Z.dark&&J(v[K],"darkChannel",tl(el(Z.dark))),Z.contrastText&&J(v[K],"contrastTextChannel",tl(el(Z.contrastText))),K==="text"&&(Ra(v[K],"primary"),Ra(v[K],"secondary")),K==="action"&&(Z.active&&Ra(v[K],"active"),Z.selected&&Ra(v[K],"selected")))})}),A=r.reduce((Q,v)=>nn(Q,v),A);const w={prefix:c,disableCssColorScheme:u,shouldSkipGeneratingVar:d,getSelector:iT(A)},{vars:j,generateThemeVars:H,generateStyleSheets:q}=dC(A,w);return A.vars=j,Object.entries(A.colorSchemes[A.defaultColorScheme]).forEach(([Q,v])=>{A[Q]=v}),A.generateThemeVars=H,A.generateStyleSheets=q,A.generateSpacing=function(){return a0(h.spacing,Id(this))},A.getColorSchemeSelector=pC(p),A.spacing=A.generateSpacing(),A.shouldSkipGeneratingVar=d,A.unstable_sxConfig={...Tl,...h==null?void 0:h.unstable_sxConfig},A.unstable_sx=function(v){return ar({sx:v,theme:this})},A.toRuntimeSource=x0,A}function Iy(n,r,i){n.colorSchemes&&i&&(n.colorSchemes[r]={...i!==!0&&i,palette:ap({...i===!0?{}:i.palette,mode:r})})}function zu(n={},...r){const{palette:i,cssVariables:l=!1,colorSchemes:u=i?void 0:{light:!0},defaultColorScheme:c=i==null?void 0:i.mode,...d}=n,p=c||"light",m=u==null?void 0:u[p],h={...u,...i?{[p]:{...typeof m!="boolean"&&m,palette:i}}:void 0};if(l===!1){if(!("colorSchemes"in n))return vd(n,...r);let y=i;"palette"in n||h[p]&&(h[p]!==!0?y=h[p].palette:p==="dark"&&(y={mode:"dark"}));const S=vd({...n,palette:y},...r);return S.defaultColorScheme=p,S.colorSchemes=h,S.palette.mode==="light"&&(S.colorSchemes.light={...h.light!==!0&&h.light,palette:S.palette},Iy(S,"dark",h.dark)),S.palette.mode==="dark"&&(S.colorSchemes.dark={...h.dark!==!0&&h.dark,palette:S.palette},Iy(S,"light",h.light)),S}return!i&&!("light"in h)&&p==="light"&&(h.light=!0),cT({...d,colorSchemes:h,defaultColorScheme:p,...typeof l!="boolean"&&l},...r)}const rp=zu();function qo(){const n=Rl(rp);return n[oa]||n}function E0(n){return n!=="ownerState"&&n!=="theme"&&n!=="sx"&&n!=="as"}const On=n=>E0(n)&&n!=="classes",ct=u0({themeId:oa,defaultTheme:rp,rootShouldForwardProp:On});function fT({theme:n,...r}){const i=oa in n?n[oa]:void 0;return O.jsx(g0,{...r,themeId:i?oa:void 0,theme:i||n})}const Xs={colorSchemeStorageKey:"mui-color-scheme",defaultLightColorScheme:"light",defaultDarkColorScheme:"dark",modeStorageKey:"mui-mode"},{CssVarsProvider:dT}=sC({themeId:oa,theme:()=>zu({cssVariables:!0}),colorSchemeStorageKey:Xs.colorSchemeStorageKey,modeStorageKey:Xs.modeStorageKey,defaultColorScheme:{light:Xs.defaultLightColorScheme,dark:Xs.defaultDarkColorScheme},resolveTheme:n=>{const r={...n,typography:S0(n.palette,n.typography)};return r.unstable_sx=function(l){return ar({sx:l,theme:this})},r}}),pT=dT;function mT({theme:n,...r}){const i=R.useMemo(()=>{if(typeof n=="function")return n;const l=oa in n?n[oa]:n;return"colorSchemes"in l?null:"vars"in l?n:{...n,vars:null}},[n]);return i?O.jsx(fT,{theme:i,...r}):O.jsx(pT,{theme:n,...r})}function hT(n){return O.jsx(A2,{...n,defaultTheme:rp,themeId:oa})}function op(n){return function(i){return O.jsx(hT,{styles:typeof n=="function"?l=>n({theme:l,...i}):n})}}function gT(){return Qd}function Bt(n){return J2(n)}const Sd=typeof op({})=="function",yT=(n,r)=>({WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",boxSizing:"border-box",WebkitTextSizeAdjust:"100%",...r&&!n.vars&&{colorScheme:n.palette.mode}}),vT=n=>({color:(n.vars||n).palette.text.primary,...n.typography.body1,backgroundColor:(n.vars||n).palette.background.default,"@media print":{backgroundColor:(n.vars||n).palette.common.white}}),R0=(n,r=!1)=>{var c,d;const i={};r&&n.colorSchemes&&typeof n.getColorSchemeSelector=="function"&&Object.entries(n.colorSchemes).forEach(([p,m])=>{var y,S;const h=n.getColorSchemeSelector(p);h.startsWith("@")?i[h]={":root":{colorScheme:(y=m.palette)==null?void 0:y.mode}}:i[h.replace(/\s*&/,"")]={colorScheme:(S=m.palette)==null?void 0:S.mode}});let l={html:yT(n,r),"*, *::before, *::after":{boxSizing:"inherit"},"strong, b":{fontWeight:n.typography.fontWeightBold},body:{margin:0,...vT(n),"&::backdrop":{backgroundColor:(n.vars||n).palette.background.default}},...i};const u=(d=(c=n.components)==null?void 0:c.MuiCssBaseline)==null?void 0:d.styleOverrides;return u&&(l=[l,u]),l},ou="mui-ecs",bT=n=>{const r=R0(n,!1),i=Array.isArray(r)?r[0]:r;return!n.vars&&i&&(i.html[`:root:has(${ou})`]={colorScheme:n.palette.mode}),n.colorSchemes&&Object.entries(n.colorSchemes).forEach(([l,u])=>{var d,p;const c=n.getColorSchemeSelector(l);c.startsWith("@")?i[c]={[`:root:not(:has(.${ou}))`]:{colorScheme:(d=u.palette)==null?void 0:d.mode}}:i[c.replace(/\s*&/,"")]={[`&:not(:has(.${ou}))`]:{colorScheme:(p=u.palette)==null?void 0:p.mode}}}),r},ST=op(Sd?({theme:n,enableColorScheme:r})=>R0(n,r):({theme:n})=>bT(n));function xT(n){const r=Bt({props:n,name:"MuiCssBaseline"}),{children:i,enableColorScheme:l=!1}=r;return O.jsxs(R.Fragment,{children:[Sd&&O.jsx(ST,{enableColorScheme:l}),!Sd&&!l&&O.jsx("span",{className:ou,style:{display:"none"}}),i]})}const CT=zu({palette:{mode:"light",primary:{main:"#0079bf",light:"#4da6d9",dark:"#005582",contrastText:"#ffffff"},secondary:{main:"#026aa7",light:"#4da6d9",dark:"#004a7c",contrastText:"#ffffff"},background:{default:"#f4f5f7",paper:"#ffffff"},text:{primary:"#172b4d",secondary:"#5e6c84"},success:{main:"#61bd4f"},warning:{main:"#f2d600"},error:{main:"#eb5a46"},info:{main:"#00c2e0"}},typography:{fontFamily:["-apple-system","BlinkMacSystemFont","Segoe UI","Roboto","Noto Sans","Ubuntu","Droid Sans","Helvetica Neue","sans-serif"].join(","),h1:{fontSize:"2rem",fontWeight:600,color:"#172b4d"},h2:{fontSize:"1.5rem",fontWeight:600,color:"#172b4d"},h3:{fontSize:"1.25rem",fontWeight:600,color:"#172b4d"},h4:{fontSize:"1.125rem",fontWeight:600,color:"#172b4d"},h5:{fontSize:"1rem",fontWeight:600,color:"#172b4d"},h6:{fontSize:"0.875rem",fontWeight:600,color:"#172b4d"},body1:{fontSize:"0.875rem",color:"#172b4d"},body2:{fontSize:"0.75rem",color:"#5e6c84"}},shape:{borderRadius:8},components:{MuiButton:{styleOverrides:{root:{textTransform:"none",borderRadius:8,fontWeight:500},contained:{boxShadow:"none","&:hover":{boxShadow:"0 2px 4px rgba(0,0,0,0.1)"}}}},MuiCard:{styleOverrides:{root:{borderRadius:8,boxShadow:"0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)","&:hover":{boxShadow:"0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)"}}}},MuiAppBar:{styleOverrides:{root:{boxShadow:"0 1px 3px rgba(0,0,0,0.12)"}}}}});function Ky(...n){return n.reduce((r,i)=>i==null?r:function(...u){r.apply(this,u),i.apply(this,u)},()=>{})}const Gt=tC;function TT(n){return Dt("MuiSvgIcon",n)}Mt("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);const ET=n=>{const{color:r,fontSize:i,classes:l}=n,u={root:["root",r!=="inherit"&&`color${st(r)}`,`fontSize${st(i)}`]};return Nt(u,TT,l)},RT=ct("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.root,i.color!=="inherit"&&r[`color${st(i.color)}`],r[`fontSize${st(i.fontSize)}`]]}})(Gt(({theme:n})=>{var r,i,l,u,c,d,p,m,h,y,S,T,E,x;return{userSelect:"none",width:"1em",height:"1em",display:"inline-block",flexShrink:0,transition:(u=(r=n.transitions)==null?void 0:r.create)==null?void 0:u.call(r,"fill",{duration:(l=(i=(n.vars??n).transitions)==null?void 0:i.duration)==null?void 0:l.shorter}),variants:[{props:C=>!C.hasSvgAsChild,style:{fill:"currentColor"}},{props:{fontSize:"inherit"},style:{fontSize:"inherit"}},{props:{fontSize:"small"},style:{fontSize:((d=(c=n.typography)==null?void 0:c.pxToRem)==null?void 0:d.call(c,20))||"1.25rem"}},{props:{fontSize:"medium"},style:{fontSize:((m=(p=n.typography)==null?void 0:p.pxToRem)==null?void 0:m.call(p,24))||"1.5rem"}},{props:{fontSize:"large"},style:{fontSize:((y=(h=n.typography)==null?void 0:h.pxToRem)==null?void 0:y.call(h,35))||"2.1875rem"}},...Object.entries((n.vars??n).palette).filter(([,C])=>C&&C.main).map(([C])=>{var D,k;return{props:{color:C},style:{color:(k=(D=(n.vars??n).palette)==null?void 0:D[C])==null?void 0:k.main}}}),{props:{color:"action"},style:{color:(T=(S=(n.vars??n).palette)==null?void 0:S.action)==null?void 0:T.active}},{props:{color:"disabled"},style:{color:(x=(E=(n.vars??n).palette)==null?void 0:E.action)==null?void 0:x.disabled}},{props:{color:"inherit"},style:{color:void 0}}]}})),xd=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiSvgIcon"}),{children:u,className:c,color:d="inherit",component:p="svg",fontSize:m="medium",htmlColor:h,inheritViewBox:y=!1,titleAccess:S,viewBox:T="0 0 24 24",...E}=l,x=R.isValidElement(u)&&u.type==="svg",C={...l,color:d,component:p,fontSize:m,instanceFontSize:r.fontSize,inheritViewBox:y,viewBox:T,hasSvgAsChild:x},D={};y||(D.viewBox=T);const k=ET(C);return O.jsxs(RT,{as:p,className:gt(k.root,c),focusable:"false",color:h,"aria-hidden":S?void 0:!0,role:S?"img":void 0,ref:i,...D,...E,...x&&u.props,ownerState:C,children:[x?u.props.children:u,S?O.jsx("title",{children:S}):null]})});xd.muiName="SvgIcon";function Gn(n,r){function i(l,u){return O.jsx(xd,{"data-testid":void 0,ref:u,...l,children:n})}return i.muiName=xd.muiName,R.memo(R.forwardRef(i))}function w0(n,r=166){let i;function l(...u){const c=()=>{n.apply(this,u)};clearTimeout(i),i=setTimeout(c,r)}return l.clear=()=>{clearTimeout(i)},l}function Rn(n){return n&&n.ownerDocument||document}function Da(n){return Rn(n).defaultView||window}function Qy(n,r){typeof n=="function"?n(r):n&&(n.current=r)}let Wy=0;function wT(n){const[r,i]=R.useState(n),l=n||r;return R.useEffect(()=>{r==null&&(Wy+=1,i(`mui-${Wy}`))},[r]),l}const OT={...dd},Zy=OT.useId;function Go(n){if(Zy!==void 0){const r=Zy();return n??r}return wT(n)}function Cd(n){const{controlled:r,default:i,name:l,state:u="value"}=n,{current:c}=R.useRef(r!==void 0),[d,p]=R.useState(i),m=c?r:d,h=R.useCallback(y=>{c||p(y)},[]);return[m,h]}function Aa(n){const r=R.useRef(n);return In(()=>{r.current=n}),R.useRef((...i)=>(0,r.current)(...i)).current}function Pe(...n){const r=R.useRef(void 0),i=R.useCallback(l=>{const u=n.map(c=>{if(c==null)return null;if(typeof c=="function"){const d=c,p=d(l);return typeof p=="function"?p:()=>{d(null)}}return c.current=l,()=>{c.current=null}});return()=>{u.forEach(c=>c==null?void 0:c())}},n);return R.useMemo(()=>n.every(l=>l==null)?null:l=>{r.current&&(r.current(),r.current=void 0),l!=null&&(r.current=i(l))},n)}function AT(n,r){const i=n.charCodeAt(2);return n[0]==="o"&&n[1]==="n"&&i>=65&&i<=90&&typeof r=="function"}function MT(n,r){if(!n)return r;function i(d,p){const m={};return Object.keys(p).forEach(h=>{AT(h,p[h])&&typeof d[h]=="function"&&(m[h]=(...y)=>{d[h](...y),p[h](...y)})}),m}if(typeof n=="function"||typeof r=="function")return d=>{const p=typeof r=="function"?r(d):r,m=typeof n=="function"?n({...d,...p}):n,h=gt(d==null?void 0:d.className,p==null?void 0:p.className,m==null?void 0:m.className),y=i(m,p);return{...p,...m,...y,...!!h&&{className:h},...(p==null?void 0:p.style)&&(m==null?void 0:m.style)&&{style:{...p.style,...m.style}},...(p==null?void 0:p.sx)&&(m==null?void 0:m.sx)&&{sx:[...Array.isArray(p.sx)?p.sx:[p.sx],...Array.isArray(m.sx)?m.sx:[m.sx]]}}};const l=r,u=i(n,l),c=gt(l==null?void 0:l.className,n==null?void 0:n.className);return{...r,...n,...u,...!!c&&{className:c},...(l==null?void 0:l.style)&&(n==null?void 0:n.style)&&{style:{...l.style,...n.style}},...(l==null?void 0:l.sx)&&(n==null?void 0:n.sx)&&{sx:[...Array.isArray(l.sx)?l.sx:[l.sx],...Array.isArray(n.sx)?n.sx:[n.sx]]}}}function O0(n,r){if(n==null)return{};var i={};for(var l in n)if({}.hasOwnProperty.call(n,l)){if(r.indexOf(l)!==-1)continue;i[l]=n[l]}return i}function Td(n,r){return Td=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(i,l){return i.__proto__=l,i},Td(n,r)}function A0(n,r){n.prototype=Object.create(r.prototype),n.prototype.constructor=n,Td(n,r)}var M0=Uv();const Is=_v(M0),Fy={disabled:!1},fu=ra.createContext(null);var zT=function(r){return r.scrollTop},nl="unmounted",Or="exited",Ar="entering",Ao="entered",Ed="exiting",ua=function(n){A0(r,n);function r(l,u){var c;c=n.call(this,l,u)||this;var d=u,p=d&&!d.isMounting?l.enter:l.appear,m;return c.appearStatus=null,l.in?p?(m=Or,c.appearStatus=Ar):m=Ao:l.unmountOnExit||l.mountOnEnter?m=nl:m=Or,c.state={status:m},c.nextCallback=null,c}r.getDerivedStateFromProps=function(u,c){var d=u.in;return d&&c.status===nl?{status:Or}:null};var i=r.prototype;return i.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},i.componentDidUpdate=function(u){var c=null;if(u!==this.props){var d=this.state.status;this.props.in?d!==Ar&&d!==Ao&&(c=Ar):(d===Ar||d===Ao)&&(c=Ed)}this.updateStatus(!1,c)},i.componentWillUnmount=function(){this.cancelNextCallback()},i.getTimeouts=function(){var u=this.props.timeout,c,d,p;return c=d=p=u,u!=null&&typeof u!="number"&&(c=u.exit,d=u.enter,p=u.appear!==void 0?u.appear:d),{exit:c,enter:d,appear:p}},i.updateStatus=function(u,c){if(u===void 0&&(u=!1),c!==null)if(this.cancelNextCallback(),c===Ar){if(this.props.unmountOnExit||this.props.mountOnEnter){var d=this.props.nodeRef?this.props.nodeRef.current:Is.findDOMNode(this);d&&zT(d)}this.performEnter(u)}else this.performExit();else this.props.unmountOnExit&&this.state.status===Or&&this.setState({status:nl})},i.performEnter=function(u){var c=this,d=this.props.enter,p=this.context?this.context.isMounting:u,m=this.props.nodeRef?[p]:[Is.findDOMNode(this),p],h=m[0],y=m[1],S=this.getTimeouts(),T=p?S.appear:S.enter;if(!u&&!d||Fy.disabled){this.safeSetState({status:Ao},function(){c.props.onEntered(h)});return}this.props.onEnter(h,y),this.safeSetState({status:Ar},function(){c.props.onEntering(h,y),c.onTransitionEnd(T,function(){c.safeSetState({status:Ao},function(){c.props.onEntered(h,y)})})})},i.performExit=function(){var u=this,c=this.props.exit,d=this.getTimeouts(),p=this.props.nodeRef?void 0:Is.findDOMNode(this);if(!c||Fy.disabled){this.safeSetState({status:Or},function(){u.props.onExited(p)});return}this.props.onExit(p),this.safeSetState({status:Ed},function(){u.props.onExiting(p),u.onTransitionEnd(d.exit,function(){u.safeSetState({status:Or},function(){u.props.onExited(p)})})})},i.cancelNextCallback=function(){this.nextCallback!==null&&(this.nextCallback.cancel(),this.nextCallback=null)},i.safeSetState=function(u,c){c=this.setNextCallback(c),this.setState(u,c)},i.setNextCallback=function(u){var c=this,d=!0;return this.nextCallback=function(p){d&&(d=!1,c.nextCallback=null,u(p))},this.nextCallback.cancel=function(){d=!1},this.nextCallback},i.onTransitionEnd=function(u,c){this.setNextCallback(c);var d=this.props.nodeRef?this.props.nodeRef.current:Is.findDOMNode(this),p=u==null&&!this.props.addEndListener;if(!d||p){setTimeout(this.nextCallback,0);return}if(this.props.addEndListener){var m=this.props.nodeRef?[this.nextCallback]:[d,this.nextCallback],h=m[0],y=m[1];this.props.addEndListener(h,y)}u!=null&&setTimeout(this.nextCallback,u)},i.render=function(){var u=this.state.status;if(u===nl)return null;var c=this.props,d=c.children;c.in,c.mountOnEnter,c.unmountOnExit,c.appear,c.enter,c.exit,c.timeout,c.addEndListener,c.onEnter,c.onEntering,c.onEntered,c.onExit,c.onExiting,c.onExited,c.nodeRef;var p=O0(c,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]);return ra.createElement(fu.Provider,{value:null},typeof d=="function"?d(u,p):ra.cloneElement(ra.Children.only(d),p))},r}(ra.Component);ua.contextType=fu;ua.propTypes={};function wo(){}ua.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:wo,onEntering:wo,onEntered:wo,onExit:wo,onExiting:wo,onExited:wo};ua.UNMOUNTED=nl;ua.EXITED=Or;ua.ENTERING=Ar;ua.ENTERED=Ao;ua.EXITING=Ed;function DT(n){if(n===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n}function ip(n,r){var i=function(c){return r&&R.isValidElement(c)?r(c):c},l=Object.create(null);return n&&R.Children.map(n,function(u){return u}).forEach(function(u){l[u.key]=i(u)}),l}function NT(n,r){n=n||{},r=r||{};function i(y){return y in r?r[y]:n[y]}var l=Object.create(null),u=[];for(var c in n)c in r?u.length&&(l[c]=u,u=[]):u.push(c);var d,p={};for(var m in r){if(l[m])for(d=0;d<l[m].length;d++){var h=l[m][d];p[l[m][d]]=i(h)}p[m]=i(m)}for(d=0;d<u.length;d++)p[u[d]]=i(u[d]);return p}function Dr(n,r,i){return i[r]!=null?i[r]:n.props[r]}function BT(n,r){return ip(n.children,function(i){return R.cloneElement(i,{onExited:r.bind(null,i),in:!0,appear:Dr(i,"appear",n),enter:Dr(i,"enter",n),exit:Dr(i,"exit",n)})})}function kT(n,r,i){var l=ip(n.children),u=NT(r,l);return Object.keys(u).forEach(function(c){var d=u[c];if(R.isValidElement(d)){var p=c in r,m=c in l,h=r[c],y=R.isValidElement(h)&&!h.props.in;m&&(!p||y)?u[c]=R.cloneElement(d,{onExited:i.bind(null,d),in:!0,exit:Dr(d,"exit",n),enter:Dr(d,"enter",n)}):!m&&p&&!y?u[c]=R.cloneElement(d,{in:!1}):m&&p&&R.isValidElement(h)&&(u[c]=R.cloneElement(d,{onExited:i.bind(null,d),in:h.props.in,exit:Dr(d,"exit",n),enter:Dr(d,"enter",n)}))}}),u}var $T=Object.values||function(n){return Object.keys(n).map(function(r){return n[r]})},jT={component:"div",childFactory:function(r){return r}},lp=function(n){A0(r,n);function r(l,u){var c;c=n.call(this,l,u)||this;var d=c.handleExited.bind(DT(c));return c.state={contextValue:{isMounting:!0},handleExited:d,firstRender:!0},c}var i=r.prototype;return i.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},i.componentWillUnmount=function(){this.mounted=!1},r.getDerivedStateFromProps=function(u,c){var d=c.children,p=c.handleExited,m=c.firstRender;return{children:m?BT(u,p):kT(u,d,p),firstRender:!1}},i.handleExited=function(u,c){var d=ip(this.props.children);u.key in d||(u.props.onExited&&u.props.onExited(c),this.mounted&&this.setState(function(p){var m=lu({},p.children);return delete m[u.key],{children:m}}))},i.render=function(){var u=this.props,c=u.component,d=u.childFactory,p=O0(u,["component","childFactory"]),m=this.state.contextValue,h=$T(this.state.children).map(d);return delete p.appear,delete p.enter,delete p.exit,c===null?ra.createElement(fu.Provider,{value:m},h):ra.createElement(fu.Provider,{value:m},ra.createElement(c,p,h))},r}(ra.Component);lp.propTypes={};lp.defaultProps=jT;const Jy={};function z0(n,r){const i=R.useRef(Jy);return i.current===Jy&&(i.current=n(r)),i}const _T=[];function UT(n){R.useEffect(n,_T)}class Du{constructor(){Gi(this,"currentId",null);Gi(this,"clear",()=>{this.currentId!==null&&(clearTimeout(this.currentId),this.currentId=null)});Gi(this,"disposeEffect",()=>this.clear)}static create(){return new Du}start(r,i){this.clear(),this.currentId=setTimeout(()=>{this.currentId=null,i()},r)}}function Mo(){const n=z0(Du.create).current;return UT(n.disposeEffect),n}const D0=n=>n.scrollTop;function du(n,r){const{timeout:i,easing:l,style:u={}}=n;return{duration:u.transitionDuration??(typeof i=="number"?i:i[r.mode]||0),easing:u.transitionTimingFunction??(typeof l=="object"?l[r.mode]:l),delay:u.transitionDelay}}function LT(n){return Dt("MuiPaper",n)}Mt("MuiPaper",["root","rounded","outlined","elevation","elevation0","elevation1","elevation2","elevation3","elevation4","elevation5","elevation6","elevation7","elevation8","elevation9","elevation10","elevation11","elevation12","elevation13","elevation14","elevation15","elevation16","elevation17","elevation18","elevation19","elevation20","elevation21","elevation22","elevation23","elevation24"]);const HT=n=>{const{square:r,elevation:i,variant:l,classes:u}=n,c={root:["root",l,!r&&"rounded",l==="elevation"&&`elevation${i}`]};return Nt(c,LT,u)},PT=ct("div",{name:"MuiPaper",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.root,r[i.variant],!i.square&&r.rounded,i.variant==="elevation"&&r[`elevation${i.elevation}`]]}})(Gt(({theme:n})=>({backgroundColor:(n.vars||n).palette.background.paper,color:(n.vars||n).palette.text.primary,transition:n.transitions.create("box-shadow"),variants:[{props:({ownerState:r})=>!r.square,style:{borderRadius:n.shape.borderRadius}},{props:{variant:"outlined"},style:{border:`1px solid ${(n.vars||n).palette.divider}`}},{props:{variant:"elevation"},style:{boxShadow:"var(--Paper-shadow)",backgroundImage:"var(--Paper-overlay)"}}]}))),wl=R.forwardRef(function(r,i){var E;const l=Bt({props:r,name:"MuiPaper"}),u=qo(),{className:c,component:d="div",elevation:p=1,square:m=!1,variant:h="elevation",...y}=l,S={...l,component:d,elevation:p,square:m,variant:h},T=HT(S);return O.jsx(PT,{as:d,ownerState:S,className:gt(T.root,c),ref:i,...y,style:{...h==="elevation"&&{"--Paper-shadow":(u.vars||u).shadows[p],...u.vars&&{"--Paper-overlay":(E=u.vars.overlays)==null?void 0:E[p]},...!u.vars&&u.palette.mode==="dark"&&{"--Paper-overlay":`linear-gradient(${ve("#fff",bd(p))}, ${ve("#fff",bd(p))})`}},...y.style}})});function qT(n){return typeof n=="string"}function N0(n,r,i){return n===void 0||qT(n)?r:{...r,ownerState:{...r.ownerState,...i}}}function B0(n,r,i){return typeof n=="function"?n(r,i):n}function k0(n,r=[]){if(n===void 0)return{};const i={};return Object.keys(n).filter(l=>l.match(/^on[A-Z]/)&&typeof n[l]=="function"&&!r.includes(l)).forEach(l=>{i[l]=n[l]}),i}function tv(n){if(n===void 0)return{};const r={};return Object.keys(n).filter(i=>!(i.match(/^on[A-Z]/)&&typeof n[i]=="function")).forEach(i=>{r[i]=n[i]}),r}function $0(n){const{getSlotProps:r,additionalProps:i,externalSlotProps:l,externalForwardedProps:u,className:c}=n;if(!r){const E=gt(i==null?void 0:i.className,c,u==null?void 0:u.className,l==null?void 0:l.className),x={...i==null?void 0:i.style,...u==null?void 0:u.style,...l==null?void 0:l.style},C={...i,...u,...l};return E.length>0&&(C.className=E),Object.keys(x).length>0&&(C.style=x),{props:C,internalRef:void 0}}const d=k0({...u,...l}),p=tv(l),m=tv(u),h=r(d),y=gt(h==null?void 0:h.className,i==null?void 0:i.className,c,u==null?void 0:u.className,l==null?void 0:l.className),S={...h==null?void 0:h.style,...i==null?void 0:i.style,...u==null?void 0:u.style,...l==null?void 0:l.style},T={...h,...i,...m,...p};return y.length>0&&(T.className=y),Object.keys(S).length>0&&(T.style=S),{props:T,internalRef:h.ref}}function de(n,r){const{className:i,elementType:l,ownerState:u,externalForwardedProps:c,internalForwardedProps:d,shouldForwardComponentProp:p=!1,...m}=r,{component:h,slots:y={[n]:void 0},slotProps:S={[n]:void 0},...T}=c,E=y[n]||l,x=B0(S[n],u),{props:{component:C,...D},internalRef:k}=$0({className:i,...m,externalForwardedProps:n==="root"?T:void 0,externalSlotProps:x}),U=Pe(k,x==null?void 0:x.ref,r.ref),M=n==="root"?C||h:C,A=N0(E,{...n==="root"&&!h&&!y[n]&&d,...n!=="root"&&!y[n]&&d,...D,...M&&!p&&{as:M},...M&&p&&{component:M},ref:U},u);return[E,A]}function pu(n){try{return n.matches(":focus-visible")}catch{}return!1}class mu{constructor(){Gi(this,"mountEffect",()=>{this.shouldMount&&!this.didMount&&this.ref.current!==null&&(this.didMount=!0,this.mounted.resolve())});this.ref={current:null},this.mounted=null,this.didMount=!1,this.shouldMount=!1,this.setShouldMount=null}static create(){return new mu}static use(){const r=z0(mu.create).current,[i,l]=R.useState(!1);return r.shouldMount=i,r.setShouldMount=l,R.useEffect(r.mountEffect,[i]),r}mount(){return this.mounted||(this.mounted=VT(),this.shouldMount=!0,this.setShouldMount(this.shouldMount)),this.mounted}start(...r){this.mount().then(()=>{var i;return(i=this.ref.current)==null?void 0:i.start(...r)})}stop(...r){this.mount().then(()=>{var i;return(i=this.ref.current)==null?void 0:i.stop(...r)})}pulsate(...r){this.mount().then(()=>{var i;return(i=this.ref.current)==null?void 0:i.pulsate(...r)})}}function GT(){return mu.use()}function VT(){let n,r;const i=new Promise((l,u)=>{n=l,r=u});return i.resolve=n,i.reject=r,i}function YT(n){const{className:r,classes:i,pulsate:l=!1,rippleX:u,rippleY:c,rippleSize:d,in:p,onExited:m,timeout:h}=n,[y,S]=R.useState(!1),T=gt(r,i.ripple,i.rippleVisible,l&&i.ripplePulsate),E={width:d,height:d,top:-(d/2)+c,left:-(d/2)+u},x=gt(i.child,y&&i.childLeaving,l&&i.childPulsate);return!p&&!y&&S(!0),R.useEffect(()=>{if(!p&&m!=null){const C=setTimeout(m,h);return()=>{clearTimeout(C)}}},[m,p,h]),O.jsx("span",{className:T,style:E,children:O.jsx("span",{className:x})})}const _n=Mt("MuiTouchRipple",["root","ripple","rippleVisible","ripplePulsate","child","childLeaving","childPulsate"]),Rd=550,XT=80,IT=Sl`
  0% {
    transform: scale(0);
    opacity: 0.1;
  }

  100% {
    transform: scale(1);
    opacity: 0.3;
  }
`,KT=Sl`
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
`,QT=Sl`
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.92);
  }

  100% {
    transform: scale(1);
  }
`,WT=ct("span",{name:"MuiTouchRipple",slot:"Root"})({overflow:"hidden",pointerEvents:"none",position:"absolute",zIndex:0,top:0,right:0,bottom:0,left:0,borderRadius:"inherit"}),ZT=ct(YT,{name:"MuiTouchRipple",slot:"Ripple"})`
  opacity: 0;
  position: absolute;

  &.${_n.rippleVisible} {
    opacity: 0.3;
    transform: scale(1);
    animation-name: ${IT};
    animation-duration: ${Rd}ms;
    animation-timing-function: ${({theme:n})=>n.transitions.easing.easeInOut};
  }

  &.${_n.ripplePulsate} {
    animation-duration: ${({theme:n})=>n.transitions.duration.shorter}ms;
  }

  & .${_n.child} {
    opacity: 1;
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: currentColor;
  }

  & .${_n.childLeaving} {
    opacity: 0;
    animation-name: ${KT};
    animation-duration: ${Rd}ms;
    animation-timing-function: ${({theme:n})=>n.transitions.easing.easeInOut};
  }

  & .${_n.childPulsate} {
    position: absolute;
    /* @noflip */
    left: 0px;
    top: 0;
    animation-name: ${QT};
    animation-duration: 2500ms;
    animation-timing-function: ${({theme:n})=>n.transitions.easing.easeInOut};
    animation-iteration-count: infinite;
    animation-delay: 200ms;
  }
`,FT=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiTouchRipple"}),{center:u=!1,classes:c={},className:d,...p}=l,[m,h]=R.useState([]),y=R.useRef(0),S=R.useRef(null);R.useEffect(()=>{S.current&&(S.current(),S.current=null)},[m]);const T=R.useRef(!1),E=Mo(),x=R.useRef(null),C=R.useRef(null),D=R.useCallback(A=>{const{pulsate:w,rippleX:j,rippleY:H,rippleSize:q,cb:Q}=A;h(v=>[...v,O.jsx(ZT,{classes:{ripple:gt(c.ripple,_n.ripple),rippleVisible:gt(c.rippleVisible,_n.rippleVisible),ripplePulsate:gt(c.ripplePulsate,_n.ripplePulsate),child:gt(c.child,_n.child),childLeaving:gt(c.childLeaving,_n.childLeaving),childPulsate:gt(c.childPulsate,_n.childPulsate)},timeout:Rd,pulsate:w,rippleX:j,rippleY:H,rippleSize:q},y.current)]),y.current+=1,S.current=Q},[c]),k=R.useCallback((A={},w={},j=()=>{})=>{const{pulsate:H=!1,center:q=u||w.pulsate,fakeElement:Q=!1}=w;if((A==null?void 0:A.type)==="mousedown"&&T.current){T.current=!1;return}(A==null?void 0:A.type)==="touchstart"&&(T.current=!0);const v=Q?null:C.current,_=v?v.getBoundingClientRect():{width:0,height:0,left:0,top:0};let K,Z,it;if(q||A===void 0||A.clientX===0&&A.clientY===0||!A.clientX&&!A.touches)K=Math.round(_.width/2),Z=Math.round(_.height/2);else{const{clientX:F,clientY:N}=A.touches&&A.touches.length>0?A.touches[0]:A;K=Math.round(F-_.left),Z=Math.round(N-_.top)}if(q)it=Math.sqrt((2*_.width**2+_.height**2)/3),it%2===0&&(it+=1);else{const F=Math.max(Math.abs((v?v.clientWidth:0)-K),K)*2+2,N=Math.max(Math.abs((v?v.clientHeight:0)-Z),Z)*2+2;it=Math.sqrt(F**2+N**2)}A!=null&&A.touches?x.current===null&&(x.current=()=>{D({pulsate:H,rippleX:K,rippleY:Z,rippleSize:it,cb:j})},E.start(XT,()=>{x.current&&(x.current(),x.current=null)})):D({pulsate:H,rippleX:K,rippleY:Z,rippleSize:it,cb:j})},[u,D,E]),U=R.useCallback(()=>{k({},{pulsate:!0})},[k]),M=R.useCallback((A,w)=>{if(E.clear(),(A==null?void 0:A.type)==="touchend"&&x.current){x.current(),x.current=null,E.start(0,()=>{M(A,w)});return}x.current=null,h(j=>j.length>0?j.slice(1):j),S.current=w},[E]);return R.useImperativeHandle(i,()=>({pulsate:U,start:k,stop:M}),[U,k,M]),O.jsx(WT,{className:gt(_n.root,c.root,d),ref:C,...p,children:O.jsx(lp,{component:null,exit:!0,children:m})})});function JT(n){return Dt("MuiButtonBase",n)}const tE=Mt("MuiButtonBase",["root","disabled","focusVisible"]),eE=n=>{const{disabled:r,focusVisible:i,focusVisibleClassName:l,classes:u}=n,d=Nt({root:["root",r&&"disabled",i&&"focusVisible"]},JT,u);return i&&l&&(d.root+=` ${l}`),d},nE=ct("button",{name:"MuiButtonBase",slot:"Root"})({display:"inline-flex",alignItems:"center",justifyContent:"center",position:"relative",boxSizing:"border-box",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none",textDecoration:"none",color:"inherit","&::-moz-focus-inner":{borderStyle:"none"},[`&.${tE.disabled}`]:{pointerEvents:"none",cursor:"default"},"@media print":{colorAdjust:"exact"}}),ko=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiButtonBase"}),{action:u,centerRipple:c=!1,children:d,className:p,component:m="button",disabled:h=!1,disableRipple:y=!1,disableTouchRipple:S=!1,focusRipple:T=!1,focusVisibleClassName:E,LinkComponent:x="a",onBlur:C,onClick:D,onContextMenu:k,onDragLeave:U,onFocus:M,onFocusVisible:A,onKeyDown:w,onKeyUp:j,onMouseDown:H,onMouseLeave:q,onMouseUp:Q,onTouchEnd:v,onTouchMove:_,onTouchStart:K,tabIndex:Z=0,TouchRippleProps:it,touchRippleRef:F,type:N,...V}=l,ot=R.useRef(null),W=GT(),z=Pe(W.ref,F),[X,et]=R.useState(!1);h&&X&&et(!1),R.useImperativeHandle(u,()=>({focusVisible:()=>{et(!0),ot.current.focus()}}),[]);const nt=W.shouldMount&&!y&&!h;R.useEffect(()=>{X&&T&&!y&&W.pulsate()},[y,T,X,W]);const lt=wa(W,"start",H,S),ut=wa(W,"stop",k,S),ft=wa(W,"stop",U,S),Tt=wa(W,"stop",Q,S),bt=wa(W,"stop",dt=>{X&&dt.preventDefault(),q&&q(dt)},S),Ot=wa(W,"start",K,S),yt=wa(W,"stop",v,S),wt=wa(W,"stop",_,S),St=wa(W,"stop",dt=>{pu(dt.target)||et(!1),C&&C(dt)},!1),$t=Aa(dt=>{ot.current||(ot.current=dt.currentTarget),pu(dt.target)&&(et(!0),A&&A(dt)),M&&M(dt)}),Et=()=>{const dt=ot.current;return m&&m!=="button"&&!(dt.tagName==="A"&&dt.href)},qt=Aa(dt=>{T&&!dt.repeat&&X&&dt.key===" "&&W.stop(dt,()=>{W.start(dt)}),dt.target===dt.currentTarget&&Et()&&dt.key===" "&&dt.preventDefault(),w&&w(dt),dt.target===dt.currentTarget&&Et()&&dt.key==="Enter"&&!h&&(dt.preventDefault(),D&&D(dt))}),Ce=Aa(dt=>{T&&dt.key===" "&&X&&!dt.defaultPrevented&&W.stop(dt,()=>{W.pulsate(dt)}),j&&j(dt),D&&dt.target===dt.currentTarget&&Et()&&dt.key===" "&&!dt.defaultPrevented&&D(dt)});let Ut=m;Ut==="button"&&(V.href||V.to)&&(Ut=x);const Kt={};Ut==="button"?(Kt.type=N===void 0?"button":N,Kt.disabled=h):(!V.href&&!V.to&&(Kt.role="button"),h&&(Kt["aria-disabled"]=h));const Qt=Pe(i,ot),Vt={...l,centerRipple:c,component:m,disabled:h,disableRipple:y,disableTouchRipple:S,focusRipple:T,tabIndex:Z,focusVisible:X},jt=eE(Vt);return O.jsxs(nE,{as:Ut,className:gt(jt.root,p),ownerState:Vt,onBlur:St,onClick:D,onContextMenu:ut,onFocus:$t,onKeyDown:qt,onKeyUp:Ce,onMouseDown:lt,onMouseLeave:bt,onMouseUp:Tt,onDragLeave:ft,onTouchEnd:yt,onTouchMove:wt,onTouchStart:Ot,ref:Qt,tabIndex:h?-1:Z,type:N,...Kt,...V,children:[d,nt?O.jsx(FT,{ref:z,center:c,...it}):null]})});function wa(n,r,i,l=!1){return Aa(u=>(i&&i(u),l||n[r](u),!0))}function aE(n){return typeof n.main=="string"}function rE(n,r=[]){if(!aE(n))return!1;for(const i of r)if(!n.hasOwnProperty(i)||typeof n[i]!="string")return!1;return!0}function rn(n=[]){return([,r])=>r&&rE(r,n)}function oE(n){return Dt("MuiCircularProgress",n)}Mt("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);const nr=44,wd=Sl`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`,Od=Sl`
  0% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -15px;
  }

  100% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: -126px;
  }
`,iE=typeof wd!="string"?Vd`
        animation: ${wd} 1.4s linear infinite;
      `:null,lE=typeof Od!="string"?Vd`
        animation: ${Od} 1.4s ease-in-out infinite;
      `:null,sE=n=>{const{classes:r,variant:i,color:l,disableShrink:u}=n,c={root:["root",i,`color${st(l)}`],svg:["svg"],circle:["circle",`circle${st(i)}`,u&&"circleDisableShrink"]};return Nt(c,oE,r)},uE=ct("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.root,r[i.variant],r[`color${st(i.color)}`]]}})(Gt(({theme:n})=>({display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:n.transitions.create("transform")}},{props:{variant:"indeterminate"},style:iE||{animation:`${wd} 1.4s linear infinite`}},...Object.entries(n.palette).filter(rn()).map(([r])=>({props:{color:r},style:{color:(n.vars||n).palette[r].main}}))]}))),cE=ct("svg",{name:"MuiCircularProgress",slot:"Svg"})({display:"block"}),fE=ct("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.circle,r[`circle${st(i.variant)}`],i.disableShrink&&r.circleDisableShrink]}})(Gt(({theme:n})=>({stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:n.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:({ownerState:r})=>r.variant==="indeterminate"&&!r.disableShrink,style:lE||{animation:`${Od} 1.4s ease-in-out infinite`}}]}))),j0=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiCircularProgress"}),{className:u,color:c="primary",disableShrink:d=!1,size:p=40,style:m,thickness:h=3.6,value:y=0,variant:S="indeterminate",...T}=l,E={...l,color:c,disableShrink:d,size:p,thickness:h,value:y,variant:S},x=sE(E),C={},D={},k={};if(S==="determinate"){const U=2*Math.PI*((nr-h)/2);C.strokeDasharray=U.toFixed(3),k["aria-valuenow"]=Math.round(y),C.strokeDashoffset=`${((100-y)/100*U).toFixed(3)}px`,D.transform="rotate(-90deg)"}return O.jsx(uE,{className:gt(x.root,u),style:{width:p,height:p,...D,...m},ownerState:E,ref:i,role:"progressbar",...k,...T,children:O.jsx(cE,{className:x.svg,ownerState:E,viewBox:`${nr/2} ${nr/2} ${nr} ${nr}`,children:O.jsx(fE,{className:x.circle,style:C,ownerState:E,cx:nr,cy:nr,r:(nr-h)/2,fill:"none",strokeWidth:h})})})});function dE(n){return Dt("MuiIconButton",n)}const ev=Mt("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge","loading","loadingIndicator","loadingWrapper"]),pE=n=>{const{classes:r,disabled:i,color:l,edge:u,size:c,loading:d}=n,p={root:["root",d&&"loading",i&&"disabled",l!=="default"&&`color${st(l)}`,u&&`edge${st(u)}`,`size${st(c)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]};return Nt(p,dE,r)},mE=ct(ko,{name:"MuiIconButton",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.root,i.loading&&r.loading,i.color!=="default"&&r[`color${st(i.color)}`],i.edge&&r[`edge${st(i.edge)}`],r[`size${st(i.size)}`]]}})(Gt(({theme:n})=>({textAlign:"center",flex:"0 0 auto",fontSize:n.typography.pxToRem(24),padding:8,borderRadius:"50%",color:(n.vars||n).palette.action.active,transition:n.transitions.create("background-color",{duration:n.transitions.duration.shortest}),variants:[{props:r=>!r.disableRipple,style:{"--IconButton-hoverBg":n.vars?`rgba(${n.vars.palette.action.activeChannel} / ${n.vars.palette.action.hoverOpacity})`:ve(n.palette.action.active,n.palette.action.hoverOpacity),"&:hover":{backgroundColor:"var(--IconButton-hoverBg)","@media (hover: none)":{backgroundColor:"transparent"}}}},{props:{edge:"start"},style:{marginLeft:-12}},{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:{edge:"end"},style:{marginRight:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}}]})),Gt(({theme:n})=>({variants:[{props:{color:"inherit"},style:{color:"inherit"}},...Object.entries(n.palette).filter(rn()).map(([r])=>({props:{color:r},style:{color:(n.vars||n).palette[r].main}})),...Object.entries(n.palette).filter(rn()).map(([r])=>({props:{color:r},style:{"--IconButton-hoverBg":n.vars?`rgba(${(n.vars||n).palette[r].mainChannel} / ${n.vars.palette.action.hoverOpacity})`:ve((n.vars||n).palette[r].main,n.palette.action.hoverOpacity)}})),{props:{size:"small"},style:{padding:5,fontSize:n.typography.pxToRem(18)}},{props:{size:"large"},style:{padding:12,fontSize:n.typography.pxToRem(28)}}],[`&.${ev.disabled}`]:{backgroundColor:"transparent",color:(n.vars||n).palette.action.disabled},[`&.${ev.loading}`]:{color:"transparent"}}))),hE=ct("span",{name:"MuiIconButton",slot:"LoadingIndicator"})(({theme:n})=>({display:"none",position:"absolute",visibility:"visible",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:(n.vars||n).palette.action.disabled,variants:[{props:{loading:!0},style:{display:"flex"}}]})),ol=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiIconButton"}),{edge:u=!1,children:c,className:d,color:p="default",disabled:m=!1,disableFocusRipple:h=!1,size:y="medium",id:S,loading:T=null,loadingIndicator:E,...x}=l,C=Go(S),D=E??O.jsx(j0,{"aria-labelledby":C,color:"inherit",size:16}),k={...l,edge:u,color:p,disabled:m,disableFocusRipple:h,loading:T,loadingIndicator:D,size:y},U=pE(k);return O.jsxs(mE,{id:T?C:S,className:gt(U.root,d),centerRipple:!0,focusRipple:!h,disabled:m||T,ref:i,...x,ownerState:k,children:[typeof T=="boolean"&&O.jsx("span",{className:U.loadingWrapper,style:{display:"contents"},children:O.jsx(hE,{className:U.loadingIndicator,ownerState:k,children:T&&D})}),c]})});function gE(n){return Dt("MuiTypography",n)}Mt("MuiTypography",["root","h1","h2","h3","h4","h5","h6","subtitle1","subtitle2","body1","body2","inherit","button","caption","overline","alignLeft","alignRight","alignCenter","alignJustify","noWrap","gutterBottom","paragraph"]);const yE={primary:!0,secondary:!0,error:!0,info:!0,success:!0,warning:!0,textPrimary:!0,textSecondary:!0,textDisabled:!0},vE=gT(),bE=n=>{const{align:r,gutterBottom:i,noWrap:l,paragraph:u,variant:c,classes:d}=n,p={root:["root",c,n.align!=="inherit"&&`align${st(r)}`,i&&"gutterBottom",l&&"noWrap",u&&"paragraph"]};return Nt(p,gE,d)},SE=ct("span",{name:"MuiTypography",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.root,i.variant&&r[i.variant],i.align!=="inherit"&&r[`align${st(i.align)}`],i.noWrap&&r.noWrap,i.gutterBottom&&r.gutterBottom,i.paragraph&&r.paragraph]}})(Gt(({theme:n})=>{var r;return{margin:0,variants:[{props:{variant:"inherit"},style:{font:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}},...Object.entries(n.typography).filter(([i,l])=>i!=="inherit"&&l&&typeof l=="object").map(([i,l])=>({props:{variant:i},style:l})),...Object.entries(n.palette).filter(rn()).map(([i])=>({props:{color:i},style:{color:(n.vars||n).palette[i].main}})),...Object.entries(((r=n.palette)==null?void 0:r.text)||{}).filter(([,i])=>typeof i=="string").map(([i])=>({props:{color:`text${st(i)}`},style:{color:(n.vars||n).palette.text[i]}})),{props:({ownerState:i})=>i.align!=="inherit",style:{textAlign:"var(--Typography-textAlign)"}},{props:({ownerState:i})=>i.noWrap,style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},{props:({ownerState:i})=>i.gutterBottom,style:{marginBottom:"0.35em"}},{props:({ownerState:i})=>i.paragraph,style:{marginBottom:16}}]}})),nv={h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",subtitle1:"h6",subtitle2:"h6",body1:"p",body2:"p",inherit:"p"},ye=R.forwardRef(function(r,i){const{color:l,...u}=Bt({props:r,name:"MuiTypography"}),c=!yE[l],d=vE({...u,...c&&{color:l}}),{align:p="inherit",className:m,component:h,gutterBottom:y=!1,noWrap:S=!1,paragraph:T=!1,variant:E="body1",variantMapping:x=nv,...C}=d,D={...d,align:p,color:l,className:m,component:h,gutterBottom:y,noWrap:S,paragraph:T,variant:E,variantMapping:x},k=h||(T?"p":x[E]||nv[E])||"span",U=bE(D);return O.jsx(SE,{as:k,ref:i,className:gt(U.root,m),...C,ownerState:D,style:{...p!=="inherit"&&{"--Typography-textAlign":p},...C.style}})});function xE(n){return Dt("MuiAppBar",n)}Mt("MuiAppBar",["root","positionFixed","positionAbsolute","positionSticky","positionStatic","positionRelative","colorDefault","colorPrimary","colorSecondary","colorInherit","colorTransparent","colorError","colorInfo","colorSuccess","colorWarning"]);const CE=n=>{const{color:r,position:i,classes:l}=n,u={root:["root",`color${st(r)}`,`position${st(i)}`]};return Nt(u,xE,l)},av=(n,r)=>n?`${n==null?void 0:n.replace(")","")}, ${r})`:r,TE=ct(wl,{name:"MuiAppBar",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.root,r[`position${st(i.position)}`],r[`color${st(i.color)}`]]}})(Gt(({theme:n})=>({display:"flex",flexDirection:"column",width:"100%",boxSizing:"border-box",flexShrink:0,variants:[{props:{position:"fixed"},style:{position:"fixed",zIndex:(n.vars||n).zIndex.appBar,top:0,left:"auto",right:0,"@media print":{position:"absolute"}}},{props:{position:"absolute"},style:{position:"absolute",zIndex:(n.vars||n).zIndex.appBar,top:0,left:"auto",right:0}},{props:{position:"sticky"},style:{position:"sticky",zIndex:(n.vars||n).zIndex.appBar,top:0,left:"auto",right:0}},{props:{position:"static"},style:{position:"static"}},{props:{position:"relative"},style:{position:"relative"}},{props:{color:"inherit"},style:{"--AppBar-color":"inherit"}},{props:{color:"default"},style:{"--AppBar-background":n.vars?n.vars.palette.AppBar.defaultBg:n.palette.grey[100],"--AppBar-color":n.vars?n.vars.palette.text.primary:n.palette.getContrastText(n.palette.grey[100]),...n.applyStyles("dark",{"--AppBar-background":n.vars?n.vars.palette.AppBar.defaultBg:n.palette.grey[900],"--AppBar-color":n.vars?n.vars.palette.text.primary:n.palette.getContrastText(n.palette.grey[900])})}},...Object.entries(n.palette).filter(rn(["contrastText"])).map(([r])=>({props:{color:r},style:{"--AppBar-background":(n.vars??n).palette[r].main,"--AppBar-color":(n.vars??n).palette[r].contrastText}})),{props:r=>r.enableColorOnDark===!0&&!["inherit","transparent"].includes(r.color),style:{backgroundColor:"var(--AppBar-background)",color:"var(--AppBar-color)"}},{props:r=>r.enableColorOnDark===!1&&!["inherit","transparent"].includes(r.color),style:{backgroundColor:"var(--AppBar-background)",color:"var(--AppBar-color)",...n.applyStyles("dark",{backgroundColor:n.vars?av(n.vars.palette.AppBar.darkBg,"var(--AppBar-background)"):null,color:n.vars?av(n.vars.palette.AppBar.darkColor,"var(--AppBar-color)"):null})}},{props:{color:"transparent"},style:{"--AppBar-background":"transparent","--AppBar-color":"inherit",backgroundColor:"var(--AppBar-background)",color:"var(--AppBar-color)",...n.applyStyles("dark",{backgroundImage:"none"})}}]}))),EE=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiAppBar"}),{className:u,color:c="primary",enableColorOnDark:d=!1,position:p="fixed",...m}=l,h={...l,color:c,position:p,enableColorOnDark:d},y=CE(h);return O.jsx(TE,{square:!0,component:"header",ownerState:h,elevation:4,className:gt(y.root,u,p==="fixed"&&"mui-fixed"),ref:i,...m})});var dn="top",Hn="bottom",Pn="right",pn="left",sp="auto",Ol=[dn,Hn,Pn,pn],$o="start",hl="end",RE="clippingParents",_0="viewport",Ki="popper",wE="reference",rv=Ol.reduce(function(n,r){return n.concat([r+"-"+$o,r+"-"+hl])},[]),U0=[].concat(Ol,[sp]).reduce(function(n,r){return n.concat([r,r+"-"+$o,r+"-"+hl])},[]),OE="beforeRead",AE="read",ME="afterRead",zE="beforeMain",DE="main",NE="afterMain",BE="beforeWrite",kE="write",$E="afterWrite",jE=[OE,AE,ME,zE,DE,NE,BE,kE,$E];function sa(n){return n?(n.nodeName||"").toLowerCase():null}function wn(n){if(n==null)return window;if(n.toString()!=="[object Window]"){var r=n.ownerDocument;return r&&r.defaultView||window}return n}function kr(n){var r=wn(n).Element;return n instanceof r||n instanceof Element}function Ln(n){var r=wn(n).HTMLElement;return n instanceof r||n instanceof HTMLElement}function up(n){if(typeof ShadowRoot>"u")return!1;var r=wn(n).ShadowRoot;return n instanceof r||n instanceof ShadowRoot}function _E(n){var r=n.state;Object.keys(r.elements).forEach(function(i){var l=r.styles[i]||{},u=r.attributes[i]||{},c=r.elements[i];!Ln(c)||!sa(c)||(Object.assign(c.style,l),Object.keys(u).forEach(function(d){var p=u[d];p===!1?c.removeAttribute(d):c.setAttribute(d,p===!0?"":p)}))})}function UE(n){var r=n.state,i={popper:{position:r.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(r.elements.popper.style,i.popper),r.styles=i,r.elements.arrow&&Object.assign(r.elements.arrow.style,i.arrow),function(){Object.keys(r.elements).forEach(function(l){var u=r.elements[l],c=r.attributes[l]||{},d=Object.keys(r.styles.hasOwnProperty(l)?r.styles[l]:i[l]),p=d.reduce(function(m,h){return m[h]="",m},{});!Ln(u)||!sa(u)||(Object.assign(u.style,p),Object.keys(c).forEach(function(m){u.removeAttribute(m)}))})}}const LE={name:"applyStyles",enabled:!0,phase:"write",fn:_E,effect:UE,requires:["computeStyles"]};function la(n){return n.split("-")[0]}var Nr=Math.max,hu=Math.min,jo=Math.round;function Ad(){var n=navigator.userAgentData;return n!=null&&n.brands&&Array.isArray(n.brands)?n.brands.map(function(r){return r.brand+"/"+r.version}).join(" "):navigator.userAgent}function L0(){return!/^((?!chrome|android).)*safari/i.test(Ad())}function _o(n,r,i){r===void 0&&(r=!1),i===void 0&&(i=!1);var l=n.getBoundingClientRect(),u=1,c=1;r&&Ln(n)&&(u=n.offsetWidth>0&&jo(l.width)/n.offsetWidth||1,c=n.offsetHeight>0&&jo(l.height)/n.offsetHeight||1);var d=kr(n)?wn(n):window,p=d.visualViewport,m=!L0()&&i,h=(l.left+(m&&p?p.offsetLeft:0))/u,y=(l.top+(m&&p?p.offsetTop:0))/c,S=l.width/u,T=l.height/c;return{width:S,height:T,top:y,right:h+S,bottom:y+T,left:h,x:h,y}}function cp(n){var r=_o(n),i=n.offsetWidth,l=n.offsetHeight;return Math.abs(r.width-i)<=1&&(i=r.width),Math.abs(r.height-l)<=1&&(l=r.height),{x:n.offsetLeft,y:n.offsetTop,width:i,height:l}}function H0(n,r){var i=r.getRootNode&&r.getRootNode();if(n.contains(r))return!0;if(i&&up(i)){var l=r;do{if(l&&n.isSameNode(l))return!0;l=l.parentNode||l.host}while(l)}return!1}function Na(n){return wn(n).getComputedStyle(n)}function HE(n){return["table","td","th"].indexOf(sa(n))>=0}function or(n){return((kr(n)?n.ownerDocument:n.document)||window.document).documentElement}function Nu(n){return sa(n)==="html"?n:n.assignedSlot||n.parentNode||(up(n)?n.host:null)||or(n)}function ov(n){return!Ln(n)||Na(n).position==="fixed"?null:n.offsetParent}function PE(n){var r=/firefox/i.test(Ad()),i=/Trident/i.test(Ad());if(i&&Ln(n)){var l=Na(n);if(l.position==="fixed")return null}var u=Nu(n);for(up(u)&&(u=u.host);Ln(u)&&["html","body"].indexOf(sa(u))<0;){var c=Na(u);if(c.transform!=="none"||c.perspective!=="none"||c.contain==="paint"||["transform","perspective"].indexOf(c.willChange)!==-1||r&&c.willChange==="filter"||r&&c.filter&&c.filter!=="none")return u;u=u.parentNode}return null}function Al(n){for(var r=wn(n),i=ov(n);i&&HE(i)&&Na(i).position==="static";)i=ov(i);return i&&(sa(i)==="html"||sa(i)==="body"&&Na(i).position==="static")?r:i||PE(n)||r}function fp(n){return["top","bottom"].indexOf(n)>=0?"x":"y"}function il(n,r,i){return Nr(n,hu(r,i))}function qE(n,r,i){var l=il(n,r,i);return l>i?i:l}function P0(){return{top:0,right:0,bottom:0,left:0}}function q0(n){return Object.assign({},P0(),n)}function G0(n,r){return r.reduce(function(i,l){return i[l]=n,i},{})}var GE=function(r,i){return r=typeof r=="function"?r(Object.assign({},i.rects,{placement:i.placement})):r,q0(typeof r!="number"?r:G0(r,Ol))};function VE(n){var r,i=n.state,l=n.name,u=n.options,c=i.elements.arrow,d=i.modifiersData.popperOffsets,p=la(i.placement),m=fp(p),h=[pn,Pn].indexOf(p)>=0,y=h?"height":"width";if(!(!c||!d)){var S=GE(u.padding,i),T=cp(c),E=m==="y"?dn:pn,x=m==="y"?Hn:Pn,C=i.rects.reference[y]+i.rects.reference[m]-d[m]-i.rects.popper[y],D=d[m]-i.rects.reference[m],k=Al(c),U=k?m==="y"?k.clientHeight||0:k.clientWidth||0:0,M=C/2-D/2,A=S[E],w=U-T[y]-S[x],j=U/2-T[y]/2+M,H=il(A,j,w),q=m;i.modifiersData[l]=(r={},r[q]=H,r.centerOffset=H-j,r)}}function YE(n){var r=n.state,i=n.options,l=i.element,u=l===void 0?"[data-popper-arrow]":l;u!=null&&(typeof u=="string"&&(u=r.elements.popper.querySelector(u),!u)||H0(r.elements.popper,u)&&(r.elements.arrow=u))}const XE={name:"arrow",enabled:!0,phase:"main",fn:VE,effect:YE,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Uo(n){return n.split("-")[1]}var IE={top:"auto",right:"auto",bottom:"auto",left:"auto"};function KE(n,r){var i=n.x,l=n.y,u=r.devicePixelRatio||1;return{x:jo(i*u)/u||0,y:jo(l*u)/u||0}}function iv(n){var r,i=n.popper,l=n.popperRect,u=n.placement,c=n.variation,d=n.offsets,p=n.position,m=n.gpuAcceleration,h=n.adaptive,y=n.roundOffsets,S=n.isFixed,T=d.x,E=T===void 0?0:T,x=d.y,C=x===void 0?0:x,D=typeof y=="function"?y({x:E,y:C}):{x:E,y:C};E=D.x,C=D.y;var k=d.hasOwnProperty("x"),U=d.hasOwnProperty("y"),M=pn,A=dn,w=window;if(h){var j=Al(i),H="clientHeight",q="clientWidth";if(j===wn(i)&&(j=or(i),Na(j).position!=="static"&&p==="absolute"&&(H="scrollHeight",q="scrollWidth")),j=j,u===dn||(u===pn||u===Pn)&&c===hl){A=Hn;var Q=S&&j===w&&w.visualViewport?w.visualViewport.height:j[H];C-=Q-l.height,C*=m?1:-1}if(u===pn||(u===dn||u===Hn)&&c===hl){M=Pn;var v=S&&j===w&&w.visualViewport?w.visualViewport.width:j[q];E-=v-l.width,E*=m?1:-1}}var _=Object.assign({position:p},h&&IE),K=y===!0?KE({x:E,y:C},wn(i)):{x:E,y:C};if(E=K.x,C=K.y,m){var Z;return Object.assign({},_,(Z={},Z[A]=U?"0":"",Z[M]=k?"0":"",Z.transform=(w.devicePixelRatio||1)<=1?"translate("+E+"px, "+C+"px)":"translate3d("+E+"px, "+C+"px, 0)",Z))}return Object.assign({},_,(r={},r[A]=U?C+"px":"",r[M]=k?E+"px":"",r.transform="",r))}function QE(n){var r=n.state,i=n.options,l=i.gpuAcceleration,u=l===void 0?!0:l,c=i.adaptive,d=c===void 0?!0:c,p=i.roundOffsets,m=p===void 0?!0:p,h={placement:la(r.placement),variation:Uo(r.placement),popper:r.elements.popper,popperRect:r.rects.popper,gpuAcceleration:u,isFixed:r.options.strategy==="fixed"};r.modifiersData.popperOffsets!=null&&(r.styles.popper=Object.assign({},r.styles.popper,iv(Object.assign({},h,{offsets:r.modifiersData.popperOffsets,position:r.options.strategy,adaptive:d,roundOffsets:m})))),r.modifiersData.arrow!=null&&(r.styles.arrow=Object.assign({},r.styles.arrow,iv(Object.assign({},h,{offsets:r.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:m})))),r.attributes.popper=Object.assign({},r.attributes.popper,{"data-popper-placement":r.placement})}const WE={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:QE,data:{}};var Ks={passive:!0};function ZE(n){var r=n.state,i=n.instance,l=n.options,u=l.scroll,c=u===void 0?!0:u,d=l.resize,p=d===void 0?!0:d,m=wn(r.elements.popper),h=[].concat(r.scrollParents.reference,r.scrollParents.popper);return c&&h.forEach(function(y){y.addEventListener("scroll",i.update,Ks)}),p&&m.addEventListener("resize",i.update,Ks),function(){c&&h.forEach(function(y){y.removeEventListener("scroll",i.update,Ks)}),p&&m.removeEventListener("resize",i.update,Ks)}}const FE={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:ZE,data:{}};var JE={left:"right",right:"left",bottom:"top",top:"bottom"};function iu(n){return n.replace(/left|right|bottom|top/g,function(r){return JE[r]})}var tR={start:"end",end:"start"};function lv(n){return n.replace(/start|end/g,function(r){return tR[r]})}function dp(n){var r=wn(n),i=r.pageXOffset,l=r.pageYOffset;return{scrollLeft:i,scrollTop:l}}function pp(n){return _o(or(n)).left+dp(n).scrollLeft}function eR(n,r){var i=wn(n),l=or(n),u=i.visualViewport,c=l.clientWidth,d=l.clientHeight,p=0,m=0;if(u){c=u.width,d=u.height;var h=L0();(h||!h&&r==="fixed")&&(p=u.offsetLeft,m=u.offsetTop)}return{width:c,height:d,x:p+pp(n),y:m}}function nR(n){var r,i=or(n),l=dp(n),u=(r=n.ownerDocument)==null?void 0:r.body,c=Nr(i.scrollWidth,i.clientWidth,u?u.scrollWidth:0,u?u.clientWidth:0),d=Nr(i.scrollHeight,i.clientHeight,u?u.scrollHeight:0,u?u.clientHeight:0),p=-l.scrollLeft+pp(n),m=-l.scrollTop;return Na(u||i).direction==="rtl"&&(p+=Nr(i.clientWidth,u?u.clientWidth:0)-c),{width:c,height:d,x:p,y:m}}function mp(n){var r=Na(n),i=r.overflow,l=r.overflowX,u=r.overflowY;return/auto|scroll|overlay|hidden/.test(i+u+l)}function V0(n){return["html","body","#document"].indexOf(sa(n))>=0?n.ownerDocument.body:Ln(n)&&mp(n)?n:V0(Nu(n))}function ll(n,r){var i;r===void 0&&(r=[]);var l=V0(n),u=l===((i=n.ownerDocument)==null?void 0:i.body),c=wn(l),d=u?[c].concat(c.visualViewport||[],mp(l)?l:[]):l,p=r.concat(d);return u?p:p.concat(ll(Nu(d)))}function Md(n){return Object.assign({},n,{left:n.x,top:n.y,right:n.x+n.width,bottom:n.y+n.height})}function aR(n,r){var i=_o(n,!1,r==="fixed");return i.top=i.top+n.clientTop,i.left=i.left+n.clientLeft,i.bottom=i.top+n.clientHeight,i.right=i.left+n.clientWidth,i.width=n.clientWidth,i.height=n.clientHeight,i.x=i.left,i.y=i.top,i}function sv(n,r,i){return r===_0?Md(eR(n,i)):kr(r)?aR(r,i):Md(nR(or(n)))}function rR(n){var r=ll(Nu(n)),i=["absolute","fixed"].indexOf(Na(n).position)>=0,l=i&&Ln(n)?Al(n):n;return kr(l)?r.filter(function(u){return kr(u)&&H0(u,l)&&sa(u)!=="body"}):[]}function oR(n,r,i,l){var u=r==="clippingParents"?rR(n):[].concat(r),c=[].concat(u,[i]),d=c[0],p=c.reduce(function(m,h){var y=sv(n,h,l);return m.top=Nr(y.top,m.top),m.right=hu(y.right,m.right),m.bottom=hu(y.bottom,m.bottom),m.left=Nr(y.left,m.left),m},sv(n,d,l));return p.width=p.right-p.left,p.height=p.bottom-p.top,p.x=p.left,p.y=p.top,p}function Y0(n){var r=n.reference,i=n.element,l=n.placement,u=l?la(l):null,c=l?Uo(l):null,d=r.x+r.width/2-i.width/2,p=r.y+r.height/2-i.height/2,m;switch(u){case dn:m={x:d,y:r.y-i.height};break;case Hn:m={x:d,y:r.y+r.height};break;case Pn:m={x:r.x+r.width,y:p};break;case pn:m={x:r.x-i.width,y:p};break;default:m={x:r.x,y:r.y}}var h=u?fp(u):null;if(h!=null){var y=h==="y"?"height":"width";switch(c){case $o:m[h]=m[h]-(r[y]/2-i[y]/2);break;case hl:m[h]=m[h]+(r[y]/2-i[y]/2);break}}return m}function gl(n,r){r===void 0&&(r={});var i=r,l=i.placement,u=l===void 0?n.placement:l,c=i.strategy,d=c===void 0?n.strategy:c,p=i.boundary,m=p===void 0?RE:p,h=i.rootBoundary,y=h===void 0?_0:h,S=i.elementContext,T=S===void 0?Ki:S,E=i.altBoundary,x=E===void 0?!1:E,C=i.padding,D=C===void 0?0:C,k=q0(typeof D!="number"?D:G0(D,Ol)),U=T===Ki?wE:Ki,M=n.rects.popper,A=n.elements[x?U:T],w=oR(kr(A)?A:A.contextElement||or(n.elements.popper),m,y,d),j=_o(n.elements.reference),H=Y0({reference:j,element:M,placement:u}),q=Md(Object.assign({},M,H)),Q=T===Ki?q:j,v={top:w.top-Q.top+k.top,bottom:Q.bottom-w.bottom+k.bottom,left:w.left-Q.left+k.left,right:Q.right-w.right+k.right},_=n.modifiersData.offset;if(T===Ki&&_){var K=_[u];Object.keys(v).forEach(function(Z){var it=[Pn,Hn].indexOf(Z)>=0?1:-1,F=[dn,Hn].indexOf(Z)>=0?"y":"x";v[Z]+=K[F]*it})}return v}function iR(n,r){r===void 0&&(r={});var i=r,l=i.placement,u=i.boundary,c=i.rootBoundary,d=i.padding,p=i.flipVariations,m=i.allowedAutoPlacements,h=m===void 0?U0:m,y=Uo(l),S=y?p?rv:rv.filter(function(x){return Uo(x)===y}):Ol,T=S.filter(function(x){return h.indexOf(x)>=0});T.length===0&&(T=S);var E=T.reduce(function(x,C){return x[C]=gl(n,{placement:C,boundary:u,rootBoundary:c,padding:d})[la(C)],x},{});return Object.keys(E).sort(function(x,C){return E[x]-E[C]})}function lR(n){if(la(n)===sp)return[];var r=iu(n);return[lv(n),r,lv(r)]}function sR(n){var r=n.state,i=n.options,l=n.name;if(!r.modifiersData[l]._skip){for(var u=i.mainAxis,c=u===void 0?!0:u,d=i.altAxis,p=d===void 0?!0:d,m=i.fallbackPlacements,h=i.padding,y=i.boundary,S=i.rootBoundary,T=i.altBoundary,E=i.flipVariations,x=E===void 0?!0:E,C=i.allowedAutoPlacements,D=r.options.placement,k=la(D),U=k===D,M=m||(U||!x?[iu(D)]:lR(D)),A=[D].concat(M).reduce(function(lt,ut){return lt.concat(la(ut)===sp?iR(r,{placement:ut,boundary:y,rootBoundary:S,padding:h,flipVariations:x,allowedAutoPlacements:C}):ut)},[]),w=r.rects.reference,j=r.rects.popper,H=new Map,q=!0,Q=A[0],v=0;v<A.length;v++){var _=A[v],K=la(_),Z=Uo(_)===$o,it=[dn,Hn].indexOf(K)>=0,F=it?"width":"height",N=gl(r,{placement:_,boundary:y,rootBoundary:S,altBoundary:T,padding:h}),V=it?Z?Pn:pn:Z?Hn:dn;w[F]>j[F]&&(V=iu(V));var ot=iu(V),W=[];if(c&&W.push(N[K]<=0),p&&W.push(N[V]<=0,N[ot]<=0),W.every(function(lt){return lt})){Q=_,q=!1;break}H.set(_,W)}if(q)for(var z=x?3:1,X=function(ut){var ft=A.find(function(Tt){var bt=H.get(Tt);if(bt)return bt.slice(0,ut).every(function(Ot){return Ot})});if(ft)return Q=ft,"break"},et=z;et>0;et--){var nt=X(et);if(nt==="break")break}r.placement!==Q&&(r.modifiersData[l]._skip=!0,r.placement=Q,r.reset=!0)}}const uR={name:"flip",enabled:!0,phase:"main",fn:sR,requiresIfExists:["offset"],data:{_skip:!1}};function uv(n,r,i){return i===void 0&&(i={x:0,y:0}),{top:n.top-r.height-i.y,right:n.right-r.width+i.x,bottom:n.bottom-r.height+i.y,left:n.left-r.width-i.x}}function cv(n){return[dn,Pn,Hn,pn].some(function(r){return n[r]>=0})}function cR(n){var r=n.state,i=n.name,l=r.rects.reference,u=r.rects.popper,c=r.modifiersData.preventOverflow,d=gl(r,{elementContext:"reference"}),p=gl(r,{altBoundary:!0}),m=uv(d,l),h=uv(p,u,c),y=cv(m),S=cv(h);r.modifiersData[i]={referenceClippingOffsets:m,popperEscapeOffsets:h,isReferenceHidden:y,hasPopperEscaped:S},r.attributes.popper=Object.assign({},r.attributes.popper,{"data-popper-reference-hidden":y,"data-popper-escaped":S})}const fR={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:cR};function dR(n,r,i){var l=la(n),u=[pn,dn].indexOf(l)>=0?-1:1,c=typeof i=="function"?i(Object.assign({},r,{placement:n})):i,d=c[0],p=c[1];return d=d||0,p=(p||0)*u,[pn,Pn].indexOf(l)>=0?{x:p,y:d}:{x:d,y:p}}function pR(n){var r=n.state,i=n.options,l=n.name,u=i.offset,c=u===void 0?[0,0]:u,d=U0.reduce(function(y,S){return y[S]=dR(S,r.rects,c),y},{}),p=d[r.placement],m=p.x,h=p.y;r.modifiersData.popperOffsets!=null&&(r.modifiersData.popperOffsets.x+=m,r.modifiersData.popperOffsets.y+=h),r.modifiersData[l]=d}const mR={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:pR};function hR(n){var r=n.state,i=n.name;r.modifiersData[i]=Y0({reference:r.rects.reference,element:r.rects.popper,placement:r.placement})}const gR={name:"popperOffsets",enabled:!0,phase:"read",fn:hR,data:{}};function yR(n){return n==="x"?"y":"x"}function vR(n){var r=n.state,i=n.options,l=n.name,u=i.mainAxis,c=u===void 0?!0:u,d=i.altAxis,p=d===void 0?!1:d,m=i.boundary,h=i.rootBoundary,y=i.altBoundary,S=i.padding,T=i.tether,E=T===void 0?!0:T,x=i.tetherOffset,C=x===void 0?0:x,D=gl(r,{boundary:m,rootBoundary:h,padding:S,altBoundary:y}),k=la(r.placement),U=Uo(r.placement),M=!U,A=fp(k),w=yR(A),j=r.modifiersData.popperOffsets,H=r.rects.reference,q=r.rects.popper,Q=typeof C=="function"?C(Object.assign({},r.rects,{placement:r.placement})):C,v=typeof Q=="number"?{mainAxis:Q,altAxis:Q}:Object.assign({mainAxis:0,altAxis:0},Q),_=r.modifiersData.offset?r.modifiersData.offset[r.placement]:null,K={x:0,y:0};if(j){if(c){var Z,it=A==="y"?dn:pn,F=A==="y"?Hn:Pn,N=A==="y"?"height":"width",V=j[A],ot=V+D[it],W=V-D[F],z=E?-q[N]/2:0,X=U===$o?H[N]:q[N],et=U===$o?-q[N]:-H[N],nt=r.elements.arrow,lt=E&&nt?cp(nt):{width:0,height:0},ut=r.modifiersData["arrow#persistent"]?r.modifiersData["arrow#persistent"].padding:P0(),ft=ut[it],Tt=ut[F],bt=il(0,H[N],lt[N]),Ot=M?H[N]/2-z-bt-ft-v.mainAxis:X-bt-ft-v.mainAxis,yt=M?-H[N]/2+z+bt+Tt+v.mainAxis:et+bt+Tt+v.mainAxis,wt=r.elements.arrow&&Al(r.elements.arrow),St=wt?A==="y"?wt.clientTop||0:wt.clientLeft||0:0,$t=(Z=_==null?void 0:_[A])!=null?Z:0,Et=V+Ot-$t-St,qt=V+yt-$t,Ce=il(E?hu(ot,Et):ot,V,E?Nr(W,qt):W);j[A]=Ce,K[A]=Ce-V}if(p){var Ut,Kt=A==="x"?dn:pn,Qt=A==="x"?Hn:Pn,Vt=j[w],jt=w==="y"?"height":"width",dt=Vt+D[Kt],_e=Vt-D[Qt],ie=[dn,pn].indexOf(k)!==-1,Ze=(Ut=_==null?void 0:_[w])!=null?Ut:0,Ue=ie?dt:Vt-H[jt]-q[jt]-Ze+v.altAxis,ee=ie?Vt+H[jt]+q[jt]-Ze-v.altAxis:_e,Te=E&&ie?qE(Ue,Vt,ee):il(E?Ue:dt,Vt,E?ee:_e);j[w]=Te,K[w]=Te-Vt}r.modifiersData[l]=K}}const bR={name:"preventOverflow",enabled:!0,phase:"main",fn:vR,requiresIfExists:["offset"]};function SR(n){return{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop}}function xR(n){return n===wn(n)||!Ln(n)?dp(n):SR(n)}function CR(n){var r=n.getBoundingClientRect(),i=jo(r.width)/n.offsetWidth||1,l=jo(r.height)/n.offsetHeight||1;return i!==1||l!==1}function TR(n,r,i){i===void 0&&(i=!1);var l=Ln(r),u=Ln(r)&&CR(r),c=or(r),d=_o(n,u,i),p={scrollLeft:0,scrollTop:0},m={x:0,y:0};return(l||!l&&!i)&&((sa(r)!=="body"||mp(c))&&(p=xR(r)),Ln(r)?(m=_o(r,!0),m.x+=r.clientLeft,m.y+=r.clientTop):c&&(m.x=pp(c))),{x:d.left+p.scrollLeft-m.x,y:d.top+p.scrollTop-m.y,width:d.width,height:d.height}}function ER(n){var r=new Map,i=new Set,l=[];n.forEach(function(c){r.set(c.name,c)});function u(c){i.add(c.name);var d=[].concat(c.requires||[],c.requiresIfExists||[]);d.forEach(function(p){if(!i.has(p)){var m=r.get(p);m&&u(m)}}),l.push(c)}return n.forEach(function(c){i.has(c.name)||u(c)}),l}function RR(n){var r=ER(n);return jE.reduce(function(i,l){return i.concat(r.filter(function(u){return u.phase===l}))},[])}function wR(n){var r;return function(){return r||(r=new Promise(function(i){Promise.resolve().then(function(){r=void 0,i(n())})})),r}}function OR(n){var r=n.reduce(function(i,l){var u=i[l.name];return i[l.name]=u?Object.assign({},u,l,{options:Object.assign({},u.options,l.options),data:Object.assign({},u.data,l.data)}):l,i},{});return Object.keys(r).map(function(i){return r[i]})}var fv={placement:"bottom",modifiers:[],strategy:"absolute"};function dv(){for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return!r.some(function(l){return!(l&&typeof l.getBoundingClientRect=="function")})}function AR(n){n===void 0&&(n={});var r=n,i=r.defaultModifiers,l=i===void 0?[]:i,u=r.defaultOptions,c=u===void 0?fv:u;return function(p,m,h){h===void 0&&(h=c);var y={placement:"bottom",orderedModifiers:[],options:Object.assign({},fv,c),modifiersData:{},elements:{reference:p,popper:m},attributes:{},styles:{}},S=[],T=!1,E={state:y,setOptions:function(k){var U=typeof k=="function"?k(y.options):k;C(),y.options=Object.assign({},c,y.options,U),y.scrollParents={reference:kr(p)?ll(p):p.contextElement?ll(p.contextElement):[],popper:ll(m)};var M=RR(OR([].concat(l,y.options.modifiers)));return y.orderedModifiers=M.filter(function(A){return A.enabled}),x(),E.update()},forceUpdate:function(){if(!T){var k=y.elements,U=k.reference,M=k.popper;if(dv(U,M)){y.rects={reference:TR(U,Al(M),y.options.strategy==="fixed"),popper:cp(M)},y.reset=!1,y.placement=y.options.placement,y.orderedModifiers.forEach(function(v){return y.modifiersData[v.name]=Object.assign({},v.data)});for(var A=0;A<y.orderedModifiers.length;A++){if(y.reset===!0){y.reset=!1,A=-1;continue}var w=y.orderedModifiers[A],j=w.fn,H=w.options,q=H===void 0?{}:H,Q=w.name;typeof j=="function"&&(y=j({state:y,options:q,name:Q,instance:E})||y)}}}},update:wR(function(){return new Promise(function(D){E.forceUpdate(),D(y)})}),destroy:function(){C(),T=!0}};if(!dv(p,m))return E;E.setOptions(h).then(function(D){!T&&h.onFirstUpdate&&h.onFirstUpdate(D)});function x(){y.orderedModifiers.forEach(function(D){var k=D.name,U=D.options,M=U===void 0?{}:U,A=D.effect;if(typeof A=="function"){var w=A({state:y,name:k,instance:E,options:M}),j=function(){};S.push(w||j)}})}function C(){S.forEach(function(D){return D()}),S=[]}return E}}var MR=[FE,gR,WE,LE,mR,uR,bR,XE,fR],zR=AR({defaultModifiers:MR});function X0(n){var S;const{elementType:r,externalSlotProps:i,ownerState:l,skipResolvingSlotProps:u=!1,...c}=n,d=u?{}:B0(i,l),{props:p,internalRef:m}=$0({...c,externalSlotProps:d}),h=Pe(m,d==null?void 0:d.ref,(S=n.additionalProps)==null?void 0:S.ref);return N0(r,{...p,ref:h},l)}function Vo(n){var r;return parseInt(R.version,10)>=19?((r=n==null?void 0:n.props)==null?void 0:r.ref)||null:(n==null?void 0:n.ref)||null}function DR(n){return typeof n=="function"?n():n}const I0=R.forwardRef(function(r,i){const{children:l,container:u,disablePortal:c=!1}=r,[d,p]=R.useState(null),m=Pe(R.isValidElement(l)?Vo(l):null,i);if(In(()=>{c||p(DR(u)||document.body)},[u,c]),In(()=>{if(d&&!c)return Qy(i,d),()=>{Qy(i,null)}},[i,d,c]),c){if(R.isValidElement(l)){const h={ref:m};return R.cloneElement(l,h)}return l}return d&&M0.createPortal(l,d)});function NR(n){return Dt("MuiPopper",n)}Mt("MuiPopper",["root"]);function BR(n,r){if(r==="ltr")return n;switch(n){case"bottom-end":return"bottom-start";case"bottom-start":return"bottom-end";case"top-end":return"top-start";case"top-start":return"top-end";default:return n}}function zd(n){return typeof n=="function"?n():n}function kR(n){return n.nodeType!==void 0}const $R=n=>{const{classes:r}=n;return Nt({root:["root"]},NR,r)},jR={},_R=R.forwardRef(function(r,i){const{anchorEl:l,children:u,direction:c,disablePortal:d,modifiers:p,open:m,placement:h,popperOptions:y,popperRef:S,slotProps:T={},slots:E={},TransitionProps:x,ownerState:C,...D}=r,k=R.useRef(null),U=Pe(k,i),M=R.useRef(null),A=Pe(M,S),w=R.useRef(A);In(()=>{w.current=A},[A]),R.useImperativeHandle(S,()=>M.current,[]);const j=BR(h,c),[H,q]=R.useState(j),[Q,v]=R.useState(zd(l));R.useEffect(()=>{M.current&&M.current.forceUpdate()}),R.useEffect(()=>{l&&v(zd(l))},[l]),In(()=>{if(!Q||!m)return;const F=ot=>{q(ot.placement)};let N=[{name:"preventOverflow",options:{altBoundary:d}},{name:"flip",options:{altBoundary:d}},{name:"onUpdate",enabled:!0,phase:"afterWrite",fn:({state:ot})=>{F(ot)}}];p!=null&&(N=N.concat(p)),y&&y.modifiers!=null&&(N=N.concat(y.modifiers));const V=zR(Q,k.current,{placement:j,...y,modifiers:N});return w.current(V),()=>{V.destroy(),w.current(null)}},[Q,d,p,m,y,j]);const _={placement:H};x!==null&&(_.TransitionProps=x);const K=$R(r),Z=E.root??"div",it=X0({elementType:Z,externalSlotProps:T.root,externalForwardedProps:D,additionalProps:{role:"tooltip",ref:U},ownerState:r,className:K.root});return O.jsx(Z,{...it,children:typeof u=="function"?u(_):u})}),UR=R.forwardRef(function(r,i){const{anchorEl:l,children:u,container:c,direction:d="ltr",disablePortal:p=!1,keepMounted:m=!1,modifiers:h,open:y,placement:S="bottom",popperOptions:T=jR,popperRef:E,style:x,transition:C=!1,slotProps:D={},slots:k={},...U}=r,[M,A]=R.useState(!0),w=()=>{A(!1)},j=()=>{A(!0)};if(!m&&!y&&(!C||M))return null;let H;if(c)H=c;else if(l){const v=zd(l);H=v&&kR(v)?Rn(v).body:Rn(null).body}const q=!y&&m&&(!C||M)?"none":void 0,Q=C?{in:y,onEnter:w,onExited:j}:void 0;return O.jsx(I0,{disablePortal:p,container:H,children:O.jsx(_R,{anchorEl:l,direction:d,disablePortal:p,modifiers:h,ref:i,open:C?!M:y,placement:S,popperOptions:T,popperRef:E,slotProps:D,slots:k,...U,style:{position:"fixed",top:0,left:0,display:q,...x},TransitionProps:Q,children:u})})}),LR=ct(UR,{name:"MuiPopper",slot:"Root"})({}),K0=R.forwardRef(function(r,i){const l=tp(),u=Bt({props:r,name:"MuiPopper"}),{anchorEl:c,component:d,components:p,componentsProps:m,container:h,disablePortal:y,keepMounted:S,modifiers:T,open:E,placement:x,popperOptions:C,popperRef:D,transition:k,slots:U,slotProps:M,...A}=u,w=(U==null?void 0:U.root)??(p==null?void 0:p.Root),j={anchorEl:c,container:h,disablePortal:y,keepMounted:S,modifiers:T,open:E,placement:x,popperOptions:C,popperRef:D,transition:k,...A};return O.jsx(LR,{as:d,direction:l?"rtl":"ltr",slots:{root:w},slotProps:M??m,...j,ref:i})}),HR=Gn(O.jsx("path",{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"}));function PR(n){return Dt("MuiChip",n)}const Pt=Mt("MuiChip",["root","sizeSmall","sizeMedium","colorDefault","colorError","colorInfo","colorPrimary","colorSecondary","colorSuccess","colorWarning","disabled","clickable","clickableColorPrimary","clickableColorSecondary","deletable","deletableColorPrimary","deletableColorSecondary","outlined","filled","outlinedPrimary","outlinedSecondary","filledPrimary","filledSecondary","avatar","avatarSmall","avatarMedium","avatarColorPrimary","avatarColorSecondary","icon","iconSmall","iconMedium","iconColorPrimary","iconColorSecondary","label","labelSmall","labelMedium","deleteIcon","deleteIconSmall","deleteIconMedium","deleteIconColorPrimary","deleteIconColorSecondary","deleteIconOutlinedColorPrimary","deleteIconOutlinedColorSecondary","deleteIconFilledColorPrimary","deleteIconFilledColorSecondary","focusVisible"]),qR=n=>{const{classes:r,disabled:i,size:l,color:u,iconColor:c,onDelete:d,clickable:p,variant:m}=n,h={root:["root",m,i&&"disabled",`size${st(l)}`,`color${st(u)}`,p&&"clickable",p&&`clickableColor${st(u)}`,d&&"deletable",d&&`deletableColor${st(u)}`,`${m}${st(u)}`],label:["label",`label${st(l)}`],avatar:["avatar",`avatar${st(l)}`,`avatarColor${st(u)}`],icon:["icon",`icon${st(l)}`,`iconColor${st(c)}`],deleteIcon:["deleteIcon",`deleteIcon${st(l)}`,`deleteIconColor${st(u)}`,`deleteIcon${st(m)}Color${st(u)}`]};return Nt(h,PR,r)},GR=ct("div",{name:"MuiChip",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n,{color:l,iconColor:u,clickable:c,onDelete:d,size:p,variant:m}=i;return[{[`& .${Pt.avatar}`]:r.avatar},{[`& .${Pt.avatar}`]:r[`avatar${st(p)}`]},{[`& .${Pt.avatar}`]:r[`avatarColor${st(l)}`]},{[`& .${Pt.icon}`]:r.icon},{[`& .${Pt.icon}`]:r[`icon${st(p)}`]},{[`& .${Pt.icon}`]:r[`iconColor${st(u)}`]},{[`& .${Pt.deleteIcon}`]:r.deleteIcon},{[`& .${Pt.deleteIcon}`]:r[`deleteIcon${st(p)}`]},{[`& .${Pt.deleteIcon}`]:r[`deleteIconColor${st(l)}`]},{[`& .${Pt.deleteIcon}`]:r[`deleteIcon${st(m)}Color${st(l)}`]},r.root,r[`size${st(p)}`],r[`color${st(l)}`],c&&r.clickable,c&&l!=="default"&&r[`clickableColor${st(l)})`],d&&r.deletable,d&&l!=="default"&&r[`deletableColor${st(l)}`],r[m],r[`${m}${st(l)}`]]}})(Gt(({theme:n})=>{const r=n.palette.mode==="light"?n.palette.grey[700]:n.palette.grey[300];return{maxWidth:"100%",fontFamily:n.typography.fontFamily,fontSize:n.typography.pxToRem(13),display:"inline-flex",alignItems:"center",justifyContent:"center",height:32,color:(n.vars||n).palette.text.primary,backgroundColor:(n.vars||n).palette.action.selected,borderRadius:32/2,whiteSpace:"nowrap",transition:n.transitions.create(["background-color","box-shadow"]),cursor:"unset",outline:0,textDecoration:"none",border:0,padding:0,verticalAlign:"middle",boxSizing:"border-box",[`&.${Pt.disabled}`]:{opacity:(n.vars||n).palette.action.disabledOpacity,pointerEvents:"none"},[`& .${Pt.avatar}`]:{marginLeft:5,marginRight:-6,width:24,height:24,color:n.vars?n.vars.palette.Chip.defaultAvatarColor:r,fontSize:n.typography.pxToRem(12)},[`& .${Pt.avatarColorPrimary}`]:{color:(n.vars||n).palette.primary.contrastText,backgroundColor:(n.vars||n).palette.primary.dark},[`& .${Pt.avatarColorSecondary}`]:{color:(n.vars||n).palette.secondary.contrastText,backgroundColor:(n.vars||n).palette.secondary.dark},[`& .${Pt.avatarSmall}`]:{marginLeft:4,marginRight:-4,width:18,height:18,fontSize:n.typography.pxToRem(10)},[`& .${Pt.icon}`]:{marginLeft:5,marginRight:-6},[`& .${Pt.deleteIcon}`]:{WebkitTapHighlightColor:"transparent",color:n.vars?`rgba(${n.vars.palette.text.primaryChannel} / 0.26)`:ve(n.palette.text.primary,.26),fontSize:22,cursor:"pointer",margin:"0 5px 0 -6px","&:hover":{color:n.vars?`rgba(${n.vars.palette.text.primaryChannel} / 0.4)`:ve(n.palette.text.primary,.4)}},variants:[{props:{size:"small"},style:{height:24,[`& .${Pt.icon}`]:{fontSize:18,marginLeft:4,marginRight:-4},[`& .${Pt.deleteIcon}`]:{fontSize:16,marginRight:4,marginLeft:-4}}},...Object.entries(n.palette).filter(rn(["contrastText"])).map(([i])=>({props:{color:i},style:{backgroundColor:(n.vars||n).palette[i].main,color:(n.vars||n).palette[i].contrastText,[`& .${Pt.deleteIcon}`]:{color:n.vars?`rgba(${n.vars.palette[i].contrastTextChannel} / 0.7)`:ve(n.palette[i].contrastText,.7),"&:hover, &:active":{color:(n.vars||n).palette[i].contrastText}}}})),{props:i=>i.iconColor===i.color,style:{[`& .${Pt.icon}`]:{color:n.vars?n.vars.palette.Chip.defaultIconColor:r}}},{props:i=>i.iconColor===i.color&&i.color!=="default",style:{[`& .${Pt.icon}`]:{color:"inherit"}}},{props:{onDelete:!0},style:{[`&.${Pt.focusVisible}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.action.selectedChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.focusOpacity}))`:ve(n.palette.action.selected,n.palette.action.selectedOpacity+n.palette.action.focusOpacity)}}},...Object.entries(n.palette).filter(rn(["dark"])).map(([i])=>({props:{color:i,onDelete:!0},style:{[`&.${Pt.focusVisible}`]:{background:(n.vars||n).palette[i].dark}}})),{props:{clickable:!0},style:{userSelect:"none",WebkitTapHighlightColor:"transparent",cursor:"pointer","&:hover":{backgroundColor:n.vars?`rgba(${n.vars.palette.action.selectedChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.hoverOpacity}))`:ve(n.palette.action.selected,n.palette.action.selectedOpacity+n.palette.action.hoverOpacity)},[`&.${Pt.focusVisible}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.action.selectedChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.focusOpacity}))`:ve(n.palette.action.selected,n.palette.action.selectedOpacity+n.palette.action.focusOpacity)},"&:active":{boxShadow:(n.vars||n).shadows[1]}}},...Object.entries(n.palette).filter(rn(["dark"])).map(([i])=>({props:{color:i,clickable:!0},style:{[`&:hover, &.${Pt.focusVisible}`]:{backgroundColor:(n.vars||n).palette[i].dark}}})),{props:{variant:"outlined"},style:{backgroundColor:"transparent",border:n.vars?`1px solid ${n.vars.palette.Chip.defaultBorder}`:`1px solid ${n.palette.mode==="light"?n.palette.grey[400]:n.palette.grey[700]}`,[`&.${Pt.clickable}:hover`]:{backgroundColor:(n.vars||n).palette.action.hover},[`&.${Pt.focusVisible}`]:{backgroundColor:(n.vars||n).palette.action.focus},[`& .${Pt.avatar}`]:{marginLeft:4},[`& .${Pt.avatarSmall}`]:{marginLeft:2},[`& .${Pt.icon}`]:{marginLeft:4},[`& .${Pt.iconSmall}`]:{marginLeft:2},[`& .${Pt.deleteIcon}`]:{marginRight:5},[`& .${Pt.deleteIconSmall}`]:{marginRight:3}}},...Object.entries(n.palette).filter(rn()).map(([i])=>({props:{variant:"outlined",color:i},style:{color:(n.vars||n).palette[i].main,border:`1px solid ${n.vars?`rgba(${n.vars.palette[i].mainChannel} / 0.7)`:ve(n.palette[i].main,.7)}`,[`&.${Pt.clickable}:hover`]:{backgroundColor:n.vars?`rgba(${n.vars.palette[i].mainChannel} / ${n.vars.palette.action.hoverOpacity})`:ve(n.palette[i].main,n.palette.action.hoverOpacity)},[`&.${Pt.focusVisible}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette[i].mainChannel} / ${n.vars.palette.action.focusOpacity})`:ve(n.palette[i].main,n.palette.action.focusOpacity)},[`& .${Pt.deleteIcon}`]:{color:n.vars?`rgba(${n.vars.palette[i].mainChannel} / 0.7)`:ve(n.palette[i].main,.7),"&:hover, &:active":{color:(n.vars||n).palette[i].main}}}}))]}})),VR=ct("span",{name:"MuiChip",slot:"Label",overridesResolver:(n,r)=>{const{ownerState:i}=n,{size:l}=i;return[r.label,r[`label${st(l)}`]]}})({overflow:"hidden",textOverflow:"ellipsis",paddingLeft:12,paddingRight:12,whiteSpace:"nowrap",variants:[{props:{variant:"outlined"},style:{paddingLeft:11,paddingRight:11}},{props:{size:"small"},style:{paddingLeft:8,paddingRight:8}},{props:{size:"small",variant:"outlined"},style:{paddingLeft:7,paddingRight:7}}]});function pv(n){return n.key==="Backspace"||n.key==="Delete"}const al=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiChip"}),{avatar:u,className:c,clickable:d,color:p="default",component:m,deleteIcon:h,disabled:y=!1,icon:S,label:T,onClick:E,onDelete:x,onKeyDown:C,onKeyUp:D,size:k="medium",variant:U="filled",tabIndex:M,skipFocusWhenDisabled:A=!1,...w}=l,j=R.useRef(null),H=Pe(j,i),q=W=>{W.stopPropagation(),x&&x(W)},Q=W=>{W.currentTarget===W.target&&pv(W)&&W.preventDefault(),C&&C(W)},v=W=>{W.currentTarget===W.target&&x&&pv(W)&&x(W),D&&D(W)},_=d!==!1&&E?!0:d,K=_||x?ko:m||"div",Z={...l,component:K,disabled:y,size:k,color:p,iconColor:R.isValidElement(S)&&S.props.color||p,onDelete:!!x,clickable:_,variant:U},it=qR(Z),F=K===ko?{component:m||"div",focusVisibleClassName:it.focusVisible,...x&&{disableRipple:!0}}:{};let N=null;x&&(N=h&&R.isValidElement(h)?R.cloneElement(h,{className:gt(h.props.className,it.deleteIcon),onClick:q}):O.jsx(HR,{className:it.deleteIcon,onClick:q}));let V=null;u&&R.isValidElement(u)&&(V=R.cloneElement(u,{className:gt(it.avatar,u.props.className)}));let ot=null;return S&&R.isValidElement(S)&&(ot=R.cloneElement(S,{className:gt(it.icon,S.props.className)})),O.jsxs(GR,{as:K,className:gt(it.root,c),disabled:_&&y?!0:void 0,onClick:E,onKeyDown:Q,onKeyUp:v,ref:H,tabIndex:A&&y?-1:M,ownerState:Z,...F,...w,children:[V||ot,O.jsx(VR,{className:it.label,ownerState:Z,children:T}),N]})});function Qs(n){return parseInt(n,10)||0}const YR={shadow:{visibility:"hidden",position:"absolute",overflow:"hidden",height:0,top:0,left:0,transform:"translateZ(0)"}};function XR(n){for(const r in n)return!1;return!0}function mv(n){return XR(n)||n.outerHeightStyle===0&&!n.overflowing}const IR=R.forwardRef(function(r,i){const{onChange:l,maxRows:u,minRows:c=1,style:d,value:p,...m}=r,{current:h}=R.useRef(p!=null),y=R.useRef(null),S=Pe(i,y),T=R.useRef(null),E=R.useRef(null),x=R.useCallback(()=>{const M=y.current,A=E.current;if(!M||!A)return;const j=Da(M).getComputedStyle(M);if(j.width==="0px")return{outerHeightStyle:0,overflowing:!1};A.style.width=j.width,A.value=M.value||r.placeholder||"x",A.value.slice(-1)===`
`&&(A.value+=" ");const H=j.boxSizing,q=Qs(j.paddingBottom)+Qs(j.paddingTop),Q=Qs(j.borderBottomWidth)+Qs(j.borderTopWidth),v=A.scrollHeight;A.value="x";const _=A.scrollHeight;let K=v;c&&(K=Math.max(Number(c)*_,K)),u&&(K=Math.min(Number(u)*_,K)),K=Math.max(K,_);const Z=K+(H==="border-box"?q+Q:0),it=Math.abs(K-v)<=1;return{outerHeightStyle:Z,overflowing:it}},[u,c,r.placeholder]),C=Aa(()=>{const M=y.current,A=x();if(!M||!A||mv(A))return!1;const w=A.outerHeightStyle;return T.current!=null&&T.current!==w}),D=R.useCallback(()=>{const M=y.current,A=x();if(!M||!A||mv(A))return;const w=A.outerHeightStyle;T.current!==w&&(T.current=w,M.style.height=`${w}px`),M.style.overflow=A.overflowing?"hidden":""},[x]),k=R.useRef(-1);In(()=>{const M=w0(D),A=y==null?void 0:y.current;if(!A)return;const w=Da(A);w.addEventListener("resize",M);let j;return typeof ResizeObserver<"u"&&(j=new ResizeObserver(()=>{C()&&(j.unobserve(A),cancelAnimationFrame(k.current),D(),k.current=requestAnimationFrame(()=>{j.observe(A)}))}),j.observe(A)),()=>{M.clear(),cancelAnimationFrame(k.current),w.removeEventListener("resize",M),j&&j.disconnect()}},[x,D,C]),In(()=>{D()});const U=M=>{h||D();const A=M.target,w=A.value.length,j=A.value.endsWith(`
`),H=A.selectionStart===w;j&&H&&A.setSelectionRange(w,w),l&&l(M)};return O.jsxs(R.Fragment,{children:[O.jsx("textarea",{value:p,onChange:U,ref:S,rows:c,style:d,...m}),O.jsx("textarea",{"aria-hidden":!0,className:r.className,readOnly:!0,ref:E,tabIndex:-1,style:{...YR.shadow,...d,paddingTop:0,paddingBottom:0}})]})});function Dd(n){return typeof n=="string"}function Yo({props:n,states:r,muiFormControl:i}){return r.reduce((l,u)=>(l[u]=n[u],i&&typeof n[u]>"u"&&(l[u]=i[u]),l),{})}const hp=R.createContext(void 0);function Xo(){return R.useContext(hp)}function hv(n){return n!=null&&!(Array.isArray(n)&&n.length===0)}function gu(n,r=!1){return n&&(hv(n.value)&&n.value!==""||r&&hv(n.defaultValue)&&n.defaultValue!=="")}function KR(n){return n.startAdornment}function QR(n){return Dt("MuiInputBase",n)}const Lo=Mt("MuiInputBase",["root","formControl","focused","disabled","adornedStart","adornedEnd","error","sizeSmall","multiline","colorSecondary","fullWidth","hiddenLabel","readOnly","input","inputSizeSmall","inputMultiline","inputTypeSearch","inputAdornedStart","inputAdornedEnd","inputHiddenLabel"]);var gv;const Bu=(n,r)=>{const{ownerState:i}=n;return[r.root,i.formControl&&r.formControl,i.startAdornment&&r.adornedStart,i.endAdornment&&r.adornedEnd,i.error&&r.error,i.size==="small"&&r.sizeSmall,i.multiline&&r.multiline,i.color&&r[`color${st(i.color)}`],i.fullWidth&&r.fullWidth,i.hiddenLabel&&r.hiddenLabel]},ku=(n,r)=>{const{ownerState:i}=n;return[r.input,i.size==="small"&&r.inputSizeSmall,i.multiline&&r.inputMultiline,i.type==="search"&&r.inputTypeSearch,i.startAdornment&&r.inputAdornedStart,i.endAdornment&&r.inputAdornedEnd,i.hiddenLabel&&r.inputHiddenLabel]},WR=n=>{const{classes:r,color:i,disabled:l,error:u,endAdornment:c,focused:d,formControl:p,fullWidth:m,hiddenLabel:h,multiline:y,readOnly:S,size:T,startAdornment:E,type:x}=n,C={root:["root",`color${st(i)}`,l&&"disabled",u&&"error",m&&"fullWidth",d&&"focused",p&&"formControl",T&&T!=="medium"&&`size${st(T)}`,y&&"multiline",E&&"adornedStart",c&&"adornedEnd",h&&"hiddenLabel",S&&"readOnly"],input:["input",l&&"disabled",x==="search"&&"inputTypeSearch",y&&"inputMultiline",T==="small"&&"inputSizeSmall",h&&"inputHiddenLabel",E&&"inputAdornedStart",c&&"inputAdornedEnd",S&&"readOnly"]};return Nt(C,QR,r)},$u=ct("div",{name:"MuiInputBase",slot:"Root",overridesResolver:Bu})(Gt(({theme:n})=>({...n.typography.body1,color:(n.vars||n).palette.text.primary,lineHeight:"1.4375em",boxSizing:"border-box",position:"relative",cursor:"text",display:"inline-flex",alignItems:"center",[`&.${Lo.disabled}`]:{color:(n.vars||n).palette.text.disabled,cursor:"default"},variants:[{props:({ownerState:r})=>r.multiline,style:{padding:"4px 0 5px"}},{props:({ownerState:r,size:i})=>r.multiline&&i==="small",style:{paddingTop:1}},{props:({ownerState:r})=>r.fullWidth,style:{width:"100%"}}]}))),ju=ct("input",{name:"MuiInputBase",slot:"Input",overridesResolver:ku})(Gt(({theme:n})=>{const r=n.palette.mode==="light",i={color:"currentColor",...n.vars?{opacity:n.vars.opacity.inputPlaceholder}:{opacity:r?.42:.5},transition:n.transitions.create("opacity",{duration:n.transitions.duration.shorter})},l={opacity:"0 !important"},u=n.vars?{opacity:n.vars.opacity.inputPlaceholder}:{opacity:r?.42:.5};return{font:"inherit",letterSpacing:"inherit",color:"currentColor",padding:"4px 0 5px",border:0,boxSizing:"content-box",background:"none",height:"1.4375em",margin:0,WebkitTapHighlightColor:"transparent",display:"block",minWidth:0,width:"100%","&::-webkit-input-placeholder":i,"&::-moz-placeholder":i,"&::-ms-input-placeholder":i,"&:focus":{outline:0},"&:invalid":{boxShadow:"none"},"&::-webkit-search-decoration":{WebkitAppearance:"none"},[`label[data-shrink=false] + .${Lo.formControl} &`]:{"&::-webkit-input-placeholder":l,"&::-moz-placeholder":l,"&::-ms-input-placeholder":l,"&:focus::-webkit-input-placeholder":u,"&:focus::-moz-placeholder":u,"&:focus::-ms-input-placeholder":u},[`&.${Lo.disabled}`]:{opacity:1,WebkitTextFillColor:(n.vars||n).palette.text.disabled},variants:[{props:({ownerState:c})=>!c.disableInjectingGlobalStyles,style:{animationName:"mui-auto-fill-cancel",animationDuration:"10ms","&:-webkit-autofill":{animationDuration:"5000s",animationName:"mui-auto-fill"}}},{props:{size:"small"},style:{paddingTop:1}},{props:({ownerState:c})=>c.multiline,style:{height:"auto",resize:"none",padding:0,paddingTop:0}},{props:{type:"search"},style:{MozAppearance:"textfield"}}]}})),yv=op({"@keyframes mui-auto-fill":{from:{display:"block"}},"@keyframes mui-auto-fill-cancel":{from:{display:"block"}}}),gp=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiInputBase"}),{"aria-describedby":u,autoComplete:c,autoFocus:d,className:p,color:m,components:h={},componentsProps:y={},defaultValue:S,disabled:T,disableInjectingGlobalStyles:E,endAdornment:x,error:C,fullWidth:D=!1,id:k,inputComponent:U="input",inputProps:M={},inputRef:A,margin:w,maxRows:j,minRows:H,multiline:q=!1,name:Q,onBlur:v,onChange:_,onClick:K,onFocus:Z,onKeyDown:it,onKeyUp:F,placeholder:N,readOnly:V,renderSuffix:ot,rows:W,size:z,slotProps:X={},slots:et={},startAdornment:nt,type:lt="text",value:ut,...ft}=l,Tt=M.value!=null?M.value:ut,{current:bt}=R.useRef(Tt!=null),Ot=R.useRef(),yt=R.useCallback(mt=>{},[]),wt=Pe(Ot,A,M.ref,yt),[St,$t]=R.useState(!1),Et=Xo(),qt=Yo({props:l,muiFormControl:Et,states:["color","disabled","error","hiddenLabel","size","required","filled"]});qt.focused=Et?Et.focused:St,R.useEffect(()=>{!Et&&T&&St&&($t(!1),v&&v())},[Et,T,St,v]);const Ce=Et&&Et.onFilled,Ut=Et&&Et.onEmpty,Kt=R.useCallback(mt=>{gu(mt)?Ce&&Ce():Ut&&Ut()},[Ce,Ut]);In(()=>{bt&&Kt({value:Tt})},[Tt,Kt,bt]);const Qt=mt=>{Z&&Z(mt),M.onFocus&&M.onFocus(mt),Et&&Et.onFocus?Et.onFocus(mt):$t(!0)},Vt=mt=>{v&&v(mt),M.onBlur&&M.onBlur(mt),Et&&Et.onBlur?Et.onBlur(mt):$t(!1)},jt=(mt,...Re)=>{if(!bt){const ae=mt.target||Ot.current;if(ae==null)throw new Error(Ma(1));Kt({value:ae.value})}M.onChange&&M.onChange(mt,...Re),_&&_(mt,...Re)};R.useEffect(()=>{Kt(Ot.current)},[]);const dt=mt=>{Ot.current&&mt.currentTarget===mt.target&&Ot.current.focus(),K&&K(mt)};let _e=U,ie=M;q&&_e==="input"&&(W?ie={type:void 0,minRows:W,maxRows:W,...ie}:ie={type:void 0,maxRows:j,minRows:H,...ie},_e=IR);const Ze=mt=>{Kt(mt.animationName==="mui-auto-fill-cancel"?Ot.current:{value:"x"})};R.useEffect(()=>{Et&&Et.setAdornedStart(!!nt)},[Et,nt]);const Ue={...l,color:qt.color||"primary",disabled:qt.disabled,endAdornment:x,error:qt.error,focused:qt.focused,formControl:Et,fullWidth:D,hiddenLabel:qt.hiddenLabel,multiline:q,size:qt.size,startAdornment:nt,type:lt},ee=WR(Ue),Te=et.root||h.Root||$u,ne=X.root||y.root||{},le=et.input||h.Input||ju;return ie={...ie,...X.input??y.input},O.jsxs(R.Fragment,{children:[!E&&typeof yv=="function"&&(gv||(gv=O.jsx(yv,{}))),O.jsxs(Te,{...ne,ref:i,onClick:dt,...ft,...!Dd(Te)&&{ownerState:{...Ue,...ne.ownerState}},className:gt(ee.root,ne.className,p,V&&"MuiInputBase-readOnly"),children:[nt,O.jsx(hp.Provider,{value:null,children:O.jsx(le,{"aria-invalid":qt.error,"aria-describedby":u,autoComplete:c,autoFocus:d,defaultValue:S,disabled:qt.disabled,id:k,onAnimationStart:Ze,name:Q,placeholder:N,readOnly:V,required:qt.required,rows:W,value:Tt,onKeyDown:it,onKeyUp:F,type:lt,...ie,...!Dd(le)&&{as:_e,ownerState:{...Ue,...ie.ownerState}},ref:wt,className:gt(ee.input,ie.className,V&&"MuiInputBase-readOnly"),onBlur:Vt,onChange:jt,onFocus:Qt})}),x,ot?ot({...qt,startAdornment:nt}):null]})]})});function ZR(n){return Dt("MuiInput",n)}const Qi={...Lo,...Mt("MuiInput",["root","underline","input"])};function FR(n){return Dt("MuiOutlinedInput",n)}const ta={...Lo,...Mt("MuiOutlinedInput",["root","notchedOutline","input"])};function JR(n){return Dt("MuiFilledInput",n)}const Rr={...Lo,...Mt("MuiFilledInput",["root","underline","input","adornedStart","adornedEnd","sizeSmall","multiline","hiddenLabel"])},tw=Gn(O.jsx("path",{d:"M7 10l5 5 5-5z"})),ew=Gn(O.jsx("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}));function nw(n){return Dt("MuiAvatar",n)}Mt("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);const aw=n=>{const{classes:r,variant:i,colorDefault:l}=n;return Nt({root:["root",i,l&&"colorDefault"],img:["img"],fallback:["fallback"]},nw,r)},rw=ct("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.root,r[i.variant],i.colorDefault&&r.colorDefault]}})(Gt(({theme:n})=>({position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:n.typography.fontFamily,fontSize:n.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(n.vars||n).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(n.vars||n).palette.background.default,...n.vars?{backgroundColor:n.vars.palette.Avatar.defaultBg}:{backgroundColor:n.palette.grey[400],...n.applyStyles("dark",{backgroundColor:n.palette.grey[600]})}}}]}))),ow=ct("img",{name:"MuiAvatar",slot:"Img"})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),iw=ct(ew,{name:"MuiAvatar",slot:"Fallback"})({width:"75%",height:"75%"});function lw({crossOrigin:n,referrerPolicy:r,src:i,srcSet:l}){const[u,c]=R.useState(!1);return R.useEffect(()=>{if(!i&&!l)return;c(!1);let d=!0;const p=new Image;return p.onload=()=>{d&&c("loaded")},p.onerror=()=>{d&&c("error")},p.crossOrigin=n,p.referrerPolicy=r,p.src=i,l&&(p.srcset=l),()=>{d=!1}},[n,r,i,l]),u}const sw=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiAvatar"}),{alt:u,children:c,className:d,component:p="div",slots:m={},slotProps:h={},imgProps:y,sizes:S,src:T,srcSet:E,variant:x="circular",...C}=l;let D=null;const k={...l,component:p,variant:x},U=lw({...y,...typeof h.img=="function"?h.img(k):h.img,src:T,srcSet:E}),M=T||E,A=M&&U!=="error";k.colorDefault=!A,delete k.ownerState;const w=aw(k),[j,H]=de("root",{ref:i,className:gt(w.root,d),elementType:rw,externalForwardedProps:{slots:m,slotProps:h,component:p,...C},ownerState:k}),[q,Q]=de("img",{className:w.img,elementType:ow,externalForwardedProps:{slots:m,slotProps:{img:{...y,...h.img}}},additionalProps:{alt:u,src:T,srcSet:E,sizes:S},ownerState:k}),[v,_]=de("fallback",{className:w.fallback,elementType:iw,externalForwardedProps:{slots:m,slotProps:h},shouldForwardComponentProp:!0,ownerState:k});return A?D=O.jsx(q,{...Q}):c||c===0?D=c:M&&u?D=u[0]:D=O.jsx(v,{..._}),O.jsx(j,{...H,children:D})}),uw={entering:{opacity:1},entered:{opacity:1}},Nd=R.forwardRef(function(r,i){const l=qo(),u={enter:l.transitions.duration.enteringScreen,exit:l.transitions.duration.leavingScreen},{addEndListener:c,appear:d=!0,children:p,easing:m,in:h,onEnter:y,onEntered:S,onEntering:T,onExit:E,onExited:x,onExiting:C,style:D,timeout:k=u,TransitionComponent:U=ua,...M}=r,A=R.useRef(null),w=Pe(A,Vo(p),i),j=it=>F=>{if(it){const N=A.current;F===void 0?it(N):it(N,F)}},H=j(T),q=j((it,F)=>{D0(it);const N=du({style:D,timeout:k,easing:m},{mode:"enter"});it.style.webkitTransition=l.transitions.create("opacity",N),it.style.transition=l.transitions.create("opacity",N),y&&y(it,F)}),Q=j(S),v=j(C),_=j(it=>{const F=du({style:D,timeout:k,easing:m},{mode:"exit"});it.style.webkitTransition=l.transitions.create("opacity",F),it.style.transition=l.transitions.create("opacity",F),E&&E(it)}),K=j(x),Z=it=>{c&&c(A.current,it)};return O.jsx(U,{appear:d,in:h,nodeRef:A,onEnter:q,onEntered:Q,onEntering:H,onExit:_,onExited:K,onExiting:v,addEndListener:Z,timeout:k,...M,children:(it,{ownerState:F,...N})=>R.cloneElement(p,{style:{opacity:0,visibility:it==="exited"&&!h?"hidden":void 0,...uw[it],...D,...p.props.style},ref:w,...N})})});function cw(n){return Dt("MuiBackdrop",n)}Mt("MuiBackdrop",["root","invisible"]);const fw=n=>{const{classes:r,invisible:i}=n;return Nt({root:["root",i&&"invisible"]},cw,r)},dw=ct("div",{name:"MuiBackdrop",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.root,i.invisible&&r.invisible]}})({position:"fixed",display:"flex",alignItems:"center",justifyContent:"center",right:0,bottom:0,top:0,left:0,backgroundColor:"rgba(0, 0, 0, 0.5)",WebkitTapHighlightColor:"transparent",variants:[{props:{invisible:!0},style:{backgroundColor:"transparent"}}]}),Q0=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiBackdrop"}),{children:u,className:c,component:d="div",invisible:p=!1,open:m,components:h={},componentsProps:y={},slotProps:S={},slots:T={},TransitionComponent:E,transitionDuration:x,...C}=l,D={...l,component:d,invisible:p},k=fw(D),U={transition:E,root:h.Root,...T},M={...y,...S},A={slots:U,slotProps:M},[w,j]=de("root",{elementType:dw,externalForwardedProps:A,className:gt(k.root,c),ownerState:D}),[H,q]=de("transition",{elementType:Nd,externalForwardedProps:A,ownerState:D});return O.jsx(H,{in:m,timeout:x,...C,...q,children:O.jsx(w,{"aria-hidden":!0,...j,classes:k,ref:i,children:u})})}),pw=Mt("MuiBox",["root"]),mw=zu(),Qe=D2({themeId:oa,defaultTheme:mw,defaultClassName:pw.root,generateClassName:o0.generate});function hw(n){return Dt("MuiButton",n)}const wr=Mt("MuiButton",["root","text","textInherit","textPrimary","textSecondary","textSuccess","textError","textInfo","textWarning","outlined","outlinedInherit","outlinedPrimary","outlinedSecondary","outlinedSuccess","outlinedError","outlinedInfo","outlinedWarning","contained","containedInherit","containedPrimary","containedSecondary","containedSuccess","containedError","containedInfo","containedWarning","disableElevation","focusVisible","disabled","colorInherit","colorPrimary","colorSecondary","colorSuccess","colorError","colorInfo","colorWarning","textSizeSmall","textSizeMedium","textSizeLarge","outlinedSizeSmall","outlinedSizeMedium","outlinedSizeLarge","containedSizeSmall","containedSizeMedium","containedSizeLarge","sizeMedium","sizeSmall","sizeLarge","fullWidth","startIcon","endIcon","icon","iconSizeSmall","iconSizeMedium","iconSizeLarge","loading","loadingWrapper","loadingIconPlaceholder","loadingIndicator","loadingPositionCenter","loadingPositionStart","loadingPositionEnd"]),gw=R.createContext({}),yw=R.createContext(void 0),vw=n=>{const{color:r,disableElevation:i,fullWidth:l,size:u,variant:c,loading:d,loadingPosition:p,classes:m}=n,h={root:["root",d&&"loading",c,`${c}${st(r)}`,`size${st(u)}`,`${c}Size${st(u)}`,`color${st(r)}`,i&&"disableElevation",l&&"fullWidth",d&&`loadingPosition${st(p)}`],startIcon:["icon","startIcon",`iconSize${st(u)}`],endIcon:["icon","endIcon",`iconSize${st(u)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]},y=Nt(h,hw,m);return{...m,...y}},W0=[{props:{size:"small"},style:{"& > *:nth-of-type(1)":{fontSize:18}}},{props:{size:"medium"},style:{"& > *:nth-of-type(1)":{fontSize:20}}},{props:{size:"large"},style:{"& > *:nth-of-type(1)":{fontSize:22}}}],bw=ct(ko,{shouldForwardProp:n=>On(n)||n==="classes",name:"MuiButton",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.root,r[i.variant],r[`${i.variant}${st(i.color)}`],r[`size${st(i.size)}`],r[`${i.variant}Size${st(i.size)}`],i.color==="inherit"&&r.colorInherit,i.disableElevation&&r.disableElevation,i.fullWidth&&r.fullWidth,i.loading&&r.loading]}})(Gt(({theme:n})=>{const r=n.palette.mode==="light"?n.palette.grey[300]:n.palette.grey[800],i=n.palette.mode==="light"?n.palette.grey.A100:n.palette.grey[700];return{...n.typography.button,minWidth:64,padding:"6px 16px",border:0,borderRadius:(n.vars||n).shape.borderRadius,transition:n.transitions.create(["background-color","box-shadow","border-color","color"],{duration:n.transitions.duration.short}),"&:hover":{textDecoration:"none"},[`&.${wr.disabled}`]:{color:(n.vars||n).palette.action.disabled},variants:[{props:{variant:"contained"},style:{color:"var(--variant-containedColor)",backgroundColor:"var(--variant-containedBg)",boxShadow:(n.vars||n).shadows[2],"&:hover":{boxShadow:(n.vars||n).shadows[4],"@media (hover: none)":{boxShadow:(n.vars||n).shadows[2]}},"&:active":{boxShadow:(n.vars||n).shadows[8]},[`&.${wr.focusVisible}`]:{boxShadow:(n.vars||n).shadows[6]},[`&.${wr.disabled}`]:{color:(n.vars||n).palette.action.disabled,boxShadow:(n.vars||n).shadows[0],backgroundColor:(n.vars||n).palette.action.disabledBackground}}},{props:{variant:"outlined"},style:{padding:"5px 15px",border:"1px solid currentColor",borderColor:"var(--variant-outlinedBorder, currentColor)",backgroundColor:"var(--variant-outlinedBg)",color:"var(--variant-outlinedColor)",[`&.${wr.disabled}`]:{border:`1px solid ${(n.vars||n).palette.action.disabledBackground}`}}},{props:{variant:"text"},style:{padding:"6px 8px",color:"var(--variant-textColor)",backgroundColor:"var(--variant-textBg)"}},...Object.entries(n.palette).filter(rn()).map(([l])=>({props:{color:l},style:{"--variant-textColor":(n.vars||n).palette[l].main,"--variant-outlinedColor":(n.vars||n).palette[l].main,"--variant-outlinedBorder":n.vars?`rgba(${n.vars.palette[l].mainChannel} / 0.5)`:ve(n.palette[l].main,.5),"--variant-containedColor":(n.vars||n).palette[l].contrastText,"--variant-containedBg":(n.vars||n).palette[l].main,"@media (hover: hover)":{"&:hover":{"--variant-containedBg":(n.vars||n).palette[l].dark,"--variant-textBg":n.vars?`rgba(${n.vars.palette[l].mainChannel} / ${n.vars.palette.action.hoverOpacity})`:ve(n.palette[l].main,n.palette.action.hoverOpacity),"--variant-outlinedBorder":(n.vars||n).palette[l].main,"--variant-outlinedBg":n.vars?`rgba(${n.vars.palette[l].mainChannel} / ${n.vars.palette.action.hoverOpacity})`:ve(n.palette[l].main,n.palette.action.hoverOpacity)}}}})),{props:{color:"inherit"},style:{color:"inherit",borderColor:"currentColor","--variant-containedBg":n.vars?n.vars.palette.Button.inheritContainedBg:r,"@media (hover: hover)":{"&:hover":{"--variant-containedBg":n.vars?n.vars.palette.Button.inheritContainedHoverBg:i,"--variant-textBg":n.vars?`rgba(${n.vars.palette.text.primaryChannel} / ${n.vars.palette.action.hoverOpacity})`:ve(n.palette.text.primary,n.palette.action.hoverOpacity),"--variant-outlinedBg":n.vars?`rgba(${n.vars.palette.text.primaryChannel} / ${n.vars.palette.action.hoverOpacity})`:ve(n.palette.text.primary,n.palette.action.hoverOpacity)}}}},{props:{size:"small",variant:"text"},style:{padding:"4px 5px",fontSize:n.typography.pxToRem(13)}},{props:{size:"large",variant:"text"},style:{padding:"8px 11px",fontSize:n.typography.pxToRem(15)}},{props:{size:"small",variant:"outlined"},style:{padding:"3px 9px",fontSize:n.typography.pxToRem(13)}},{props:{size:"large",variant:"outlined"},style:{padding:"7px 21px",fontSize:n.typography.pxToRem(15)}},{props:{size:"small",variant:"contained"},style:{padding:"4px 10px",fontSize:n.typography.pxToRem(13)}},{props:{size:"large",variant:"contained"},style:{padding:"8px 22px",fontSize:n.typography.pxToRem(15)}},{props:{disableElevation:!0},style:{boxShadow:"none","&:hover":{boxShadow:"none"},[`&.${wr.focusVisible}`]:{boxShadow:"none"},"&:active":{boxShadow:"none"},[`&.${wr.disabled}`]:{boxShadow:"none"}}},{props:{fullWidth:!0},style:{width:"100%"}},{props:{loadingPosition:"center"},style:{transition:n.transitions.create(["background-color","box-shadow","border-color"],{duration:n.transitions.duration.short}),[`&.${wr.loading}`]:{color:"transparent"}}}]}})),Sw=ct("span",{name:"MuiButton",slot:"StartIcon",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.startIcon,i.loading&&r.startIconLoadingStart,r[`iconSize${st(i.size)}`]]}})(({theme:n})=>({display:"inherit",marginRight:8,marginLeft:-4,variants:[{props:{size:"small"},style:{marginLeft:-2}},{props:{loadingPosition:"start",loading:!0},style:{transition:n.transitions.create(["opacity"],{duration:n.transitions.duration.short}),opacity:0}},{props:{loadingPosition:"start",loading:!0,fullWidth:!0},style:{marginRight:-8}},...W0]})),xw=ct("span",{name:"MuiButton",slot:"EndIcon",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.endIcon,i.loading&&r.endIconLoadingEnd,r[`iconSize${st(i.size)}`]]}})(({theme:n})=>({display:"inherit",marginRight:-4,marginLeft:8,variants:[{props:{size:"small"},style:{marginRight:-2}},{props:{loadingPosition:"end",loading:!0},style:{transition:n.transitions.create(["opacity"],{duration:n.transitions.duration.short}),opacity:0}},{props:{loadingPosition:"end",loading:!0,fullWidth:!0},style:{marginLeft:-8}},...W0]})),Cw=ct("span",{name:"MuiButton",slot:"LoadingIndicator"})(({theme:n})=>({display:"none",position:"absolute",visibility:"visible",variants:[{props:{loading:!0},style:{display:"flex"}},{props:{loadingPosition:"start"},style:{left:14}},{props:{loadingPosition:"start",size:"small"},style:{left:10}},{props:{variant:"text",loadingPosition:"start"},style:{left:6}},{props:{loadingPosition:"center"},style:{left:"50%",transform:"translate(-50%)",color:(n.vars||n).palette.action.disabled}},{props:{loadingPosition:"end"},style:{right:14}},{props:{loadingPosition:"end",size:"small"},style:{right:10}},{props:{variant:"text",loadingPosition:"end"},style:{right:6}},{props:{loadingPosition:"start",fullWidth:!0},style:{position:"relative",left:-10}},{props:{loadingPosition:"end",fullWidth:!0},style:{position:"relative",right:-10}}]})),vv=ct("span",{name:"MuiButton",slot:"LoadingIconPlaceholder"})({display:"inline-block",width:"1em",height:"1em"}),Br=R.forwardRef(function(r,i){const l=R.useContext(gw),u=R.useContext(yw),c=pl(l,r),d=Bt({props:c,name:"MuiButton"}),{children:p,color:m="primary",component:h="button",className:y,disabled:S=!1,disableElevation:T=!1,disableFocusRipple:E=!1,endIcon:x,focusVisibleClassName:C,fullWidth:D=!1,id:k,loading:U=null,loadingIndicator:M,loadingPosition:A="center",size:w="medium",startIcon:j,type:H,variant:q="text",...Q}=d,v=Go(k),_=M??O.jsx(j0,{"aria-labelledby":v,color:"inherit",size:16}),K={...d,color:m,component:h,disabled:S,disableElevation:T,disableFocusRipple:E,fullWidth:D,loading:U,loadingIndicator:_,loadingPosition:A,size:w,type:H,variant:q},Z=vw(K),it=(j||U&&A==="start")&&O.jsx(Sw,{className:Z.startIcon,ownerState:K,children:j||O.jsx(vv,{className:Z.loadingIconPlaceholder,ownerState:K})}),F=(x||U&&A==="end")&&O.jsx(xw,{className:Z.endIcon,ownerState:K,children:x||O.jsx(vv,{className:Z.loadingIconPlaceholder,ownerState:K})}),N=u||"",V=typeof U=="boolean"?O.jsx("span",{className:Z.loadingWrapper,style:{display:"contents"},children:U&&O.jsx(Cw,{className:Z.loadingIndicator,ownerState:K,children:_})}):null;return O.jsxs(bw,{ownerState:K,className:gt(l.className,Z.root,y,N),component:h,disabled:S||U,focusRipple:!E,focusVisibleClassName:gt(Z.focusVisible,C),ref:i,type:H,id:U?v:k,...Q,classes:Z,children:[it,A!=="end"&&V,p,A==="end"&&V,F]})});function Tw(n){return Dt("MuiCard",n)}Mt("MuiCard",["root"]);const Ew=n=>{const{classes:r}=n;return Nt({root:["root"]},Tw,r)},Rw=ct(wl,{name:"MuiCard",slot:"Root"})({overflow:"hidden"}),Mr=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiCard"}),{className:u,raised:c=!1,...d}=l,p={...l,raised:c},m=Ew(p);return O.jsx(Rw,{className:gt(m.root,u),elevation:c?8:void 0,ref:i,ownerState:p,...d})});function ww(n){return Dt("MuiCardContent",n)}Mt("MuiCardContent",["root"]);const Ow=n=>{const{classes:r}=n;return Nt({root:["root"]},ww,r)},Aw=ct("div",{name:"MuiCardContent",slot:"Root"})({padding:16,"&:last-child":{paddingBottom:24}}),zr=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiCardContent"}),{className:u,component:c="div",...d}=l,p={...l,component:c},m=Ow(p);return O.jsx(Aw,{as:c,className:gt(m.root,u),ownerState:p,ref:i,...d})}),Mw=vC({createStyledComponent:ct("div",{name:"MuiContainer",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.root,r[`maxWidth${st(String(i.maxWidth))}`],i.fixed&&r.fixed,i.disableGutters&&r.disableGutters]}}),useThemeProps:n=>Bt({props:n,name:"MuiContainer"})});function Z0(n=window){const r=n.document.documentElement.clientWidth;return n.innerWidth-r}function zw(n){const r=Rn(n);return r.body===n?Da(n).innerWidth>r.documentElement.clientWidth:n.scrollHeight>n.clientHeight}function sl(n,r){r?n.setAttribute("aria-hidden","true"):n.removeAttribute("aria-hidden")}function bv(n){return parseInt(Da(n).getComputedStyle(n).paddingRight,10)||0}function Dw(n){const i=["TEMPLATE","SCRIPT","STYLE","LINK","MAP","META","NOSCRIPT","PICTURE","COL","COLGROUP","PARAM","SLOT","SOURCE","TRACK"].includes(n.tagName),l=n.tagName==="INPUT"&&n.getAttribute("type")==="hidden";return i||l}function Sv(n,r,i,l,u){const c=[r,i,...l];[].forEach.call(n.children,d=>{const p=!c.includes(d),m=!Dw(d);p&&m&&sl(d,u)})}function ld(n,r){let i=-1;return n.some((l,u)=>r(l)?(i=u,!0):!1),i}function Nw(n,r){const i=[],l=n.container;if(!r.disableScrollLock){if(zw(l)){const d=Z0(Da(l));i.push({value:l.style.paddingRight,property:"padding-right",el:l}),l.style.paddingRight=`${bv(l)+d}px`;const p=Rn(l).querySelectorAll(".mui-fixed");[].forEach.call(p,m=>{i.push({value:m.style.paddingRight,property:"padding-right",el:m}),m.style.paddingRight=`${bv(m)+d}px`})}let c;if(l.parentNode instanceof DocumentFragment)c=Rn(l).body;else{const d=l.parentElement,p=Da(l);c=(d==null?void 0:d.nodeName)==="HTML"&&p.getComputedStyle(d).overflowY==="scroll"?d:l}i.push({value:c.style.overflow,property:"overflow",el:c},{value:c.style.overflowX,property:"overflow-x",el:c},{value:c.style.overflowY,property:"overflow-y",el:c}),c.style.overflow="hidden"}return()=>{i.forEach(({value:c,el:d,property:p})=>{c?d.style.setProperty(p,c):d.style.removeProperty(p)})}}function Bw(n){const r=[];return[].forEach.call(n.children,i=>{i.getAttribute("aria-hidden")==="true"&&r.push(i)}),r}class kw{constructor(){this.modals=[],this.containers=[]}add(r,i){let l=this.modals.indexOf(r);if(l!==-1)return l;l=this.modals.length,this.modals.push(r),r.modalRef&&sl(r.modalRef,!1);const u=Bw(i);Sv(i,r.mount,r.modalRef,u,!0);const c=ld(this.containers,d=>d.container===i);return c!==-1?(this.containers[c].modals.push(r),l):(this.containers.push({modals:[r],container:i,restore:null,hiddenSiblings:u}),l)}mount(r,i){const l=ld(this.containers,c=>c.modals.includes(r)),u=this.containers[l];u.restore||(u.restore=Nw(u,i))}remove(r,i=!0){const l=this.modals.indexOf(r);if(l===-1)return l;const u=ld(this.containers,d=>d.modals.includes(r)),c=this.containers[u];if(c.modals.splice(c.modals.indexOf(r),1),this.modals.splice(l,1),c.modals.length===0)c.restore&&c.restore(),r.modalRef&&sl(r.modalRef,i),Sv(c.container,r.mount,r.modalRef,c.hiddenSiblings,!1),this.containers.splice(u,1);else{const d=c.modals[c.modals.length-1];d.modalRef&&sl(d.modalRef,!1)}return l}isTopModal(r){return this.modals.length>0&&this.modals[this.modals.length-1]===r}}const $w=["input","select","textarea","a[href]","button","[tabindex]","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])'].join(",");function jw(n){const r=parseInt(n.getAttribute("tabindex")||"",10);return Number.isNaN(r)?n.contentEditable==="true"||(n.nodeName==="AUDIO"||n.nodeName==="VIDEO"||n.nodeName==="DETAILS")&&n.getAttribute("tabindex")===null?0:n.tabIndex:r}function _w(n){if(n.tagName!=="INPUT"||n.type!=="radio"||!n.name)return!1;const r=l=>n.ownerDocument.querySelector(`input[type="radio"]${l}`);let i=r(`[name="${n.name}"]:checked`);return i||(i=r(`[name="${n.name}"]`)),i!==n}function Uw(n){return!(n.disabled||n.tagName==="INPUT"&&n.type==="hidden"||_w(n))}function Lw(n){const r=[],i=[];return Array.from(n.querySelectorAll($w)).forEach((l,u)=>{const c=jw(l);c===-1||!Uw(l)||(c===0?r.push(l):i.push({documentOrder:u,tabIndex:c,node:l}))}),i.sort((l,u)=>l.tabIndex===u.tabIndex?l.documentOrder-u.documentOrder:l.tabIndex-u.tabIndex).map(l=>l.node).concat(r)}function Hw(){return!0}function Pw(n){const{children:r,disableAutoFocus:i=!1,disableEnforceFocus:l=!1,disableRestoreFocus:u=!1,getTabbable:c=Lw,isEnabled:d=Hw,open:p}=n,m=R.useRef(!1),h=R.useRef(null),y=R.useRef(null),S=R.useRef(null),T=R.useRef(null),E=R.useRef(!1),x=R.useRef(null),C=Pe(Vo(r),x),D=R.useRef(null);R.useEffect(()=>{!p||!x.current||(E.current=!i)},[i,p]),R.useEffect(()=>{if(!p||!x.current)return;const M=Rn(x.current);return x.current.contains(M.activeElement)||(x.current.hasAttribute("tabIndex")||x.current.setAttribute("tabIndex","-1"),E.current&&x.current.focus()),()=>{u||(S.current&&S.current.focus&&(m.current=!0,S.current.focus()),S.current=null)}},[p]),R.useEffect(()=>{if(!p||!x.current)return;const M=Rn(x.current),A=H=>{D.current=H,!(l||!d()||H.key!=="Tab")&&M.activeElement===x.current&&H.shiftKey&&(m.current=!0,y.current&&y.current.focus())},w=()=>{var Q,v;const H=x.current;if(H===null)return;if(!M.hasFocus()||!d()||m.current){m.current=!1;return}if(H.contains(M.activeElement)||l&&M.activeElement!==h.current&&M.activeElement!==y.current)return;if(M.activeElement!==T.current)T.current=null;else if(T.current!==null)return;if(!E.current)return;let q=[];if((M.activeElement===h.current||M.activeElement===y.current)&&(q=c(x.current)),q.length>0){const _=!!((Q=D.current)!=null&&Q.shiftKey&&((v=D.current)==null?void 0:v.key)==="Tab"),K=q[0],Z=q[q.length-1];typeof K!="string"&&typeof Z!="string"&&(_?Z.focus():K.focus())}else H.focus()};M.addEventListener("focusin",w),M.addEventListener("keydown",A,!0);const j=setInterval(()=>{M.activeElement&&M.activeElement.tagName==="BODY"&&w()},50);return()=>{clearInterval(j),M.removeEventListener("focusin",w),M.removeEventListener("keydown",A,!0)}},[i,l,u,d,p,c]);const k=M=>{S.current===null&&(S.current=M.relatedTarget),E.current=!0,T.current=M.target;const A=r.props.onFocus;A&&A(M)},U=M=>{S.current===null&&(S.current=M.relatedTarget),E.current=!0};return O.jsxs(R.Fragment,{children:[O.jsx("div",{tabIndex:p?0:-1,onFocus:U,ref:h,"data-testid":"sentinelStart"}),R.cloneElement(r,{ref:C,onFocus:k}),O.jsx("div",{tabIndex:p?0:-1,onFocus:U,ref:y,"data-testid":"sentinelEnd"})]})}function qw(n){return typeof n=="function"?n():n}function Gw(n){return n?n.props.hasOwnProperty("in"):!1}const xv=()=>{},Ws=new kw;function Vw(n){const{container:r,disableEscapeKeyDown:i=!1,disableScrollLock:l=!1,closeAfterTransition:u=!1,onTransitionEnter:c,onTransitionExited:d,children:p,onClose:m,open:h,rootRef:y}=n,S=R.useRef({}),T=R.useRef(null),E=R.useRef(null),x=Pe(E,y),[C,D]=R.useState(!h),k=Gw(p);let U=!0;(n["aria-hidden"]==="false"||n["aria-hidden"]===!1)&&(U=!1);const M=()=>Rn(T.current),A=()=>(S.current.modalRef=E.current,S.current.mount=T.current,S.current),w=()=>{Ws.mount(A(),{disableScrollLock:l}),E.current&&(E.current.scrollTop=0)},j=Aa(()=>{const F=qw(r)||M().body;Ws.add(A(),F),E.current&&w()}),H=()=>Ws.isTopModal(A()),q=Aa(F=>{T.current=F,F&&(h&&H()?w():E.current&&sl(E.current,U))}),Q=R.useCallback(()=>{Ws.remove(A(),U)},[U]);R.useEffect(()=>()=>{Q()},[Q]),R.useEffect(()=>{h?j():(!k||!u)&&Q()},[h,Q,k,u,j]);const v=F=>N=>{var V;(V=F.onKeyDown)==null||V.call(F,N),!(N.key!=="Escape"||N.which===229||!H())&&(i||(N.stopPropagation(),m&&m(N,"escapeKeyDown")))},_=F=>N=>{var V;(V=F.onClick)==null||V.call(F,N),N.target===N.currentTarget&&m&&m(N,"backdropClick")};return{getRootProps:(F={})=>{const N=k0(n);delete N.onTransitionEnter,delete N.onTransitionExited;const V={...N,...F};return{role:"presentation",...V,onKeyDown:v(V),ref:x}},getBackdropProps:(F={})=>{const N=F;return{"aria-hidden":!0,...N,onClick:_(N),open:h}},getTransitionProps:()=>{const F=()=>{D(!1),c&&c()},N=()=>{D(!0),d&&d(),u&&Q()};return{onEnter:Ky(F,(p==null?void 0:p.props.onEnter)??xv),onExited:Ky(N,(p==null?void 0:p.props.onExited)??xv)}},rootRef:x,portalRef:q,isTopModal:H,exited:C,hasTransition:k}}function Yw(n){return Dt("MuiModal",n)}Mt("MuiModal",["root","hidden","backdrop"]);const Xw=n=>{const{open:r,exited:i,classes:l}=n;return Nt({root:["root",!r&&i&&"hidden"],backdrop:["backdrop"]},Yw,l)},Iw=ct("div",{name:"MuiModal",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.root,!i.open&&i.exited&&r.hidden]}})(Gt(({theme:n})=>({position:"fixed",zIndex:(n.vars||n).zIndex.modal,right:0,bottom:0,top:0,left:0,variants:[{props:({ownerState:r})=>!r.open&&r.exited,style:{visibility:"hidden"}}]}))),Kw=ct(Q0,{name:"MuiModal",slot:"Backdrop"})({zIndex:-1}),F0=R.forwardRef(function(r,i){const l=Bt({name:"MuiModal",props:r}),{BackdropComponent:u=Kw,BackdropProps:c,classes:d,className:p,closeAfterTransition:m=!1,children:h,container:y,component:S,components:T={},componentsProps:E={},disableAutoFocus:x=!1,disableEnforceFocus:C=!1,disableEscapeKeyDown:D=!1,disablePortal:k=!1,disableRestoreFocus:U=!1,disableScrollLock:M=!1,hideBackdrop:A=!1,keepMounted:w=!1,onClose:j,onTransitionEnter:H,onTransitionExited:q,open:Q,slotProps:v={},slots:_={},theme:K,...Z}=l,it={...l,closeAfterTransition:m,disableAutoFocus:x,disableEnforceFocus:C,disableEscapeKeyDown:D,disablePortal:k,disableRestoreFocus:U,disableScrollLock:M,hideBackdrop:A,keepMounted:w},{getRootProps:F,getBackdropProps:N,getTransitionProps:V,portalRef:ot,isTopModal:W,exited:z,hasTransition:X}=Vw({...it,rootRef:i}),et={...it,exited:z},nt=Xw(et),lt={};if(h.props.tabIndex===void 0&&(lt.tabIndex="-1"),X){const{onEnter:yt,onExited:wt}=V();lt.onEnter=yt,lt.onExited=wt}const ut={slots:{root:T.Root,backdrop:T.Backdrop,..._},slotProps:{...E,...v}},[ft,Tt]=de("root",{ref:i,elementType:Iw,externalForwardedProps:{...ut,...Z,component:S},getSlotProps:F,ownerState:et,className:gt(p,nt==null?void 0:nt.root,!et.open&&et.exited&&(nt==null?void 0:nt.hidden))}),[bt,Ot]=de("backdrop",{ref:c==null?void 0:c.ref,elementType:u,externalForwardedProps:ut,shouldForwardComponentProp:!0,additionalProps:c,getSlotProps:yt=>N({...yt,onClick:wt=>{yt!=null&&yt.onClick&&yt.onClick(wt)}}),className:gt(c==null?void 0:c.className,nt==null?void 0:nt.backdrop),ownerState:et});return!w&&!Q&&(!X||z)?null:O.jsx(I0,{ref:ot,container:y,disablePortal:k,children:O.jsxs(ft,{...Tt,children:[!A&&u?O.jsx(bt,{...Ot}):null,O.jsx(Pw,{disableEnforceFocus:C,disableAutoFocus:x,disableRestoreFocus:U,isEnabled:W,open:Q,children:R.cloneElement(h,lt)})]})})});function Qw(n){return Dt("MuiDialog",n)}const sd=Mt("MuiDialog",["root","scrollPaper","scrollBody","container","paper","paperScrollPaper","paperScrollBody","paperWidthFalse","paperWidthXs","paperWidthSm","paperWidthMd","paperWidthLg","paperWidthXl","paperFullWidth","paperFullScreen"]),J0=R.createContext({}),Ww=ct(Q0,{name:"MuiDialog",slot:"Backdrop",overrides:(n,r)=>r.backdrop})({zIndex:-1}),Zw=n=>{const{classes:r,scroll:i,maxWidth:l,fullWidth:u,fullScreen:c}=n,d={root:["root"],container:["container",`scroll${st(i)}`],paper:["paper",`paperScroll${st(i)}`,`paperWidth${st(String(l))}`,u&&"paperFullWidth",c&&"paperFullScreen"]};return Nt(d,Qw,r)},Fw=ct(F0,{name:"MuiDialog",slot:"Root"})({"@media print":{position:"absolute !important"}}),Jw=ct("div",{name:"MuiDialog",slot:"Container",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.container,r[`scroll${st(i.scroll)}`]]}})({height:"100%","@media print":{height:"auto"},outline:0,variants:[{props:{scroll:"paper"},style:{display:"flex",justifyContent:"center",alignItems:"center"}},{props:{scroll:"body"},style:{overflowY:"auto",overflowX:"hidden",textAlign:"center","&::after":{content:'""',display:"inline-block",verticalAlign:"middle",height:"100%",width:"0"}}}]}),tO=ct(wl,{name:"MuiDialog",slot:"Paper",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.paper,r[`scrollPaper${st(i.scroll)}`],r[`paperWidth${st(String(i.maxWidth))}`],i.fullWidth&&r.paperFullWidth,i.fullScreen&&r.paperFullScreen]}})(Gt(({theme:n})=>({margin:32,position:"relative",overflowY:"auto","@media print":{overflowY:"visible",boxShadow:"none"},variants:[{props:{scroll:"paper"},style:{display:"flex",flexDirection:"column",maxHeight:"calc(100% - 64px)"}},{props:{scroll:"body"},style:{display:"inline-block",verticalAlign:"middle",textAlign:"initial"}},{props:({ownerState:r})=>!r.maxWidth,style:{maxWidth:"calc(100% - 64px)"}},{props:{maxWidth:"xs"},style:{maxWidth:n.breakpoints.unit==="px"?Math.max(n.breakpoints.values.xs,444):`max(${n.breakpoints.values.xs}${n.breakpoints.unit}, 444px)`,[`&.${sd.paperScrollBody}`]:{[n.breakpoints.down(Math.max(n.breakpoints.values.xs,444)+32*2)]:{maxWidth:"calc(100% - 64px)"}}}},...Object.keys(n.breakpoints.values).filter(r=>r!=="xs").map(r=>({props:{maxWidth:r},style:{maxWidth:`${n.breakpoints.values[r]}${n.breakpoints.unit}`,[`&.${sd.paperScrollBody}`]:{[n.breakpoints.down(n.breakpoints.values[r]+32*2)]:{maxWidth:"calc(100% - 64px)"}}}})),{props:({ownerState:r})=>r.fullWidth,style:{width:"calc(100% - 64px)"}},{props:({ownerState:r})=>r.fullScreen,style:{margin:0,width:"100%",maxWidth:"100%",height:"100%",maxHeight:"none",borderRadius:0,[`&.${sd.paperScrollBody}`]:{margin:0,maxWidth:"100%"}}}]}))),eO=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiDialog"}),u=qo(),c={enter:u.transitions.duration.enteringScreen,exit:u.transitions.duration.leavingScreen},{"aria-describedby":d,"aria-labelledby":p,"aria-modal":m=!0,BackdropComponent:h,BackdropProps:y,children:S,className:T,disableEscapeKeyDown:E=!1,fullScreen:x=!1,fullWidth:C=!1,maxWidth:D="sm",onClick:k,onClose:U,open:M,PaperComponent:A=wl,PaperProps:w={},scroll:j="paper",slots:H={},slotProps:q={},TransitionComponent:Q=Nd,transitionDuration:v=c,TransitionProps:_,...K}=l,Z={...l,disableEscapeKeyDown:E,fullScreen:x,fullWidth:C,maxWidth:D,scroll:j},it=Zw(Z),F=R.useRef(),N=$t=>{F.current=$t.target===$t.currentTarget},V=$t=>{k&&k($t),F.current&&(F.current=null,U&&U($t,"backdropClick"))},ot=Go(p),W=R.useMemo(()=>({titleId:ot}),[ot]),z={transition:Q,...H},X={transition:_,paper:w,backdrop:y,...q},et={slots:z,slotProps:X},[nt,lt]=de("root",{elementType:Fw,shouldForwardComponentProp:!0,externalForwardedProps:et,ownerState:Z,className:gt(it.root,T),ref:i}),[ut,ft]=de("backdrop",{elementType:Ww,shouldForwardComponentProp:!0,externalForwardedProps:et,ownerState:Z}),[Tt,bt]=de("paper",{elementType:tO,shouldForwardComponentProp:!0,externalForwardedProps:et,ownerState:Z,className:gt(it.paper,w.className)}),[Ot,yt]=de("container",{elementType:Jw,externalForwardedProps:et,ownerState:Z,className:it.container}),[wt,St]=de("transition",{elementType:Nd,externalForwardedProps:et,ownerState:Z,additionalProps:{appear:!0,in:M,timeout:v,role:"presentation"}});return O.jsx(nt,{closeAfterTransition:!0,slots:{backdrop:ut},slotProps:{backdrop:{transitionDuration:v,as:h,...ft}},disableEscapeKeyDown:E,onClose:U,open:M,onClick:V,...lt,...K,children:O.jsx(wt,{...St,children:O.jsx(Ot,{onMouseDown:N,...yt,children:O.jsx(Tt,{as:A,elevation:24,role:"dialog","aria-describedby":d,"aria-labelledby":ot,"aria-modal":m,...bt,children:O.jsx(J0.Provider,{value:W,children:S})})})})})});function nO(n){return Dt("MuiDialogActions",n)}Mt("MuiDialogActions",["root","spacing"]);const aO=n=>{const{classes:r,disableSpacing:i}=n;return Nt({root:["root",!i&&"spacing"]},nO,r)},rO=ct("div",{name:"MuiDialogActions",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.root,!i.disableSpacing&&r.spacing]}})({display:"flex",alignItems:"center",padding:8,justifyContent:"flex-end",flex:"0 0 auto",variants:[{props:({ownerState:n})=>!n.disableSpacing,style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),oO=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiDialogActions"}),{className:u,disableSpacing:c=!1,...d}=l,p={...l,disableSpacing:c},m=aO(p);return O.jsx(rO,{className:gt(m.root,u),ownerState:p,ref:i,...d})});function iO(n){return Dt("MuiDialogContent",n)}Mt("MuiDialogContent",["root","dividers"]);function lO(n){return Dt("MuiDialogTitle",n)}const sO=Mt("MuiDialogTitle",["root"]),uO=n=>{const{classes:r,dividers:i}=n;return Nt({root:["root",i&&"dividers"]},iO,r)},cO=ct("div",{name:"MuiDialogContent",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.root,i.dividers&&r.dividers]}})(Gt(({theme:n})=>({flex:"1 1 auto",WebkitOverflowScrolling:"touch",overflowY:"auto",padding:"20px 24px",variants:[{props:({ownerState:r})=>r.dividers,style:{padding:"16px 24px",borderTop:`1px solid ${(n.vars||n).palette.divider}`,borderBottom:`1px solid ${(n.vars||n).palette.divider}`}},{props:({ownerState:r})=>!r.dividers,style:{[`.${sO.root} + &`]:{paddingTop:0}}}]}))),fO=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiDialogContent"}),{className:u,dividers:c=!1,...d}=l,p={...l,dividers:c},m=uO(p);return O.jsx(cO,{className:gt(m.root,u),ownerState:p,ref:i,...d})}),dO=n=>{const{classes:r}=n;return Nt({root:["root"]},lO,r)},pO=ct(ye,{name:"MuiDialogTitle",slot:"Root"})({padding:"16px 24px",flex:"0 0 auto"}),mO=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiDialogTitle"}),{className:u,id:c,...d}=l,p=l,m=dO(p),{titleId:h=c}=R.useContext(J0);return O.jsx(pO,{component:"h2",className:gt(m.root,u),ownerState:p,ref:i,variant:"h6",id:c??h,...d})}),Cv=Mt("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"]);function hO(n){return Dt("MuiFab",n)}const Tv=Mt("MuiFab",["root","primary","secondary","extended","circular","focusVisible","disabled","colorInherit","sizeSmall","sizeMedium","sizeLarge","info","error","warning","success"]),gO=n=>{const{color:r,variant:i,classes:l,size:u}=n,c={root:["root",i,`size${st(u)}`,r==="inherit"?"colorInherit":r]},d=Nt(c,hO,l);return{...l,...d}},yO=ct(ko,{name:"MuiFab",slot:"Root",shouldForwardProp:n=>On(n)||n==="classes",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.root,r[i.variant],r[`size${st(i.size)}`],i.color==="inherit"&&r.colorInherit,r[st(i.size)],r[i.color]]}})(Gt(({theme:n})=>{var r,i;return{...n.typography.button,minHeight:36,transition:n.transitions.create(["background-color","box-shadow","border-color"],{duration:n.transitions.duration.short}),borderRadius:"50%",padding:0,minWidth:0,width:56,height:56,zIndex:(n.vars||n).zIndex.fab,boxShadow:(n.vars||n).shadows[6],"&:active":{boxShadow:(n.vars||n).shadows[12]},color:n.vars?n.vars.palette.grey[900]:(i=(r=n.palette).getContrastText)==null?void 0:i.call(r,n.palette.grey[300]),backgroundColor:(n.vars||n).palette.grey[300],"&:hover":{backgroundColor:(n.vars||n).palette.grey.A100,"@media (hover: none)":{backgroundColor:(n.vars||n).palette.grey[300]},textDecoration:"none"},[`&.${Tv.focusVisible}`]:{boxShadow:(n.vars||n).shadows[6]},variants:[{props:{size:"small"},style:{width:40,height:40}},{props:{size:"medium"},style:{width:48,height:48}},{props:{variant:"extended"},style:{borderRadius:48/2,padding:"0 16px",width:"auto",minHeight:"auto",minWidth:48,height:48}},{props:{variant:"extended",size:"small"},style:{width:"auto",padding:"0 8px",borderRadius:34/2,minWidth:34,height:34}},{props:{variant:"extended",size:"medium"},style:{width:"auto",padding:"0 16px",borderRadius:40/2,minWidth:40,height:40}},{props:{color:"inherit"},style:{color:"inherit"}}]}}),Gt(({theme:n})=>({variants:[...Object.entries(n.palette).filter(rn(["dark","contrastText"])).map(([r])=>({props:{color:r},style:{color:(n.vars||n).palette[r].contrastText,backgroundColor:(n.vars||n).palette[r].main,"&:hover":{backgroundColor:(n.vars||n).palette[r].dark,"@media (hover: none)":{backgroundColor:(n.vars||n).palette[r].main}}}}))]})),Gt(({theme:n})=>({[`&.${Tv.disabled}`]:{color:(n.vars||n).palette.action.disabled,boxShadow:(n.vars||n).shadows[0],backgroundColor:(n.vars||n).palette.action.disabledBackground}}))),vO=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiFab"}),{children:u,className:c,color:d="default",component:p="button",disabled:m=!1,disableFocusRipple:h=!1,focusVisibleClassName:y,size:S="large",variant:T="circular",...E}=l,x={...l,color:d,component:p,disabled:m,disableFocusRipple:h,size:S,variant:T},C=gO(x);return O.jsx(yO,{className:gt(C.root,c),component:p,disabled:m,focusRipple:!h,focusVisibleClassName:gt(C.focusVisible,y),ownerState:x,ref:i,...E,classes:C,children:u})}),bO=n=>{const{classes:r,disableUnderline:i,startAdornment:l,endAdornment:u,size:c,hiddenLabel:d,multiline:p}=n,m={root:["root",!i&&"underline",l&&"adornedStart",u&&"adornedEnd",c==="small"&&`size${st(c)}`,d&&"hiddenLabel",p&&"multiline"],input:["input"]},h=Nt(m,JR,r);return{...r,...h}},SO=ct($u,{shouldForwardProp:n=>On(n)||n==="classes",name:"MuiFilledInput",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[...Bu(n,r),!i.disableUnderline&&r.underline]}})(Gt(({theme:n})=>{const r=n.palette.mode==="light",i=r?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)",l=r?"rgba(0, 0, 0, 0.06)":"rgba(255, 255, 255, 0.09)",u=r?"rgba(0, 0, 0, 0.09)":"rgba(255, 255, 255, 0.13)",c=r?"rgba(0, 0, 0, 0.12)":"rgba(255, 255, 255, 0.12)";return{position:"relative",backgroundColor:n.vars?n.vars.palette.FilledInput.bg:l,borderTopLeftRadius:(n.vars||n).shape.borderRadius,borderTopRightRadius:(n.vars||n).shape.borderRadius,transition:n.transitions.create("background-color",{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut}),"&:hover":{backgroundColor:n.vars?n.vars.palette.FilledInput.hoverBg:u,"@media (hover: none)":{backgroundColor:n.vars?n.vars.palette.FilledInput.bg:l}},[`&.${Rr.focused}`]:{backgroundColor:n.vars?n.vars.palette.FilledInput.bg:l},[`&.${Rr.disabled}`]:{backgroundColor:n.vars?n.vars.palette.FilledInput.disabledBg:c},variants:[{props:({ownerState:d})=>!d.disableUnderline,style:{"&::after":{left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:n.transitions.create("transform",{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut}),pointerEvents:"none"},[`&.${Rr.focused}:after`]:{transform:"scaleX(1) translateX(0)"},[`&.${Rr.error}`]:{"&::before, &::after":{borderBottomColor:(n.vars||n).palette.error.main}},"&::before":{borderBottom:`1px solid ${n.vars?`rgba(${n.vars.palette.common.onBackgroundChannel} / ${n.vars.opacity.inputUnderline})`:i}`,left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:n.transitions.create("border-bottom-color",{duration:n.transitions.duration.shorter}),pointerEvents:"none"},[`&:hover:not(.${Rr.disabled}, .${Rr.error}):before`]:{borderBottom:`1px solid ${(n.vars||n).palette.text.primary}`},[`&.${Rr.disabled}:before`]:{borderBottomStyle:"dotted"}}},...Object.entries(n.palette).filter(rn()).map(([d])=>{var p;return{props:{disableUnderline:!1,color:d},style:{"&::after":{borderBottom:`2px solid ${(p=(n.vars||n).palette[d])==null?void 0:p.main}`}}}}),{props:({ownerState:d})=>d.startAdornment,style:{paddingLeft:12}},{props:({ownerState:d})=>d.endAdornment,style:{paddingRight:12}},{props:({ownerState:d})=>d.multiline,style:{padding:"25px 12px 8px"}},{props:({ownerState:d,size:p})=>d.multiline&&p==="small",style:{paddingTop:21,paddingBottom:4}},{props:({ownerState:d})=>d.multiline&&d.hiddenLabel,style:{paddingTop:16,paddingBottom:17}},{props:({ownerState:d})=>d.multiline&&d.hiddenLabel&&d.size==="small",style:{paddingTop:8,paddingBottom:9}}]}})),xO=ct(ju,{name:"MuiFilledInput",slot:"Input",overridesResolver:ku})(Gt(({theme:n})=>({paddingTop:25,paddingRight:12,paddingBottom:8,paddingLeft:12,...!n.vars&&{"&:-webkit-autofill":{WebkitBoxShadow:n.palette.mode==="light"?null:"0 0 0 100px #266798 inset",WebkitTextFillColor:n.palette.mode==="light"?null:"#fff",caretColor:n.palette.mode==="light"?null:"#fff",borderTopLeftRadius:"inherit",borderTopRightRadius:"inherit"}},...n.vars&&{"&:-webkit-autofill":{borderTopLeftRadius:"inherit",borderTopRightRadius:"inherit"},[n.getColorSchemeSelector("dark")]:{"&:-webkit-autofill":{WebkitBoxShadow:"0 0 0 100px #266798 inset",WebkitTextFillColor:"#fff",caretColor:"#fff"}}},variants:[{props:{size:"small"},style:{paddingTop:21,paddingBottom:4}},{props:({ownerState:r})=>r.hiddenLabel,style:{paddingTop:16,paddingBottom:17}},{props:({ownerState:r})=>r.startAdornment,style:{paddingLeft:0}},{props:({ownerState:r})=>r.endAdornment,style:{paddingRight:0}},{props:({ownerState:r})=>r.hiddenLabel&&r.size==="small",style:{paddingTop:8,paddingBottom:9}},{props:({ownerState:r})=>r.multiline,style:{paddingTop:0,paddingBottom:0,paddingLeft:0,paddingRight:0}}]}))),yp=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiFilledInput"}),{disableUnderline:u=!1,components:c={},componentsProps:d,fullWidth:p=!1,hiddenLabel:m,inputComponent:h="input",multiline:y=!1,slotProps:S,slots:T={},type:E="text",...x}=l,C={...l,disableUnderline:u,fullWidth:p,inputComponent:h,multiline:y,type:E},D=bO(l),k={root:{ownerState:C},input:{ownerState:C}},U=S??d?nn(k,S??d):k,M=T.root??c.Root??SO,A=T.input??c.Input??xO;return O.jsx(gp,{slots:{root:M,input:A},slotProps:U,fullWidth:p,inputComponent:h,multiline:y,ref:i,type:E,...x,classes:D})});yp.muiName="Input";function CO(n){return Dt("MuiFormControl",n)}Mt("MuiFormControl",["root","marginNone","marginNormal","marginDense","fullWidth","disabled"]);const TO=n=>{const{classes:r,margin:i,fullWidth:l}=n,u={root:["root",i!=="none"&&`margin${st(i)}`,l&&"fullWidth"]};return Nt(u,CO,r)},EO=ct("div",{name:"MuiFormControl",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.root,r[`margin${st(i.margin)}`],i.fullWidth&&r.fullWidth]}})({display:"inline-flex",flexDirection:"column",position:"relative",minWidth:0,padding:0,margin:0,border:0,verticalAlign:"top",variants:[{props:{margin:"normal"},style:{marginTop:16,marginBottom:8}},{props:{margin:"dense"},style:{marginTop:8,marginBottom:4}},{props:{fullWidth:!0},style:{width:"100%"}}]}),RO=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiFormControl"}),{children:u,className:c,color:d="primary",component:p="div",disabled:m=!1,error:h=!1,focused:y,fullWidth:S=!1,hiddenLabel:T=!1,margin:E="none",required:x=!1,size:C="medium",variant:D="outlined",...k}=l,U={...l,color:d,component:p,disabled:m,error:h,fullWidth:S,hiddenLabel:T,margin:E,required:x,size:C,variant:D},M=TO(U),[A,w]=R.useState(()=>{let F=!1;return u&&R.Children.forEach(u,N=>{if(!ru(N,["Input","Select"]))return;const V=ru(N,["Select"])?N.props.input:N;V&&KR(V.props)&&(F=!0)}),F}),[j,H]=R.useState(()=>{let F=!1;return u&&R.Children.forEach(u,N=>{ru(N,["Input","Select"])&&(gu(N.props,!0)||gu(N.props.inputProps,!0))&&(F=!0)}),F}),[q,Q]=R.useState(!1);m&&q&&Q(!1);const v=y!==void 0&&!m?y:q;let _;R.useRef(!1);const K=R.useCallback(()=>{H(!0)},[]),Z=R.useCallback(()=>{H(!1)},[]),it=R.useMemo(()=>({adornedStart:A,setAdornedStart:w,color:d,disabled:m,error:h,filled:j,focused:v,fullWidth:S,hiddenLabel:T,size:C,onBlur:()=>{Q(!1)},onFocus:()=>{Q(!0)},onEmpty:Z,onFilled:K,registerEffect:_,required:x,variant:D}),[A,d,m,h,j,v,S,T,_,Z,K,x,C,D]);return O.jsx(hp.Provider,{value:it,children:O.jsx(EO,{as:p,ownerState:U,className:gt(M.root,c),ref:i,...k,children:u})})});function wO(n){return Dt("MuiFormHelperText",n)}const Ev=Mt("MuiFormHelperText",["root","error","disabled","sizeSmall","sizeMedium","contained","focused","filled","required"]);var Rv;const OO=n=>{const{classes:r,contained:i,size:l,disabled:u,error:c,filled:d,focused:p,required:m}=n,h={root:["root",u&&"disabled",c&&"error",l&&`size${st(l)}`,i&&"contained",p&&"focused",d&&"filled",m&&"required"]};return Nt(h,wO,r)},AO=ct("p",{name:"MuiFormHelperText",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.root,i.size&&r[`size${st(i.size)}`],i.contained&&r.contained,i.filled&&r.filled]}})(Gt(({theme:n})=>({color:(n.vars||n).palette.text.secondary,...n.typography.caption,textAlign:"left",marginTop:3,marginRight:0,marginBottom:0,marginLeft:0,[`&.${Ev.disabled}`]:{color:(n.vars||n).palette.text.disabled},[`&.${Ev.error}`]:{color:(n.vars||n).palette.error.main},variants:[{props:{size:"small"},style:{marginTop:4}},{props:({ownerState:r})=>r.contained,style:{marginLeft:14,marginRight:14}}]}))),MO=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiFormHelperText"}),{children:u,className:c,component:d="p",disabled:p,error:m,filled:h,focused:y,margin:S,required:T,variant:E,...x}=l,C=Xo(),D=Yo({props:l,muiFormControl:C,states:["variant","size","disabled","error","filled","focused","required"]}),k={...l,component:d,contained:D.variant==="filled"||D.variant==="outlined",variant:D.variant,size:D.size,disabled:D.disabled,error:D.error,filled:D.filled,focused:D.focused,required:D.required};delete k.ownerState;const U=OO(k);return O.jsx(AO,{as:d,className:gt(U.root,c),ref:i,...x,ownerState:k,children:u===" "?Rv||(Rv=O.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"})):u})});function zO(n){return Dt("MuiFormLabel",n)}const ul=Mt("MuiFormLabel",["root","colorSecondary","focused","disabled","error","filled","required","asterisk"]),DO=n=>{const{classes:r,color:i,focused:l,disabled:u,error:c,filled:d,required:p}=n,m={root:["root",`color${st(i)}`,u&&"disabled",c&&"error",d&&"filled",l&&"focused",p&&"required"],asterisk:["asterisk",c&&"error"]};return Nt(m,zO,r)},NO=ct("label",{name:"MuiFormLabel",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.root,i.color==="secondary"&&r.colorSecondary,i.filled&&r.filled]}})(Gt(({theme:n})=>({color:(n.vars||n).palette.text.secondary,...n.typography.body1,lineHeight:"1.4375em",padding:0,position:"relative",variants:[...Object.entries(n.palette).filter(rn()).map(([r])=>({props:{color:r},style:{[`&.${ul.focused}`]:{color:(n.vars||n).palette[r].main}}})),{props:{},style:{[`&.${ul.disabled}`]:{color:(n.vars||n).palette.text.disabled},[`&.${ul.error}`]:{color:(n.vars||n).palette.error.main}}}]}))),BO=ct("span",{name:"MuiFormLabel",slot:"Asterisk"})(Gt(({theme:n})=>({[`&.${ul.error}`]:{color:(n.vars||n).palette.error.main}}))),kO=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiFormLabel"}),{children:u,className:c,color:d,component:p="label",disabled:m,error:h,filled:y,focused:S,required:T,...E}=l,x=Xo(),C=Yo({props:l,muiFormControl:x,states:["color","required","focused","disabled","error","filled"]}),D={...l,color:C.color||"primary",component:p,disabled:C.disabled,error:C.error,filled:C.filled,focused:C.focused,required:C.required},k=DO(D);return O.jsxs(NO,{as:p,ownerState:D,className:gt(k.root,c),ref:i,...E,children:[u,C.required&&O.jsxs(BO,{ownerState:D,"aria-hidden":!0,className:k.asterisk,children:[" ","*"]})]})}),Oa=kC({createStyledComponent:ct("div",{name:"MuiGrid",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.root,i.container&&r.container]}}),componentName:"MuiGrid",useThemeProps:n=>Bt({props:n,name:"MuiGrid"}),useTheme:qo});function Bd(n){return`scale(${n}, ${n**2})`}const $O={entering:{opacity:1,transform:Bd(1)},entered:{opacity:1,transform:"none"}},ud=typeof navigator<"u"&&/^((?!chrome|android).)*(safari|mobile)/i.test(navigator.userAgent)&&/(os |version\/)15(.|_)4/i.test(navigator.userAgent),yu=R.forwardRef(function(r,i){const{addEndListener:l,appear:u=!0,children:c,easing:d,in:p,onEnter:m,onEntered:h,onEntering:y,onExit:S,onExited:T,onExiting:E,style:x,timeout:C="auto",TransitionComponent:D=ua,...k}=r,U=Mo(),M=R.useRef(),A=qo(),w=R.useRef(null),j=Pe(w,Vo(c),i),H=F=>N=>{if(F){const V=w.current;N===void 0?F(V):F(V,N)}},q=H(y),Q=H((F,N)=>{D0(F);const{duration:V,delay:ot,easing:W}=du({style:x,timeout:C,easing:d},{mode:"enter"});let z;C==="auto"?(z=A.transitions.getAutoHeightDuration(F.clientHeight),M.current=z):z=V,F.style.transition=[A.transitions.create("opacity",{duration:z,delay:ot}),A.transitions.create("transform",{duration:ud?z:z*.666,delay:ot,easing:W})].join(","),m&&m(F,N)}),v=H(h),_=H(E),K=H(F=>{const{duration:N,delay:V,easing:ot}=du({style:x,timeout:C,easing:d},{mode:"exit"});let W;C==="auto"?(W=A.transitions.getAutoHeightDuration(F.clientHeight),M.current=W):W=N,F.style.transition=[A.transitions.create("opacity",{duration:W,delay:V}),A.transitions.create("transform",{duration:ud?W:W*.666,delay:ud?V:V||W*.333,easing:ot})].join(","),F.style.opacity=0,F.style.transform=Bd(.75),S&&S(F)}),Z=H(T),it=F=>{C==="auto"&&U.start(M.current||0,F),l&&l(w.current,F)};return O.jsx(D,{appear:u,in:p,nodeRef:w,onEnter:Q,onEntered:v,onEntering:q,onExit:K,onExited:Z,onExiting:_,addEndListener:it,timeout:C==="auto"?null:C,...k,children:(F,{ownerState:N,...V})=>R.cloneElement(c,{style:{opacity:0,transform:Bd(.75),visibility:F==="exited"&&!p?"hidden":void 0,...$O[F],...x,...c.props.style},ref:j,...V})})});yu&&(yu.muiSupportAuto=!0);const jO=n=>{const{classes:r,disableUnderline:i}=n,u=Nt({root:["root",!i&&"underline"],input:["input"]},ZR,r);return{...r,...u}},_O=ct($u,{shouldForwardProp:n=>On(n)||n==="classes",name:"MuiInput",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[...Bu(n,r),!i.disableUnderline&&r.underline]}})(Gt(({theme:n})=>{let i=n.palette.mode==="light"?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)";return n.vars&&(i=`rgba(${n.vars.palette.common.onBackgroundChannel} / ${n.vars.opacity.inputUnderline})`),{position:"relative",variants:[{props:({ownerState:l})=>l.formControl,style:{"label + &":{marginTop:16}}},{props:({ownerState:l})=>!l.disableUnderline,style:{"&::after":{left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:n.transitions.create("transform",{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut}),pointerEvents:"none"},[`&.${Qi.focused}:after`]:{transform:"scaleX(1) translateX(0)"},[`&.${Qi.error}`]:{"&::before, &::after":{borderBottomColor:(n.vars||n).palette.error.main}},"&::before":{borderBottom:`1px solid ${i}`,left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:n.transitions.create("border-bottom-color",{duration:n.transitions.duration.shorter}),pointerEvents:"none"},[`&:hover:not(.${Qi.disabled}, .${Qi.error}):before`]:{borderBottom:`2px solid ${(n.vars||n).palette.text.primary}`,"@media (hover: none)":{borderBottom:`1px solid ${i}`}},[`&.${Qi.disabled}:before`]:{borderBottomStyle:"dotted"}}},...Object.entries(n.palette).filter(rn()).map(([l])=>({props:{color:l,disableUnderline:!1},style:{"&::after":{borderBottom:`2px solid ${(n.vars||n).palette[l].main}`}}}))]}})),UO=ct(ju,{name:"MuiInput",slot:"Input",overridesResolver:ku})({}),vp=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiInput"}),{disableUnderline:u=!1,components:c={},componentsProps:d,fullWidth:p=!1,inputComponent:m="input",multiline:h=!1,slotProps:y,slots:S={},type:T="text",...E}=l,x=jO(l),D={root:{ownerState:{disableUnderline:u}}},k=y??d?nn(y??d,D):D,U=S.root??c.Root??_O,M=S.input??c.Input??UO;return O.jsx(gp,{slots:{root:U,input:M},slotProps:k,fullWidth:p,inputComponent:m,multiline:h,ref:i,type:T,...E,classes:x})});vp.muiName="Input";function LO(n){return Dt("MuiInputLabel",n)}Mt("MuiInputLabel",["root","focused","disabled","error","required","asterisk","formControl","sizeSmall","shrink","animated","standard","filled","outlined"]);const HO=n=>{const{classes:r,formControl:i,size:l,shrink:u,disableAnimation:c,variant:d,required:p}=n,m={root:["root",i&&"formControl",!c&&"animated",u&&"shrink",l&&l!=="medium"&&`size${st(l)}`,d],asterisk:[p&&"asterisk"]},h=Nt(m,LO,r);return{...r,...h}},PO=ct(kO,{shouldForwardProp:n=>On(n)||n==="classes",name:"MuiInputLabel",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[{[`& .${ul.asterisk}`]:r.asterisk},r.root,i.formControl&&r.formControl,i.size==="small"&&r.sizeSmall,i.shrink&&r.shrink,!i.disableAnimation&&r.animated,i.focused&&r.focused,r[i.variant]]}})(Gt(({theme:n})=>({display:"block",transformOrigin:"top left",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%",variants:[{props:({ownerState:r})=>r.formControl,style:{position:"absolute",left:0,top:0,transform:"translate(0, 20px) scale(1)"}},{props:{size:"small"},style:{transform:"translate(0, 17px) scale(1)"}},{props:({ownerState:r})=>r.shrink,style:{transform:"translate(0, -1.5px) scale(0.75)",transformOrigin:"top left",maxWidth:"133%"}},{props:({ownerState:r})=>!r.disableAnimation,style:{transition:n.transitions.create(["color","transform","max-width"],{duration:n.transitions.duration.shorter,easing:n.transitions.easing.easeOut})}},{props:{variant:"filled"},style:{zIndex:1,pointerEvents:"none",transform:"translate(12px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"}},{props:{variant:"filled",size:"small"},style:{transform:"translate(12px, 13px) scale(1)"}},{props:({variant:r,ownerState:i})=>r==="filled"&&i.shrink,style:{userSelect:"none",pointerEvents:"auto",transform:"translate(12px, 7px) scale(0.75)",maxWidth:"calc(133% - 24px)"}},{props:({variant:r,ownerState:i,size:l})=>r==="filled"&&i.shrink&&l==="small",style:{transform:"translate(12px, 4px) scale(0.75)"}},{props:{variant:"outlined"},style:{zIndex:1,pointerEvents:"none",transform:"translate(14px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"}},{props:{variant:"outlined",size:"small"},style:{transform:"translate(14px, 9px) scale(1)"}},{props:({variant:r,ownerState:i})=>r==="outlined"&&i.shrink,style:{userSelect:"none",pointerEvents:"auto",maxWidth:"calc(133% - 32px)",transform:"translate(14px, -9px) scale(0.75)"}}]}))),qO=R.forwardRef(function(r,i){const l=Bt({name:"MuiInputLabel",props:r}),{disableAnimation:u=!1,margin:c,shrink:d,variant:p,className:m,...h}=l,y=Xo();let S=d;typeof S>"u"&&y&&(S=y.filled||y.focused||y.adornedStart);const T=Yo({props:l,muiFormControl:y,states:["size","variant","required","focused"]}),E={...l,disableAnimation:u,formControl:y,shrink:S,size:T.size,variant:T.variant,required:T.required,focused:T.focused},x=HO(E);return O.jsx(PO,{"data-shrink":S,ref:i,className:gt(x.root,m),...h,ownerState:E,classes:x})}),kd=R.createContext({});function GO(n){return Dt("MuiList",n)}Mt("MuiList",["root","padding","dense","subheader"]);const VO=n=>{const{classes:r,disablePadding:i,dense:l,subheader:u}=n;return Nt({root:["root",!i&&"padding",l&&"dense",u&&"subheader"]},GO,r)},YO=ct("ul",{name:"MuiList",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.root,!i.disablePadding&&r.padding,i.dense&&r.dense,i.subheader&&r.subheader]}})({listStyle:"none",margin:0,padding:0,position:"relative",variants:[{props:({ownerState:n})=>!n.disablePadding,style:{paddingTop:8,paddingBottom:8}},{props:({ownerState:n})=>n.subheader,style:{paddingTop:0}}]}),XO=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiList"}),{children:u,className:c,component:d="ul",dense:p=!1,disablePadding:m=!1,subheader:h,...y}=l,S=R.useMemo(()=>({dense:p}),[p]),T={...l,component:d,dense:p,disablePadding:m},E=VO(T);return O.jsx(kd.Provider,{value:S,children:O.jsxs(YO,{as:d,className:gt(E.root,c),ref:i,ownerState:T,...y,children:[h,u]})})}),wv=Mt("MuiListItemIcon",["root","alignItemsFlexStart"]),Ov=Mt("MuiListItemText",["root","multiline","dense","inset","primary","secondary"]);function cd(n,r,i){return n===r?n.firstChild:r&&r.nextElementSibling?r.nextElementSibling:i?null:n.firstChild}function Av(n,r,i){return n===r?i?n.firstChild:n.lastChild:r&&r.previousElementSibling?r.previousElementSibling:i?null:n.lastChild}function tb(n,r){if(r===void 0)return!0;let i=n.innerText;return i===void 0&&(i=n.textContent),i=i.trim().toLowerCase(),i.length===0?!1:r.repeating?i[0]===r.keys[0]:i.startsWith(r.keys.join(""))}function Wi(n,r,i,l,u,c){let d=!1,p=u(n,r,r?i:!1);for(;p;){if(p===n.firstChild){if(d)return!1;d=!0}const m=l?!1:p.disabled||p.getAttribute("aria-disabled")==="true";if(!p.hasAttribute("tabindex")||!tb(p,c)||m)p=u(n,p,i);else return p.focus(),!0}return!1}const IO=R.forwardRef(function(r,i){const{actions:l,autoFocus:u=!1,autoFocusItem:c=!1,children:d,className:p,disabledItemsFocusable:m=!1,disableListWrap:h=!1,onKeyDown:y,variant:S="selectedMenu",...T}=r,E=R.useRef(null),x=R.useRef({keys:[],repeating:!0,previousKeyMatched:!0,lastTime:null});In(()=>{u&&E.current.focus()},[u]),R.useImperativeHandle(l,()=>({adjustStyleForScrollbar:(M,{direction:A})=>{const w=!E.current.style.width;if(M.clientHeight<E.current.clientHeight&&w){const j=`${Z0(Da(M))}px`;E.current.style[A==="rtl"?"paddingLeft":"paddingRight"]=j,E.current.style.width=`calc(100% + ${j})`}return E.current}}),[]);const C=M=>{const A=E.current,w=M.key;if(M.ctrlKey||M.metaKey||M.altKey){y&&y(M);return}const H=Rn(A).activeElement;if(w==="ArrowDown")M.preventDefault(),Wi(A,H,h,m,cd);else if(w==="ArrowUp")M.preventDefault(),Wi(A,H,h,m,Av);else if(w==="Home")M.preventDefault(),Wi(A,null,h,m,cd);else if(w==="End")M.preventDefault(),Wi(A,null,h,m,Av);else if(w.length===1){const q=x.current,Q=w.toLowerCase(),v=performance.now();q.keys.length>0&&(v-q.lastTime>500?(q.keys=[],q.repeating=!0,q.previousKeyMatched=!0):q.repeating&&Q!==q.keys[0]&&(q.repeating=!1)),q.lastTime=v,q.keys.push(Q);const _=H&&!q.repeating&&tb(H,q);q.previousKeyMatched&&(_||Wi(A,H,!1,m,cd,q))?M.preventDefault():q.previousKeyMatched=!1}y&&y(M)},D=Pe(E,i);let k=-1;R.Children.forEach(d,(M,A)=>{if(!R.isValidElement(M)){k===A&&(k+=1,k>=d.length&&(k=-1));return}M.props.disabled||(S==="selectedMenu"&&M.props.selected||k===-1)&&(k=A),k===A&&(M.props.disabled||M.props.muiSkipListHighlight||M.type.muiSkipListHighlight)&&(k+=1,k>=d.length&&(k=-1))});const U=R.Children.map(d,(M,A)=>{if(A===k){const w={};return c&&(w.autoFocus=!0),M.props.tabIndex===void 0&&S==="selectedMenu"&&(w.tabIndex=0),R.cloneElement(M,w)}return M});return O.jsx(XO,{role:"menu",ref:D,className:p,onKeyDown:C,tabIndex:u?0:-1,...T,children:U})});function KO(n){return Dt("MuiPopover",n)}Mt("MuiPopover",["root","paper"]);function Mv(n,r){let i=0;return typeof r=="number"?i=r:r==="center"?i=n.height/2:r==="bottom"&&(i=n.height),i}function zv(n,r){let i=0;return typeof r=="number"?i=r:r==="center"?i=n.width/2:r==="right"&&(i=n.width),i}function Dv(n){return[n.horizontal,n.vertical].map(r=>typeof r=="number"?`${r}px`:r).join(" ")}function Zs(n){return typeof n=="function"?n():n}const QO=n=>{const{classes:r}=n;return Nt({root:["root"],paper:["paper"]},KO,r)},WO=ct(F0,{name:"MuiPopover",slot:"Root"})({}),eb=ct(wl,{name:"MuiPopover",slot:"Paper"})({position:"absolute",overflowY:"auto",overflowX:"hidden",minWidth:16,minHeight:16,maxWidth:"calc(100% - 32px)",maxHeight:"calc(100% - 32px)",outline:0}),ZO=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiPopover"}),{action:u,anchorEl:c,anchorOrigin:d={vertical:"top",horizontal:"left"},anchorPosition:p,anchorReference:m="anchorEl",children:h,className:y,container:S,elevation:T=8,marginThreshold:E=16,open:x,PaperProps:C={},slots:D={},slotProps:k={},transformOrigin:U={vertical:"top",horizontal:"left"},TransitionComponent:M,transitionDuration:A="auto",TransitionProps:w={},disableScrollLock:j=!1,...H}=l,q=R.useRef(),Q={...l,anchorOrigin:d,anchorReference:m,elevation:T,marginThreshold:E,transformOrigin:U,TransitionComponent:M,transitionDuration:A,TransitionProps:w},v=QO(Q),_=R.useCallback(()=>{if(m==="anchorPosition")return p;const yt=Zs(c),St=(yt&&yt.nodeType===1?yt:Rn(q.current).body).getBoundingClientRect();return{top:St.top+Mv(St,d.vertical),left:St.left+zv(St,d.horizontal)}},[c,d.horizontal,d.vertical,p,m]),K=R.useCallback(yt=>({vertical:Mv(yt,U.vertical),horizontal:zv(yt,U.horizontal)}),[U.horizontal,U.vertical]),Z=R.useCallback(yt=>{const wt={width:yt.offsetWidth,height:yt.offsetHeight},St=K(wt);if(m==="none")return{top:null,left:null,transformOrigin:Dv(St)};const $t=_();let Et=$t.top-St.vertical,qt=$t.left-St.horizontal;const Ce=Et+wt.height,Ut=qt+wt.width,Kt=Da(Zs(c)),Qt=Kt.innerHeight-E,Vt=Kt.innerWidth-E;if(E!==null&&Et<E){const jt=Et-E;Et-=jt,St.vertical+=jt}else if(E!==null&&Ce>Qt){const jt=Ce-Qt;Et-=jt,St.vertical+=jt}if(E!==null&&qt<E){const jt=qt-E;qt-=jt,St.horizontal+=jt}else if(Ut>Vt){const jt=Ut-Vt;qt-=jt,St.horizontal+=jt}return{top:`${Math.round(Et)}px`,left:`${Math.round(qt)}px`,transformOrigin:Dv(St)}},[c,m,_,K,E]),[it,F]=R.useState(x),N=R.useCallback(()=>{const yt=q.current;if(!yt)return;const wt=Z(yt);wt.top!==null&&yt.style.setProperty("top",wt.top),wt.left!==null&&(yt.style.left=wt.left),yt.style.transformOrigin=wt.transformOrigin,F(!0)},[Z]);R.useEffect(()=>(j&&window.addEventListener("scroll",N),()=>window.removeEventListener("scroll",N)),[c,j,N]);const V=()=>{N()},ot=()=>{F(!1)};R.useEffect(()=>{x&&N()}),R.useImperativeHandle(u,()=>x?{updatePosition:()=>{N()}}:null,[x,N]),R.useEffect(()=>{if(!x)return;const yt=w0(()=>{N()}),wt=Da(Zs(c));return wt.addEventListener("resize",yt),()=>{yt.clear(),wt.removeEventListener("resize",yt)}},[c,x,N]);let W=A;const z={slots:{transition:M,...D},slotProps:{transition:w,paper:C,...k}},[X,et]=de("transition",{elementType:yu,externalForwardedProps:z,ownerState:Q,getSlotProps:yt=>({...yt,onEntering:(wt,St)=>{var $t;($t=yt.onEntering)==null||$t.call(yt,wt,St),V()},onExited:wt=>{var St;(St=yt.onExited)==null||St.call(yt,wt),ot()}}),additionalProps:{appear:!0,in:x}});A==="auto"&&!X.muiSupportAuto&&(W=void 0);const nt=S||(c?Rn(Zs(c)).body:void 0),[lt,{slots:ut,slotProps:ft,...Tt}]=de("root",{ref:i,elementType:WO,externalForwardedProps:{...z,...H},shouldForwardComponentProp:!0,additionalProps:{slots:{backdrop:D.backdrop},slotProps:{backdrop:MT(typeof k.backdrop=="function"?k.backdrop(Q):k.backdrop,{invisible:!0})},container:nt,open:x},ownerState:Q,className:gt(v.root,y)}),[bt,Ot]=de("paper",{ref:q,className:v.paper,elementType:eb,externalForwardedProps:z,shouldForwardComponentProp:!0,additionalProps:{elevation:T,style:it?void 0:{opacity:0}},ownerState:Q});return O.jsx(lt,{...Tt,...!Dd(lt)&&{slots:ut,slotProps:ft,disableScrollLock:j},children:O.jsx(X,{...et,timeout:W,children:O.jsx(bt,{...Ot,children:h})})})});function FO(n){return Dt("MuiMenu",n)}Mt("MuiMenu",["root","paper","list"]);const JO={vertical:"top",horizontal:"right"},tA={vertical:"top",horizontal:"left"},eA=n=>{const{classes:r}=n;return Nt({root:["root"],paper:["paper"],list:["list"]},FO,r)},nA=ct(ZO,{shouldForwardProp:n=>On(n)||n==="classes",name:"MuiMenu",slot:"Root"})({}),aA=ct(eb,{name:"MuiMenu",slot:"Paper"})({maxHeight:"calc(100% - 96px)",WebkitOverflowScrolling:"touch"}),rA=ct(IO,{name:"MuiMenu",slot:"List"})({outline:0}),$d=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiMenu"}),{autoFocus:u=!0,children:c,className:d,disableAutoFocusItem:p=!1,MenuListProps:m={},onClose:h,open:y,PaperProps:S={},PopoverClasses:T,transitionDuration:E="auto",TransitionProps:{onEntering:x,...C}={},variant:D="selectedMenu",slots:k={},slotProps:U={},...M}=l,A=tp(),w={...l,autoFocus:u,disableAutoFocusItem:p,MenuListProps:m,onEntering:x,PaperProps:S,transitionDuration:E,TransitionProps:C,variant:D},j=eA(w),H=u&&!p&&y,q=R.useRef(null),Q=(W,z)=>{q.current&&q.current.adjustStyleForScrollbar(W,{direction:A?"rtl":"ltr"}),x&&x(W,z)},v=W=>{W.key==="Tab"&&(W.preventDefault(),h&&h(W,"tabKeyDown"))};let _=-1;R.Children.map(c,(W,z)=>{R.isValidElement(W)&&(W.props.disabled||(D==="selectedMenu"&&W.props.selected||_===-1)&&(_=z))});const K={slots:k,slotProps:{list:m,transition:C,paper:S,...U}},Z=X0({elementType:k.root,externalSlotProps:U.root,ownerState:w,className:[j.root,d]}),[it,F]=de("paper",{className:j.paper,elementType:aA,externalForwardedProps:K,shouldForwardComponentProp:!0,ownerState:w}),[N,V]=de("list",{className:gt(j.list,m.className),elementType:rA,shouldForwardComponentProp:!0,externalForwardedProps:K,getSlotProps:W=>({...W,onKeyDown:z=>{var X;v(z),(X=W.onKeyDown)==null||X.call(W,z)}}),ownerState:w}),ot=typeof K.slotProps.transition=="function"?K.slotProps.transition(w):K.slotProps.transition;return O.jsx(nA,{onClose:h,anchorOrigin:{vertical:"bottom",horizontal:A?"right":"left"},transformOrigin:A?JO:tA,slots:{root:k.root,paper:it,backdrop:k.backdrop,...k.transition&&{transition:k.transition}},slotProps:{root:Z,paper:F,backdrop:typeof U.backdrop=="function"?U.backdrop(w):U.backdrop,transition:{...ot,onEntering:(...W)=>{var z;Q(...W),(z=ot==null?void 0:ot.onEntering)==null||z.call(ot,...W)}}},open:y,ref:i,transitionDuration:E,ownerState:w,...M,classes:T,children:O.jsx(N,{actions:q,autoFocus:u&&(_===-1||p),autoFocusItem:H,variant:D,...V,children:c})})});function oA(n){return Dt("MuiMenuItem",n)}const Zi=Mt("MuiMenuItem",["root","focusVisible","dense","disabled","divider","gutters","selected"]),iA=(n,r)=>{const{ownerState:i}=n;return[r.root,i.dense&&r.dense,i.divider&&r.divider,!i.disableGutters&&r.gutters]},lA=n=>{const{disabled:r,dense:i,divider:l,disableGutters:u,selected:c,classes:d}=n,m=Nt({root:["root",i&&"dense",r&&"disabled",!u&&"gutters",l&&"divider",c&&"selected"]},oA,d);return{...d,...m}},sA=ct(ko,{shouldForwardProp:n=>On(n)||n==="classes",name:"MuiMenuItem",slot:"Root",overridesResolver:iA})(Gt(({theme:n})=>({...n.typography.body1,display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minHeight:48,paddingTop:6,paddingBottom:6,boxSizing:"border-box",whiteSpace:"nowrap","&:hover":{textDecoration:"none",backgroundColor:(n.vars||n).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${Zi.selected}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / ${n.vars.palette.action.selectedOpacity})`:ve(n.palette.primary.main,n.palette.action.selectedOpacity),[`&.${Zi.focusVisible}`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.focusOpacity}))`:ve(n.palette.primary.main,n.palette.action.selectedOpacity+n.palette.action.focusOpacity)}},[`&.${Zi.selected}:hover`]:{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / calc(${n.vars.palette.action.selectedOpacity} + ${n.vars.palette.action.hoverOpacity}))`:ve(n.palette.primary.main,n.palette.action.selectedOpacity+n.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:n.vars?`rgba(${n.vars.palette.primary.mainChannel} / ${n.vars.palette.action.selectedOpacity})`:ve(n.palette.primary.main,n.palette.action.selectedOpacity)}},[`&.${Zi.focusVisible}`]:{backgroundColor:(n.vars||n).palette.action.focus},[`&.${Zi.disabled}`]:{opacity:(n.vars||n).palette.action.disabledOpacity},[`& + .${Cv.root}`]:{marginTop:n.spacing(1),marginBottom:n.spacing(1)},[`& + .${Cv.inset}`]:{marginLeft:52},[`& .${Ov.root}`]:{marginTop:0,marginBottom:0},[`& .${Ov.inset}`]:{paddingLeft:36},[`& .${wv.root}`]:{minWidth:36},variants:[{props:({ownerState:r})=>!r.disableGutters,style:{paddingLeft:16,paddingRight:16}},{props:({ownerState:r})=>r.divider,style:{borderBottom:`1px solid ${(n.vars||n).palette.divider}`,backgroundClip:"padding-box"}},{props:({ownerState:r})=>!r.dense,style:{[n.breakpoints.up("sm")]:{minHeight:"auto"}}},{props:({ownerState:r})=>r.dense,style:{minHeight:32,paddingTop:4,paddingBottom:4,...n.typography.body2,[`& .${wv.root} svg`]:{fontSize:"1.25rem"}}}]}))),Oo=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiMenuItem"}),{autoFocus:u=!1,component:c="li",dense:d=!1,divider:p=!1,disableGutters:m=!1,focusVisibleClassName:h,role:y="menuitem",tabIndex:S,className:T,...E}=l,x=R.useContext(kd),C=R.useMemo(()=>({dense:d||x.dense||!1,disableGutters:m}),[x.dense,d,m]),D=R.useRef(null);In(()=>{u&&D.current&&D.current.focus()},[u]);const k={...l,dense:C.dense,divider:p,disableGutters:m},U=lA(l),M=Pe(D,i);let A;return l.disabled||(A=S!==void 0?S:-1),O.jsx(kd.Provider,{value:C,children:O.jsx(sA,{ref:M,role:y,tabIndex:A,component:c,focusVisibleClassName:gt(U.focusVisible,h),className:gt(U.root,T),...E,ownerState:k,classes:U})})});function uA(n){return Dt("MuiNativeSelect",n)}const bp=Mt("MuiNativeSelect",["root","select","multiple","filled","outlined","standard","disabled","icon","iconOpen","iconFilled","iconOutlined","iconStandard","nativeInput","error"]),cA=n=>{const{classes:r,variant:i,disabled:l,multiple:u,open:c,error:d}=n,p={select:["select",i,l&&"disabled",u&&"multiple",d&&"error"],icon:["icon",`icon${st(i)}`,c&&"iconOpen",l&&"disabled"]};return Nt(p,uA,r)},nb=ct("select")(({theme:n})=>({MozAppearance:"none",WebkitAppearance:"none",userSelect:"none",borderRadius:0,cursor:"pointer","&:focus":{borderRadius:0},[`&.${bp.disabled}`]:{cursor:"default"},"&[multiple]":{height:"auto"},"&:not([multiple]) option, &:not([multiple]) optgroup":{backgroundColor:(n.vars||n).palette.background.paper},variants:[{props:({ownerState:r})=>r.variant!=="filled"&&r.variant!=="outlined",style:{"&&&":{paddingRight:24,minWidth:16}}},{props:{variant:"filled"},style:{"&&&":{paddingRight:32}}},{props:{variant:"outlined"},style:{borderRadius:(n.vars||n).shape.borderRadius,"&:focus":{borderRadius:(n.vars||n).shape.borderRadius},"&&&":{paddingRight:32}}}]})),fA=ct(nb,{name:"MuiNativeSelect",slot:"Select",shouldForwardProp:On,overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.select,r[i.variant],i.error&&r.error,{[`&.${bp.multiple}`]:r.multiple}]}})({}),ab=ct("svg")(({theme:n})=>({position:"absolute",right:0,top:"calc(50% - .5em)",pointerEvents:"none",color:(n.vars||n).palette.action.active,[`&.${bp.disabled}`]:{color:(n.vars||n).palette.action.disabled},variants:[{props:({ownerState:r})=>r.open,style:{transform:"rotate(180deg)"}},{props:{variant:"filled"},style:{right:7}},{props:{variant:"outlined"},style:{right:7}}]})),dA=ct(ab,{name:"MuiNativeSelect",slot:"Icon",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.icon,i.variant&&r[`icon${st(i.variant)}`],i.open&&r.iconOpen]}})({}),pA=R.forwardRef(function(r,i){const{className:l,disabled:u,error:c,IconComponent:d,inputRef:p,variant:m="standard",...h}=r,y={...r,disabled:u,variant:m,error:c},S=cA(y);return O.jsxs(R.Fragment,{children:[O.jsx(fA,{ownerState:y,className:gt(S.select,l),disabled:u,ref:p||i,...h}),r.multiple?null:O.jsx(dA,{as:d,ownerState:y,className:S.icon})]})});var Nv;const mA=ct("fieldset",{shouldForwardProp:On})({textAlign:"left",position:"absolute",bottom:0,right:0,top:-5,left:0,margin:0,padding:"0 8px",pointerEvents:"none",borderRadius:"inherit",borderStyle:"solid",borderWidth:1,overflow:"hidden",minWidth:"0%"}),hA=ct("legend",{shouldForwardProp:On})(Gt(({theme:n})=>({float:"unset",width:"auto",overflow:"hidden",variants:[{props:({ownerState:r})=>!r.withLabel,style:{padding:0,lineHeight:"11px",transition:n.transitions.create("width",{duration:150,easing:n.transitions.easing.easeOut})}},{props:({ownerState:r})=>r.withLabel,style:{display:"block",padding:0,height:11,fontSize:"0.75em",visibility:"hidden",maxWidth:.01,transition:n.transitions.create("max-width",{duration:50,easing:n.transitions.easing.easeOut}),whiteSpace:"nowrap","& > span":{paddingLeft:5,paddingRight:5,display:"inline-block",opacity:0,visibility:"visible"}}},{props:({ownerState:r})=>r.withLabel&&r.notched,style:{maxWidth:"100%",transition:n.transitions.create("max-width",{duration:100,easing:n.transitions.easing.easeOut,delay:50})}}]})));function gA(n){const{children:r,classes:i,className:l,label:u,notched:c,...d}=n,p=u!=null&&u!=="",m={...n,notched:c,withLabel:p};return O.jsx(mA,{"aria-hidden":!0,className:l,ownerState:m,...d,children:O.jsx(hA,{ownerState:m,children:p?O.jsx("span",{children:u}):Nv||(Nv=O.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"}))})})}const yA=n=>{const{classes:r}=n,l=Nt({root:["root"],notchedOutline:["notchedOutline"],input:["input"]},FR,r);return{...r,...l}},vA=ct($u,{shouldForwardProp:n=>On(n)||n==="classes",name:"MuiOutlinedInput",slot:"Root",overridesResolver:Bu})(Gt(({theme:n})=>{const r=n.palette.mode==="light"?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)";return{position:"relative",borderRadius:(n.vars||n).shape.borderRadius,[`&:hover .${ta.notchedOutline}`]:{borderColor:(n.vars||n).palette.text.primary},"@media (hover: none)":{[`&:hover .${ta.notchedOutline}`]:{borderColor:n.vars?`rgba(${n.vars.palette.common.onBackgroundChannel} / 0.23)`:r}},[`&.${ta.focused} .${ta.notchedOutline}`]:{borderWidth:2},variants:[...Object.entries(n.palette).filter(rn()).map(([i])=>({props:{color:i},style:{[`&.${ta.focused} .${ta.notchedOutline}`]:{borderColor:(n.vars||n).palette[i].main}}})),{props:{},style:{[`&.${ta.error} .${ta.notchedOutline}`]:{borderColor:(n.vars||n).palette.error.main},[`&.${ta.disabled} .${ta.notchedOutline}`]:{borderColor:(n.vars||n).palette.action.disabled}}},{props:({ownerState:i})=>i.startAdornment,style:{paddingLeft:14}},{props:({ownerState:i})=>i.endAdornment,style:{paddingRight:14}},{props:({ownerState:i})=>i.multiline,style:{padding:"16.5px 14px"}},{props:({ownerState:i,size:l})=>i.multiline&&l==="small",style:{padding:"8.5px 14px"}}]}})),bA=ct(gA,{name:"MuiOutlinedInput",slot:"NotchedOutline"})(Gt(({theme:n})=>{const r=n.palette.mode==="light"?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)";return{borderColor:n.vars?`rgba(${n.vars.palette.common.onBackgroundChannel} / 0.23)`:r}})),SA=ct(ju,{name:"MuiOutlinedInput",slot:"Input",overridesResolver:ku})(Gt(({theme:n})=>({padding:"16.5px 14px",...!n.vars&&{"&:-webkit-autofill":{WebkitBoxShadow:n.palette.mode==="light"?null:"0 0 0 100px #266798 inset",WebkitTextFillColor:n.palette.mode==="light"?null:"#fff",caretColor:n.palette.mode==="light"?null:"#fff",borderRadius:"inherit"}},...n.vars&&{"&:-webkit-autofill":{borderRadius:"inherit"},[n.getColorSchemeSelector("dark")]:{"&:-webkit-autofill":{WebkitBoxShadow:"0 0 0 100px #266798 inset",WebkitTextFillColor:"#fff",caretColor:"#fff"}}},variants:[{props:{size:"small"},style:{padding:"8.5px 14px"}},{props:({ownerState:r})=>r.multiline,style:{padding:0}},{props:({ownerState:r})=>r.startAdornment,style:{paddingLeft:0}},{props:({ownerState:r})=>r.endAdornment,style:{paddingRight:0}}]}))),Sp=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiOutlinedInput"}),{components:u={},fullWidth:c=!1,inputComponent:d="input",label:p,multiline:m=!1,notched:h,slots:y={},slotProps:S={},type:T="text",...E}=l,x=yA(l),C=Xo(),D=Yo({props:l,muiFormControl:C,states:["color","disabled","error","focused","hiddenLabel","size","required"]}),k={...l,color:D.color||"primary",disabled:D.disabled,error:D.error,focused:D.focused,formControl:C,fullWidth:c,hiddenLabel:D.hiddenLabel,multiline:m,size:D.size,type:T},U=y.root??u.Root??vA,M=y.input??u.Input??SA,[A,w]=de("notchedOutline",{elementType:bA,className:x.notchedOutline,shouldForwardComponentProp:!0,ownerState:k,externalForwardedProps:{slots:y,slotProps:S},additionalProps:{label:p!=null&&p!==""&&D.required?O.jsxs(R.Fragment,{children:[p," ","*"]}):p}});return O.jsx(gp,{slots:{root:U,input:M},slotProps:S,renderSuffix:j=>O.jsx(A,{...w,notched:typeof h<"u"?h:!!(j.startAdornment||j.filled||j.focused)}),fullWidth:c,inputComponent:d,multiline:m,ref:i,type:T,...E,classes:{...x,notchedOutline:null}})});Sp.muiName="Input";function rb(n){return Dt("MuiSelect",n)}const Fi=Mt("MuiSelect",["root","select","multiple","filled","outlined","standard","disabled","focused","icon","iconOpen","iconFilled","iconOutlined","iconStandard","nativeInput","error"]);var Bv;const xA=ct(nb,{name:"MuiSelect",slot:"Select",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[{[`&.${Fi.select}`]:r.select},{[`&.${Fi.select}`]:r[i.variant]},{[`&.${Fi.error}`]:r.error},{[`&.${Fi.multiple}`]:r.multiple}]}})({[`&.${Fi.select}`]:{height:"auto",minHeight:"1.4375em",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden"}}),CA=ct(ab,{name:"MuiSelect",slot:"Icon",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.icon,i.variant&&r[`icon${st(i.variant)}`],i.open&&r.iconOpen]}})({}),TA=ct("input",{shouldForwardProp:n=>E0(n)&&n!=="classes",name:"MuiSelect",slot:"NativeInput"})({bottom:0,left:0,position:"absolute",opacity:0,pointerEvents:"none",width:"100%",boxSizing:"border-box"});function kv(n,r){return typeof r=="object"&&r!==null?n===r:String(n)===String(r)}function EA(n){return n==null||typeof n=="string"&&!n.trim()}const RA=n=>{const{classes:r,variant:i,disabled:l,multiple:u,open:c,error:d}=n,p={select:["select",i,l&&"disabled",u&&"multiple",d&&"error"],icon:["icon",`icon${st(i)}`,c&&"iconOpen",l&&"disabled"],nativeInput:["nativeInput"]};return Nt(p,rb,r)},wA=R.forwardRef(function(r,i){var Wt;const{"aria-describedby":l,"aria-label":u,autoFocus:c,autoWidth:d,children:p,className:m,defaultOpen:h,defaultValue:y,disabled:S,displayEmpty:T,error:E=!1,IconComponent:x,inputRef:C,labelId:D,MenuProps:k={},multiple:U,name:M,onBlur:A,onChange:w,onClose:j,onFocus:H,onOpen:q,open:Q,readOnly:v,renderValue:_,required:K,SelectDisplayProps:Z={},tabIndex:it,type:F,value:N,variant:V="standard",...ot}=r,[W,z]=Cd({controlled:N,default:y,name:"Select"}),[X,et]=Cd({controlled:Q,default:h,name:"Select"}),nt=R.useRef(null),lt=R.useRef(null),[ut,ft]=R.useState(null),{current:Tt}=R.useRef(Q!=null),[bt,Ot]=R.useState(),yt=Pe(i,C),wt=R.useCallback(ht=>{lt.current=ht,ht&&ft(ht)},[]),St=ut==null?void 0:ut.parentNode;R.useImperativeHandle(yt,()=>({focus:()=>{lt.current.focus()},node:nt.current,value:W}),[W]),R.useEffect(()=>{h&&X&&ut&&!Tt&&(Ot(d?null:St.clientWidth),lt.current.focus())},[ut,d]),R.useEffect(()=>{c&&lt.current.focus()},[c]),R.useEffect(()=>{if(!D)return;const ht=Rn(lt.current).getElementById(D);if(ht){const Yt=()=>{getSelection().isCollapsed&&lt.current.focus()};return ht.addEventListener("click",Yt),()=>{ht.removeEventListener("click",Yt)}}},[D]);const $t=(ht,Yt)=>{ht?q&&q(Yt):j&&j(Yt),Tt||(Ot(d?null:St.clientWidth),et(ht))},Et=ht=>{ht.button===0&&(ht.preventDefault(),lt.current.focus(),$t(!0,ht))},qt=ht=>{$t(!1,ht)},Ce=R.Children.toArray(p),Ut=ht=>{const Yt=Ce.find(be=>be.props.value===ht.target.value);Yt!==void 0&&(z(Yt.props.value),w&&w(ht,Yt))},Kt=ht=>Yt=>{let be;if(Yt.currentTarget.hasAttribute("tabindex")){if(U){be=Array.isArray(W)?W.slice():[];const hn=W.indexOf(ht.props.value);hn===-1?be.push(ht.props.value):be.splice(hn,1)}else be=ht.props.value;if(ht.props.onClick&&ht.props.onClick(Yt),W!==be&&(z(be),w)){const hn=Yt.nativeEvent||Yt,ir=new hn.constructor(hn.type,hn);Object.defineProperty(ir,"target",{writable:!0,value:{value:be,name:M}}),w(ir,ht)}U||$t(!1,Yt)}},Qt=ht=>{v||[" ","ArrowUp","ArrowDown","Enter"].includes(ht.key)&&(ht.preventDefault(),$t(!0,ht))},Vt=ut!==null&&X,jt=ht=>{!Vt&&A&&(Object.defineProperty(ht,"target",{writable:!0,value:{value:W,name:M}}),A(ht))};delete ot["aria-invalid"];let dt,_e;const ie=[];let Ze=!1;(gu({value:W})||T)&&(_?dt=_(W):Ze=!0);const Ue=Ce.map(ht=>{if(!R.isValidElement(ht))return null;let Yt;if(U){if(!Array.isArray(W))throw new Error(Ma(2));Yt=W.some(be=>kv(be,ht.props.value)),Yt&&Ze&&ie.push(ht.props.children)}else Yt=kv(W,ht.props.value),Yt&&Ze&&(_e=ht.props.children);return R.cloneElement(ht,{"aria-selected":Yt?"true":"false",onClick:Kt(ht),onKeyUp:be=>{be.key===" "&&be.preventDefault(),ht.props.onKeyUp&&ht.props.onKeyUp(be)},role:"option",selected:Yt,value:void 0,"data-value":ht.props.value})});Ze&&(U?ie.length===0?dt=null:dt=ie.reduce((ht,Yt,be)=>(ht.push(Yt),be<ie.length-1&&ht.push(", "),ht),[]):dt=_e);let ee=bt;!d&&Tt&&ut&&(ee=St.clientWidth);let Te;typeof it<"u"?Te=it:Te=S?null:0;const ne=Z.id||(M?`mui-component-select-${M}`:void 0),le={...r,variant:V,value:W,open:Vt,error:E},mt=RA(le),Re={...k.PaperProps,...(Wt=k.slotProps)==null?void 0:Wt.paper},ae=Go();return O.jsxs(R.Fragment,{children:[O.jsx(xA,{as:"div",ref:wt,tabIndex:Te,role:"combobox","aria-controls":Vt?ae:void 0,"aria-disabled":S?"true":void 0,"aria-expanded":Vt?"true":"false","aria-haspopup":"listbox","aria-label":u,"aria-labelledby":[D,ne].filter(Boolean).join(" ")||void 0,"aria-describedby":l,"aria-required":K?"true":void 0,"aria-invalid":E?"true":void 0,onKeyDown:Qt,onMouseDown:S||v?null:Et,onBlur:jt,onFocus:H,...Z,ownerState:le,className:gt(Z.className,mt.select,m),id:ne,children:EA(dt)?Bv||(Bv=O.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"})):dt}),O.jsx(TA,{"aria-invalid":E,value:Array.isArray(W)?W.join(","):W,name:M,ref:nt,"aria-hidden":!0,onChange:Ut,tabIndex:-1,disabled:S,className:mt.nativeInput,autoFocus:c,required:K,...ot,ownerState:le}),O.jsx(CA,{as:x,className:mt.icon,ownerState:le}),O.jsx($d,{id:`menu-${M||""}`,anchorEl:St,open:Vt,onClose:qt,anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"},...k,slotProps:{...k.slotProps,list:{"aria-labelledby":D,role:"listbox","aria-multiselectable":U?"true":void 0,disableListWrap:!0,id:ae,...k.MenuListProps},paper:{...Re,style:{minWidth:ee,...Re!=null?Re.style:null}}},children:Ue})]})}),OA=n=>{const{classes:r}=n,l=Nt({root:["root"]},rb,r);return{...r,...l}},xp={name:"MuiSelect",slot:"Root",shouldForwardProp:n=>On(n)&&n!=="variant"},AA=ct(vp,xp)(""),MA=ct(Sp,xp)(""),zA=ct(yp,xp)(""),ob=R.forwardRef(function(r,i){const l=Bt({name:"MuiSelect",props:r}),{autoWidth:u=!1,children:c,classes:d={},className:p,defaultOpen:m=!1,displayEmpty:h=!1,IconComponent:y=tw,id:S,input:T,inputProps:E,label:x,labelId:C,MenuProps:D,multiple:k=!1,native:U=!1,onClose:M,onOpen:A,open:w,renderValue:j,SelectDisplayProps:H,variant:q="outlined",...Q}=l,v=U?pA:wA,_=Xo(),K=Yo({props:l,muiFormControl:_,states:["variant","error"]}),Z=K.variant||q,it={...l,variant:Z,classes:d},F=OA(it),{root:N,...V}=F,ot=T||{standard:O.jsx(AA,{ownerState:it}),outlined:O.jsx(MA,{label:x,ownerState:it}),filled:O.jsx(zA,{ownerState:it})}[Z],W=Pe(i,Vo(ot));return O.jsx(R.Fragment,{children:R.cloneElement(ot,{inputComponent:v,inputProps:{children:c,error:K.error,IconComponent:y,variant:Z,type:void 0,multiple:k,...U?{id:S}:{autoWidth:u,defaultOpen:m,displayEmpty:h,labelId:C,MenuProps:D,onClose:M,onOpen:A,open:w,renderValue:j,SelectDisplayProps:{id:S,...H}},...E,classes:E?nn(V,E.classes):V,...T?T.props.inputProps:{}},...(k&&U||h)&&Z==="outlined"?{notched:!0}:{},ref:W,className:gt(ot.props.className,p,F.root),...!T&&{variant:Z},...Q})})});ob.muiName="Select";function DA(n){return Dt("MuiTooltip",n)}const ze=Mt("MuiTooltip",["popper","popperInteractive","popperArrow","popperClose","tooltip","tooltipArrow","touch","tooltipPlacementLeft","tooltipPlacementRight","tooltipPlacementTop","tooltipPlacementBottom","arrow"]);function NA(n){return Math.round(n*1e5)/1e5}const BA=n=>{const{classes:r,disableInteractive:i,arrow:l,touch:u,placement:c}=n,d={popper:["popper",!i&&"popperInteractive",l&&"popperArrow"],tooltip:["tooltip",l&&"tooltipArrow",u&&"touch",`tooltipPlacement${st(c.split("-")[0])}`],arrow:["arrow"]};return Nt(d,DA,r)},kA=ct(K0,{name:"MuiTooltip",slot:"Popper",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.popper,!i.disableInteractive&&r.popperInteractive,i.arrow&&r.popperArrow,!i.open&&r.popperClose]}})(Gt(({theme:n})=>({zIndex:(n.vars||n).zIndex.tooltip,pointerEvents:"none",variants:[{props:({ownerState:r})=>!r.disableInteractive,style:{pointerEvents:"auto"}},{props:({open:r})=>!r,style:{pointerEvents:"none"}},{props:({ownerState:r})=>r.arrow,style:{[`&[data-popper-placement*="bottom"] .${ze.arrow}`]:{top:0,marginTop:"-0.71em","&::before":{transformOrigin:"0 100%"}},[`&[data-popper-placement*="top"] .${ze.arrow}`]:{bottom:0,marginBottom:"-0.71em","&::before":{transformOrigin:"100% 0"}},[`&[data-popper-placement*="right"] .${ze.arrow}`]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"100% 100%"}},[`&[data-popper-placement*="left"] .${ze.arrow}`]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"0 0"}}}},{props:({ownerState:r})=>r.arrow&&!r.isRtl,style:{[`&[data-popper-placement*="right"] .${ze.arrow}`]:{left:0,marginLeft:"-0.71em"}}},{props:({ownerState:r})=>r.arrow&&!!r.isRtl,style:{[`&[data-popper-placement*="right"] .${ze.arrow}`]:{right:0,marginRight:"-0.71em"}}},{props:({ownerState:r})=>r.arrow&&!r.isRtl,style:{[`&[data-popper-placement*="left"] .${ze.arrow}`]:{right:0,marginRight:"-0.71em"}}},{props:({ownerState:r})=>r.arrow&&!!r.isRtl,style:{[`&[data-popper-placement*="left"] .${ze.arrow}`]:{left:0,marginLeft:"-0.71em"}}}]}))),$A=ct("div",{name:"MuiTooltip",slot:"Tooltip",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.tooltip,i.touch&&r.touch,i.arrow&&r.tooltipArrow,r[`tooltipPlacement${st(i.placement.split("-")[0])}`]]}})(Gt(({theme:n})=>({backgroundColor:n.vars?n.vars.palette.Tooltip.bg:ve(n.palette.grey[700],.92),borderRadius:(n.vars||n).shape.borderRadius,color:(n.vars||n).palette.common.white,fontFamily:n.typography.fontFamily,padding:"4px 8px",fontSize:n.typography.pxToRem(11),maxWidth:300,margin:2,wordWrap:"break-word",fontWeight:n.typography.fontWeightMedium,[`.${ze.popper}[data-popper-placement*="left"] &`]:{transformOrigin:"right center"},[`.${ze.popper}[data-popper-placement*="right"] &`]:{transformOrigin:"left center"},[`.${ze.popper}[data-popper-placement*="top"] &`]:{transformOrigin:"center bottom",marginBottom:"14px"},[`.${ze.popper}[data-popper-placement*="bottom"] &`]:{transformOrigin:"center top",marginTop:"14px"},variants:[{props:({ownerState:r})=>r.arrow,style:{position:"relative",margin:0}},{props:({ownerState:r})=>r.touch,style:{padding:"8px 16px",fontSize:n.typography.pxToRem(14),lineHeight:`${NA(16/14)}em`,fontWeight:n.typography.fontWeightRegular}},{props:({ownerState:r})=>!r.isRtl,style:{[`.${ze.popper}[data-popper-placement*="left"] &`]:{marginRight:"14px"},[`.${ze.popper}[data-popper-placement*="right"] &`]:{marginLeft:"14px"}}},{props:({ownerState:r})=>!r.isRtl&&r.touch,style:{[`.${ze.popper}[data-popper-placement*="left"] &`]:{marginRight:"24px"},[`.${ze.popper}[data-popper-placement*="right"] &`]:{marginLeft:"24px"}}},{props:({ownerState:r})=>!!r.isRtl,style:{[`.${ze.popper}[data-popper-placement*="left"] &`]:{marginLeft:"14px"},[`.${ze.popper}[data-popper-placement*="right"] &`]:{marginRight:"14px"}}},{props:({ownerState:r})=>!!r.isRtl&&r.touch,style:{[`.${ze.popper}[data-popper-placement*="left"] &`]:{marginLeft:"24px"},[`.${ze.popper}[data-popper-placement*="right"] &`]:{marginRight:"24px"}}},{props:({ownerState:r})=>r.touch,style:{[`.${ze.popper}[data-popper-placement*="top"] &`]:{marginBottom:"24px"}}},{props:({ownerState:r})=>r.touch,style:{[`.${ze.popper}[data-popper-placement*="bottom"] &`]:{marginTop:"24px"}}}]}))),jA=ct("span",{name:"MuiTooltip",slot:"Arrow"})(Gt(({theme:n})=>({overflow:"hidden",position:"absolute",width:"1em",height:"0.71em",boxSizing:"border-box",color:n.vars?n.vars.palette.Tooltip.bg:ve(n.palette.grey[700],.9),"&::before":{content:'""',margin:"auto",display:"block",width:"100%",height:"100%",backgroundColor:"currentColor",transform:"rotate(45deg)"}})));let Fs=!1;const $v=new Du;let Ji={x:0,y:0};function Js(n,r){return(i,...l)=>{r&&r(i,...l),n(i,...l)}}const fd=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiTooltip"}),{arrow:u=!1,children:c,classes:d,components:p={},componentsProps:m={},describeChild:h=!1,disableFocusListener:y=!1,disableHoverListener:S=!1,disableInteractive:T=!1,disableTouchListener:E=!1,enterDelay:x=100,enterNextDelay:C=0,enterTouchDelay:D=700,followCursor:k=!1,id:U,leaveDelay:M=0,leaveTouchDelay:A=1500,onClose:w,onOpen:j,open:H,placement:q="bottom",PopperComponent:Q,PopperProps:v={},slotProps:_={},slots:K={},title:Z,TransitionComponent:it,TransitionProps:F,...N}=l,V=R.isValidElement(c)?c:O.jsx("span",{children:c}),ot=qo(),W=tp(),[z,X]=R.useState(),[et,nt]=R.useState(null),lt=R.useRef(!1),ut=T||k,ft=Mo(),Tt=Mo(),bt=Mo(),Ot=Mo(),[yt,wt]=Cd({controlled:H,default:!1,name:"Tooltip",state:"open"});let St=yt;const $t=Go(U),Et=R.useRef(),qt=Aa(()=>{Et.current!==void 0&&(document.body.style.WebkitUserSelect=Et.current,Et.current=void 0),Ot.clear()});R.useEffect(()=>qt,[qt]);const Ce=At=>{$v.clear(),Fs=!0,wt(!0),j&&!St&&j(At)},Ut=Aa(At=>{$v.start(800+M,()=>{Fs=!1}),wt(!1),w&&St&&w(At),ft.start(ot.transitions.duration.shortest,()=>{lt.current=!1})}),Kt=At=>{lt.current&&At.type!=="touchstart"||(z&&z.removeAttribute("title"),Tt.clear(),bt.clear(),x||Fs&&C?Tt.start(Fs?C:x,()=>{Ce(At)}):Ce(At))},Qt=At=>{Tt.clear(),bt.start(M,()=>{Ut(At)})},[,Vt]=R.useState(!1),jt=At=>{pu(At.target)||(Vt(!1),Qt(At))},dt=At=>{z||X(At.currentTarget),pu(At.target)&&(Vt(!0),Kt(At))},_e=At=>{lt.current=!0;const on=V.props;on.onTouchStart&&on.onTouchStart(At)},ie=At=>{_e(At),bt.clear(),ft.clear(),qt(),Et.current=document.body.style.WebkitUserSelect,document.body.style.WebkitUserSelect="none",Ot.start(D,()=>{document.body.style.WebkitUserSelect=Et.current,Kt(At)})},Ze=At=>{V.props.onTouchEnd&&V.props.onTouchEnd(At),qt(),bt.start(A,()=>{Ut(At)})};R.useEffect(()=>{if(!St)return;function At(on){on.key==="Escape"&&Ut(on)}return document.addEventListener("keydown",At),()=>{document.removeEventListener("keydown",At)}},[Ut,St]);const Ue=Pe(Vo(V),X,i);!Z&&Z!==0&&(St=!1);const ee=R.useRef(),Te=At=>{const on=V.props;on.onMouseMove&&on.onMouseMove(At),Ji={x:At.clientX,y:At.clientY},ee.current&&ee.current.update()},ne={},le=typeof Z=="string";h?(ne.title=!St&&le&&!S?Z:null,ne["aria-describedby"]=St?$t:null):(ne["aria-label"]=le?Z:null,ne["aria-labelledby"]=St&&!le?$t:null);const mt={...ne,...N,...V.props,className:gt(N.className,V.props.className),onTouchStart:_e,ref:Ue,...k?{onMouseMove:Te}:{}},Re={};E||(mt.onTouchStart=ie,mt.onTouchEnd=Ze),S||(mt.onMouseOver=Js(Kt,mt.onMouseOver),mt.onMouseLeave=Js(Qt,mt.onMouseLeave),ut||(Re.onMouseOver=Kt,Re.onMouseLeave=Qt)),y||(mt.onFocus=Js(dt,mt.onFocus),mt.onBlur=Js(jt,mt.onBlur),ut||(Re.onFocus=dt,Re.onBlur=jt));const ae={...l,isRtl:W,arrow:u,disableInteractive:ut,placement:q,PopperComponentProp:Q,touch:lt.current},Wt=typeof _.popper=="function"?_.popper(ae):_.popper,ht=R.useMemo(()=>{var on,Nl;let At=[{name:"arrow",enabled:!!et,options:{element:et,padding:4}}];return(on=v.popperOptions)!=null&&on.modifiers&&(At=At.concat(v.popperOptions.modifiers)),(Nl=Wt==null?void 0:Wt.popperOptions)!=null&&Nl.modifiers&&(At=At.concat(Wt.popperOptions.modifiers)),{...v.popperOptions,...Wt==null?void 0:Wt.popperOptions,modifiers:At}},[et,v.popperOptions,Wt==null?void 0:Wt.popperOptions]),Yt=BA(ae),be=typeof _.transition=="function"?_.transition(ae):_.transition,hn={slots:{popper:p.Popper,transition:p.Transition??it,tooltip:p.Tooltip,arrow:p.Arrow,...K},slotProps:{arrow:_.arrow??m.arrow,popper:{...v,...Wt??m.popper},tooltip:_.tooltip??m.tooltip,transition:{...F,...be??m.transition}}},[ir,Ml]=de("popper",{elementType:kA,externalForwardedProps:hn,ownerState:ae,className:gt(Yt.popper,v==null?void 0:v.className)}),[Io,lr]=de("transition",{elementType:yu,externalForwardedProps:hn,ownerState:ae}),[_u,zl]=de("tooltip",{elementType:$A,className:Yt.tooltip,externalForwardedProps:hn,ownerState:ae}),[Dl,Ko]=de("arrow",{elementType:jA,className:Yt.arrow,externalForwardedProps:hn,ownerState:ae,ref:nt});return O.jsxs(R.Fragment,{children:[R.cloneElement(V,mt),O.jsx(ir,{as:Q??K0,placement:q,anchorEl:k?{getBoundingClientRect:()=>({top:Ji.y,left:Ji.x,right:Ji.x,bottom:Ji.y,width:0,height:0})}:z,popperRef:ee,open:z?St:!1,id:$t,transition:!0,...Re,...Ml,popperOptions:ht,children:({TransitionProps:At})=>O.jsx(Io,{timeout:ot.transitions.duration.shorter,...At,...lr,children:O.jsxs(_u,{...zl,children:[Z,u?O.jsx(Dl,{...Ko}):null]})})})]})});function _A(n){return Dt("MuiToolbar",n)}Mt("MuiToolbar",["root","gutters","regular","dense"]);const UA=n=>{const{classes:r,disableGutters:i,variant:l}=n;return Nt({root:["root",!i&&"gutters",l]},_A,r)},LA=ct("div",{name:"MuiToolbar",slot:"Root",overridesResolver:(n,r)=>{const{ownerState:i}=n;return[r.root,!i.disableGutters&&r.gutters,r[i.variant]]}})(Gt(({theme:n})=>({position:"relative",display:"flex",alignItems:"center",variants:[{props:({ownerState:r})=>!r.disableGutters,style:{paddingLeft:n.spacing(2),paddingRight:n.spacing(2),[n.breakpoints.up("sm")]:{paddingLeft:n.spacing(3),paddingRight:n.spacing(3)}}},{props:{variant:"dense"},style:{minHeight:48}},{props:{variant:"regular"},style:n.mixins.toolbar}]}))),HA=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiToolbar"}),{className:u,component:c="div",disableGutters:d=!1,variant:p="regular",...m}=l,h={...l,component:c,disableGutters:d,variant:p},y=UA(h);return O.jsx(LA,{as:c,className:gt(y.root,u),ref:i,ownerState:h,...m})});function PA(n){return Dt("MuiTextField",n)}Mt("MuiTextField",["root"]);const qA={standard:vp,filled:yp,outlined:Sp},GA=n=>{const{classes:r}=n;return Nt({root:["root"]},PA,r)},VA=ct(RO,{name:"MuiTextField",slot:"Root"})({}),YA=R.forwardRef(function(r,i){const l=Bt({props:r,name:"MuiTextField"}),{autoComplete:u,autoFocus:c=!1,children:d,className:p,color:m="primary",defaultValue:h,disabled:y=!1,error:S=!1,FormHelperTextProps:T,fullWidth:E=!1,helperText:x,id:C,InputLabelProps:D,inputProps:k,InputProps:U,inputRef:M,label:A,maxRows:w,minRows:j,multiline:H=!1,name:q,onBlur:Q,onChange:v,onFocus:_,placeholder:K,required:Z=!1,rows:it,select:F=!1,SelectProps:N,slots:V={},slotProps:ot={},type:W,value:z,variant:X="outlined",...et}=l,nt={...l,autoFocus:c,color:m,disabled:y,error:S,fullWidth:E,multiline:H,required:Z,select:F,variant:X},lt=GA(nt),ut=Go(C),ft=x&&ut?`${ut}-helper-text`:void 0,Tt=A&&ut?`${ut}-label`:void 0,bt=qA[X],Ot={slots:V,slotProps:{input:U,inputLabel:D,htmlInput:k,formHelperText:T,select:N,...ot}},yt={},wt=Ot.slotProps.inputLabel;X==="outlined"&&(wt&&typeof wt.shrink<"u"&&(yt.notched=wt.shrink),yt.label=A),F&&((!N||!N.native)&&(yt.id=void 0),yt["aria-describedby"]=void 0);const[St,$t]=de("root",{elementType:VA,shouldForwardComponentProp:!0,externalForwardedProps:{...Ot,...et},ownerState:nt,className:gt(lt.root,p),ref:i,additionalProps:{disabled:y,error:S,fullWidth:E,required:Z,color:m,variant:X}}),[Et,qt]=de("input",{elementType:bt,externalForwardedProps:Ot,additionalProps:yt,ownerState:nt}),[Ce,Ut]=de("inputLabel",{elementType:qO,externalForwardedProps:Ot,ownerState:nt}),[Kt,Qt]=de("htmlInput",{elementType:"input",externalForwardedProps:Ot,ownerState:nt}),[Vt,jt]=de("formHelperText",{elementType:MO,externalForwardedProps:Ot,ownerState:nt}),[dt,_e]=de("select",{elementType:ob,externalForwardedProps:Ot,ownerState:nt}),ie=O.jsx(Et,{"aria-describedby":ft,autoComplete:u,autoFocus:c,defaultValue:h,fullWidth:E,multiline:H,name:q,rows:it,maxRows:w,minRows:j,type:W,value:z,id:ut,inputRef:M,onBlur:Q,onChange:v,onFocus:_,placeholder:K,inputProps:Qt,slots:{input:V.htmlInput?Kt:void 0},...qt});return O.jsxs(St,{...$t,children:[A!=null&&A!==""&&O.jsx(Ce,{htmlFor:ut,id:Tt,...Ut,children:A}),F?O.jsx(dt,{"aria-describedby":ft,id:ut,labelId:Tt,value:z,input:ie,..._e,children:d}):ie,x&&O.jsx(Vt,{id:ft,...jt,children:x})]})}),XA=Gn(O.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 4c1.93 0 3.5 1.57 3.5 3.5S13.93 13 12 13s-3.5-1.57-3.5-3.5S10.07 6 12 6m0 14c-2.03 0-4.43-.82-6.14-2.88C7.55 15.8 9.68 15 12 15s4.45.8 6.14 2.12C16.43 19.18 14.03 20 12 20"})),ib=Gn(O.jsx("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"})),IA=Gn(O.jsx("path",{d:"M3 13h8V3H3zm0 8h8v-6H3zm10 0h8V11h-8zm0-18v6h8V3z"})),KA=Gn(O.jsx("path",{d:"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"})),QA=Gn(O.jsx("path",{d:"M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2m0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2m0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2"})),WA=Gn(O.jsx("path",{d:"M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2m6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1z"})),ZA=Gn(O.jsx("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4m0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4"})),jv=Gn(O.jsx("path",{d:"M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6"})),FA=Gn(O.jsx("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2M9 17H7V7h2zm4-5h-2V7h2zm4 3h-2V7h2z"})),JA=({currentView:n,onViewChange:r})=>{const[i,l]=R.useState(null),[u,c]=R.useState(null),d=T=>{l(T.currentTarget)},p=()=>{l(null)},m=T=>{c(T.currentTarget)},h=()=>{c(null)},y=!!i,S=!!u;return O.jsx(EE,{position:"static",elevation:1,children:O.jsxs(HA,{children:[O.jsx(IA,{sx:{mr:2}}),O.jsx(ye,{variant:"h6",component:"div",sx:{flexGrow:1},children:"Trello Clone"}),O.jsxs(Qe,{sx:{display:"flex",gap:1,mr:2},children:[O.jsx(Br,{color:"inherit",startIcon:O.jsx(KA,{}),onClick:()=>r("home"),variant:n==="home"?"outlined":"text",sx:{borderColor:n==="home"?"rgba(255,255,255,0.5)":"transparent","&:hover":{backgroundColor:"rgba(255,255,255,0.1)"}},children:"Home"}),O.jsx(Br,{color:"inherit",startIcon:O.jsx(FA,{}),onClick:()=>r("board"),variant:n==="board"?"outlined":"text",sx:{borderColor:n==="board"?"rgba(255,255,255,0.5)":"transparent","&:hover":{backgroundColor:"rgba(255,255,255,0.1)"}},children:"Board"})]}),O.jsxs(Qe,{sx:{display:"flex",alignItems:"center",gap:1},children:[O.jsx(fd,{title:"Notifications",children:O.jsxs(ol,{color:"inherit",onClick:m,sx:{position:"relative"},children:[O.jsx(WA,{}),O.jsx(Qe,{sx:{position:"absolute",top:8,right:8,width:8,height:8,backgroundColor:"error.main",borderRadius:"50%"}})]})}),O.jsx(fd,{title:"Settings",children:O.jsx(ol,{color:"inherit",children:O.jsx(jv,{})})}),O.jsx(fd,{title:"Profile",children:O.jsx(ol,{onClick:d,color:"inherit",children:O.jsx(sw,{sx:{width:32,height:32,bgcolor:"secondary.main"},children:"JD"})})})]}),O.jsxs($d,{anchorEl:i,open:y,onClose:p,onClick:p,PaperProps:{elevation:3,sx:{mt:1.5,minWidth:200}},children:[O.jsxs(Oo,{children:[O.jsx(XA,{sx:{mr:2}}),"Profile"]}),O.jsxs(Oo,{children:[O.jsx(jv,{sx:{mr:2}}),"Settings"]}),O.jsx(Oo,{children:"Logout"})]}),O.jsxs($d,{anchorEl:u,open:S,onClose:h,onClick:h,PaperProps:{elevation:3,sx:{mt:1.5,minWidth:300}},children:[O.jsx(Oo,{children:O.jsxs(Qe,{children:[O.jsx(ye,{variant:"subtitle2",children:"New card added"}),O.jsx(ye,{variant:"body2",color:"text.secondary",children:'"Implement authentication" was added to In Progress'})]})}),O.jsx(Oo,{children:O.jsxs(Qe,{children:[O.jsx(ye,{variant:"subtitle2",children:"Card moved"}),O.jsx(ye,{variant:"body2",color:"text.secondary",children:'"Project setup" was moved to Done'})]})}),O.jsx(Oo,{children:O.jsxs(Qe,{children:[O.jsx(ye,{variant:"subtitle2",children:"Due date reminder"}),O.jsx(ye,{variant:"body2",color:"text.secondary",children:'"Design homepage" is due tomorrow'})]})})]})]})})},t4=()=>{const[n,r]=R.useState([{id:1,title:"To Do",cards:[{id:1,title:"Design homepage",description:"Create wireframes and mockups",labels:["Design","High Priority"]},{id:2,title:"Setup database",description:"Configure PostgreSQL",labels:["Backend"]}]},{id:2,title:"In Progress",cards:[{id:3,title:"Implement authentication",description:"Add login/signup functionality",labels:["Backend","Security"]}]},{id:3,title:"Done",cards:[{id:4,title:"Project setup",description:"Initialize React app with Vite",labels:["Setup"]}]}]),[i,l]=R.useState(!1),[u,c]=R.useState(""),[d,p]=R.useState(null),[m,h]=R.useState(null),y=E=>{p(E),l(!0)},S=()=>{if(u.trim()){const E={id:Date.now(),title:u,description:"",labels:[]};r(n.map(x=>x.id===d?{...x,cards:[...x.cards,E]}:x)),c(""),l(!1)}},T=E=>({Design:"primary",Backend:"secondary","High Priority":"error",Security:"warning",Setup:"success"})[E]||"default";return O.jsxs(Qe,{sx:{p:3},children:[O.jsx(ye,{variant:"h4",gutterBottom:!0,children:"Project Board"}),O.jsx(Qe,{sx:{display:"flex",gap:3,overflowX:"auto",pb:2},children:n.map(E=>O.jsxs(Qe,{sx:{minWidth:300,maxWidth:300,backgroundColor:"#f4f5f7",borderRadius:2,p:2},children:[O.jsxs(Qe,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[O.jsx(ye,{variant:"h6",fontWeight:"bold",children:E.title}),O.jsx(ol,{size:"small",children:O.jsx(QA,{})})]}),O.jsxs(Qe,{sx:{display:"flex",flexDirection:"column",gap:2},children:[E.cards.map(x=>O.jsx(Mr,{sx:{cursor:"pointer","&:hover":{boxShadow:3}},children:O.jsxs(zr,{sx:{p:2,"&:last-child":{pb:2}},children:[O.jsx(ye,{variant:"subtitle1",fontWeight:"medium",gutterBottom:!0,children:x.title}),x.description&&O.jsx(ye,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:x.description}),x.labels.length>0&&O.jsx(Qe,{sx:{display:"flex",flexWrap:"wrap",gap:.5,mt:1},children:x.labels.map((C,D)=>O.jsx(al,{label:C,size:"small",color:T(C),variant:"outlined"},D))}),O.jsx(Qe,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mt:2},children:O.jsx(Qe,{sx:{display:"flex",gap:.5},children:O.jsx(ol,{size:"small",children:O.jsx(ZA,{fontSize:"small"})})})})]})},x.id)),O.jsx(Br,{variant:"outlined",startIcon:O.jsx(ib,{}),onClick:()=>y(E.id),sx:{borderStyle:"dashed",color:"text.secondary",borderColor:"divider","&:hover":{borderStyle:"solid",borderColor:"primary.main",color:"primary.main"}},children:"Add a card"})]})]},E.id))}),O.jsxs(eO,{open:i,onClose:()=>l(!1),maxWidth:"sm",fullWidth:!0,children:[O.jsx(mO,{children:"Add New Card"}),O.jsx(fO,{children:O.jsx(YA,{autoFocus:!0,margin:"dense",label:"Card Title",fullWidth:!0,variant:"outlined",value:u,onChange:E=>c(E.target.value),onKeyPress:E=>{E.key==="Enter"&&S()}})}),O.jsxs(oO,{children:[O.jsx(Br,{onClick:()=>l(!1),children:"Cancel"}),O.jsx(Br,{onClick:S,variant:"contained",children:"Add Card"})]})]})]})};function e4(){const[n,r]=R.useState(0),[i,l]=R.useState("home"),u=d=>{l(d)},c=()=>{switch(i){case"board":return O.jsx(t4,{});case"home":default:return O.jsx(n4,{count:n,setCount:r})}};return O.jsxs(Qe,{sx:{flexGrow:1},children:[O.jsx(JA,{currentView:i,onViewChange:u}),c(),O.jsx(vO,{color:"primary","aria-label":"add",sx:{position:"fixed",bottom:16,right:16},children:O.jsx(ib,{})})]})}const n4=({count:n,setCount:r})=>O.jsx(Mw,{maxWidth:"lg",sx:{mt:4,mb:4},children:O.jsxs(Oa,{container:!0,spacing:3,children:[O.jsx(Oa,{item:!0,xs:12,md:8,children:O.jsx(Mr,{children:O.jsxs(zr,{children:[O.jsx(ye,{variant:"h4",component:"h1",gutterBottom:!0,children:"Welcome to Your Trello Clone!"}),O.jsx(ye,{variant:"body1",paragraph:!0,children:"This is a modern React application built with Vite and Material-UI. The setup includes a custom theme with Trello-inspired colors and styling."}),O.jsxs(Qe,{sx:{mt:2},children:[O.jsx(al,{label:"React 19",color:"primary",sx:{mr:1}}),O.jsx(al,{label:"Vite",color:"secondary",sx:{mr:1}}),O.jsx(al,{label:"Material-UI",color:"success",sx:{mr:1}}),O.jsx(al,{label:"Emotion",color:"info"})]})]})})}),O.jsx(Oa,{item:!0,xs:12,md:4,children:O.jsx(Mr,{children:O.jsxs(zr,{children:[O.jsx(ye,{variant:"h5",component:"h2",gutterBottom:!0,children:"Interactive Demo"}),O.jsx(ye,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Test the reactivity with this counter:"}),O.jsxs(Qe,{sx:{textAlign:"center",mt:2},children:[O.jsx(ye,{variant:"h3",color:"primary",gutterBottom:!0,children:n}),O.jsx(Br,{variant:"contained",onClick:()=>r(i=>i+1),sx:{mr:1},children:"Increment"}),O.jsx(Br,{variant:"outlined",onClick:()=>r(0),children:"Reset"})]})]})})}),O.jsxs(Oa,{item:!0,xs:12,children:[O.jsx(ye,{variant:"h5",component:"h2",gutterBottom:!0,sx:{mt:2},children:"Features Included"}),O.jsxs(Oa,{container:!0,spacing:2,children:[O.jsx(Oa,{item:!0,xs:12,sm:6,md:3,children:O.jsx(Mr,{sx:{height:"100%"},children:O.jsxs(zr,{children:[O.jsx(ye,{variant:"h6",gutterBottom:!0,children:"🎨 Custom Theme"}),O.jsx(ye,{variant:"body2",children:"Trello-inspired color palette and typography"})]})})}),O.jsx(Oa,{item:!0,xs:12,sm:6,md:3,children:O.jsx(Mr,{sx:{height:"100%"},children:O.jsxs(zr,{children:[O.jsx(ye,{variant:"h6",gutterBottom:!0,children:"⚡ Vite Build Tool"}),O.jsx(ye,{variant:"body2",children:"Lightning fast development and build process"})]})})}),O.jsx(Oa,{item:!0,xs:12,sm:6,md:3,children:O.jsx(Mr,{sx:{height:"100%"},children:O.jsxs(zr,{children:[O.jsx(ye,{variant:"h6",gutterBottom:!0,children:"🧩 MUI Components"}),O.jsx(ye,{variant:"body2",children:"Complete Material-UI component library"})]})})}),O.jsx(Oa,{item:!0,xs:12,sm:6,md:3,children:O.jsx(Mr,{sx:{height:"100%"},children:O.jsxs(zr,{children:[O.jsx(ye,{variant:"h6",gutterBottom:!0,children:"🔧 Ready to Build"}),O.jsx(ye,{variant:"body2",children:"Perfect foundation for your Trello clone"})]})})})]})]})]})});AS.createRoot(document.getElementById("root")).render(O.jsx(R.StrictMode,{children:O.jsxs(mT,{theme:CT,children:[O.jsx(xT,{}),O.jsx(e4,{})]})}));
